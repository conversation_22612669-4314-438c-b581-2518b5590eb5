package com.ultimatedungeon.udx.instance;

import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.entity.Projectile;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.block.BlockExplodeEvent;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityExplodeEvent;
import org.bukkit.event.entity.EntitySpawnEvent;
import org.bukkit.event.inventory.InventoryMoveItemEvent;
import org.bukkit.event.player.PlayerBucketEmptyEvent;
import org.bukkit.event.player.PlayerBucketFillEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerTeleportEvent;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.NotNull;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Handles protection and anti-grief measures for dungeon instances.
 * 
 * <p>This service prevents unwanted interactions within instance worlds,
 * including block modification, explosions, and unauthorized entity spawning.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class ProtectionService implements Listener {
    
    private final Plugin plugin;
    private final InstanceManager instanceManager;
    private final Set<String> protectedWorlds;
    
    // Configuration flags
    private boolean allowBlockBreaking = false;
    private boolean allowBlockPlacing = false;
    private boolean allowExplosions = false;
    private boolean allowFluidFlow = false;
    private boolean allowHopperTransfer = false;
    private boolean allowPvP = false;
    private boolean allowEnderPearls = false;
    private boolean allowOutsideEntitySpawning = false;
    
    public ProtectionService(@NotNull Plugin plugin, @NotNull InstanceManager instanceManager) {
        this.plugin = plugin;
        this.instanceManager = instanceManager;
        this.protectedWorlds = ConcurrentHashMap.newKeySet();
    }
    
    /**
     * Adds a world to protection.
     * 
     * @param worldName the world name to protect
     */
    public void addProtectedWorld(@NotNull String worldName) {
        protectedWorlds.add(worldName);
        plugin.getLogger().info("Added world protection: " + worldName);
    }
    
    /**
     * Removes a world from protection.
     * 
     * @param worldName the world name to unprotect
     */
    public void removeProtectedWorld(@NotNull String worldName) {
        protectedWorlds.remove(worldName);
        plugin.getLogger().info("Removed world protection: " + worldName);
    }
    
    /**
     * Checks if a world is protected.
     * 
     * @param world the world to check
     * @return true if protected
     */
    public boolean isProtected(@NotNull World world) {
        return protectedWorlds.contains(world.getName());
    }
    
    /**
     * Checks if a player is authorized to perform actions in an instance.
     * 
     * @param player the player to check
     * @param world the world to check
     * @return true if authorized
     */
    private boolean isAuthorized(@NotNull Player player, @NotNull World world) {
        if (!isProtected(world)) {
            return true;
        }
        
        // Check if player has admin permissions
        if (player.hasPermission("udx.admin.bypass")) {
            return true;
        }
        
        // Check if player is in an active instance in this world
        DungeonInstance instance = instanceManager.getInstanceByWorld(world);
        if (instance != null) {
            return instance.getPlayerIds().contains(player.getUniqueId());
        }
        
        return false;
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onBlockBreak(@NotNull BlockBreakEvent event) {
        if (!isProtected(event.getBlock().getWorld())) {
            return;
        }
        
        Player player = event.getPlayer();
        
        // Allow if player is authorized and block breaking is enabled
        if (allowBlockBreaking && isAuthorized(player, event.getBlock().getWorld())) {
            return;
        }
        
        // Cancel the event
        event.setCancelled(true);
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onBlockPlace(@NotNull BlockPlaceEvent event) {
        if (!isProtected(event.getBlock().getWorld())) {
            return;
        }
        
        Player player = event.getPlayer();
        
        // Allow if player is authorized and block placing is enabled
        if (allowBlockPlacing && isAuthorized(player, event.getBlock().getWorld())) {
            return;
        }
        
        // Cancel the event
        event.setCancelled(true);
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerInteract(@NotNull PlayerInteractEvent event) {
        if (event.getClickedBlock() == null || !isProtected(event.getClickedBlock().getWorld())) {
            return;
        }
        
        Player player = event.getPlayer();
        Material blockType = event.getClickedBlock().getType();
        
        // Allow interaction with certain blocks (doors, buttons, levers, chests)
        if (isInteractableBlock(blockType)) {
            if (isAuthorized(player, event.getClickedBlock().getWorld())) {
                return;
            }
        }
        
        // Cancel unauthorized interactions
        if (!isAuthorized(player, event.getClickedBlock().getWorld())) {
            event.setCancelled(true);
        }
    }
    
    /**
     * Checks if a block type is interactable (allowed to be used).
     */
    private boolean isInteractableBlock(@NotNull Material material) {
        return switch (material) {
            case CHEST, TRAPPED_CHEST, ENDER_CHEST,
                 BARREL, SHULKER_BOX,
                 OAK_DOOR, SPRUCE_DOOR, BIRCH_DOOR, JUNGLE_DOOR, ACACIA_DOOR, DARK_OAK_DOOR, MANGROVE_DOOR, CHERRY_DOOR, BAMBOO_DOOR, CRIMSON_DOOR, WARPED_DOOR,
                 IRON_DOOR,
                 OAK_TRAPDOOR, SPRUCE_TRAPDOOR, BIRCH_TRAPDOOR, JUNGLE_TRAPDOOR, ACACIA_TRAPDOOR, DARK_OAK_TRAPDOOR, MANGROVE_TRAPDOOR, CHERRY_TRAPDOOR, BAMBOO_TRAPDOOR, CRIMSON_TRAPDOOR, WARPED_TRAPDOOR,
                 IRON_TRAPDOOR,
                 LEVER, STONE_BUTTON, OAK_BUTTON, SPRUCE_BUTTON, BIRCH_BUTTON, JUNGLE_BUTTON, ACACIA_BUTTON, DARK_OAK_BUTTON, MANGROVE_BUTTON, CHERRY_BUTTON, BAMBOO_BUTTON, CRIMSON_BUTTON, WARPED_BUTTON,
                 STONE_PRESSURE_PLATE, OAK_PRESSURE_PLATE, SPRUCE_PRESSURE_PLATE, BIRCH_PRESSURE_PLATE, JUNGLE_PRESSURE_PLATE, ACACIA_PRESSURE_PLATE, DARK_OAK_PRESSURE_PLATE, MANGROVE_PRESSURE_PLATE, CHERRY_PRESSURE_PLATE, BAMBOO_PRESSURE_PLATE, CRIMSON_PRESSURE_PLATE, WARPED_PRESSURE_PLATE,
                 HEAVY_WEIGHTED_PRESSURE_PLATE, LIGHT_WEIGHTED_PRESSURE_PLATE -> true;
            default -> false;
        };
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onBucketEmpty(@NotNull PlayerBucketEmptyEvent event) {
        if (!isProtected(event.getBlock().getWorld())) {
            return;
        }
        
        if (!allowFluidFlow || !isAuthorized(event.getPlayer(), event.getBlock().getWorld())) {
            event.setCancelled(true);
        }
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onBucketFill(@NotNull PlayerBucketFillEvent event) {
        if (!isProtected(event.getBlock().getWorld())) {
            return;
        }
        
        if (!allowFluidFlow || !isAuthorized(event.getPlayer(), event.getBlock().getWorld())) {
            event.setCancelled(true);
        }
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityExplode(@NotNull EntityExplodeEvent event) {
        if (!isProtected(event.getLocation().getWorld())) {
            return;
        }
        
        if (!allowExplosions) {
            event.setCancelled(true);
        }
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onBlockExplode(@NotNull BlockExplodeEvent event) {
        if (!isProtected(event.getBlock().getWorld())) {
            return;
        }
        
        if (!allowExplosions) {
            event.setCancelled(true);
        }
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onInventoryMoveItem(@NotNull InventoryMoveItemEvent event) {
        if (event.getSource().getLocation() == null || !isProtected(event.getSource().getLocation().getWorld())) {
            return;
        }
        
        if (!allowHopperTransfer) {
            event.setCancelled(true);
        }
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityDamageByEntity(@NotNull EntityDamageByEntityEvent event) {
        if (!isProtected(event.getEntity().getWorld())) {
            return;
        }
        
        // Handle PvP
        if (event.getEntity() instanceof Player victim && event.getDamager() instanceof Player attacker) {
            if (!allowPvP) {
                event.setCancelled(true);
                return;
            }
            
            // Check if both players are in the same instance
            DungeonInstance instance = instanceManager.getInstanceByWorld(victim.getWorld());
            if (instance != null) {
                if (!instance.getPlayerIds().contains(victim.getUniqueId()) || 
                    !instance.getPlayerIds().contains(attacker.getUniqueId())) {
                    event.setCancelled(true);
                }
            }
        }
        
        // Handle projectile PvP
        if (event.getEntity() instanceof Player victim && event.getDamager() instanceof Projectile projectile) {
            if (projectile.getShooter() instanceof Player attacker) {
                if (!allowPvP) {
                    event.setCancelled(true);
                    return;
                }
                
                // Check if both players are in the same instance
                DungeonInstance instance = instanceManager.getInstanceByWorld(victim.getWorld());
                if (instance != null) {
                    if (!instance.getPlayerIds().contains(victim.getUniqueId()) || 
                        !instance.getPlayerIds().contains(attacker.getUniqueId())) {
                        event.setCancelled(true);
                    }
                }
            }
        }
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onEntitySpawn(@NotNull EntitySpawnEvent event) {
        if (!isProtected(event.getLocation().getWorld())) {
            return;
        }
        
        Entity entity = event.getEntity();
        
        // Allow dungeon-related entities (spawned by the plugin)
        if (entity.hasMetadata("udx.spawned")) {
            return;
        }
        
        // Block outside entity spawning if disabled
        if (!allowOutsideEntitySpawning) {
            // Allow certain passive entities
            if (isAllowedEntityType(entity.getType())) {
                return;
            }
            
            event.setCancelled(true);
        }
    }
    
    /**
     * Checks if an entity type is allowed to spawn naturally.
     */
    private boolean isAllowedEntityType(@NotNull EntityType type) {
        return switch (type) {
            case ITEM, EXPERIENCE_ORB, ARROW, TRIDENT, FIREWORK_ROCKET -> true;
            default -> false;
        };
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerTeleport(@NotNull PlayerTeleportEvent event) {
        if (!isProtected(event.getTo().getWorld())) {
            return;
        }
        
        // Block ender pearl teleportation if disabled
        if (event.getCause() == PlayerTeleportEvent.TeleportCause.ENDER_PEARL && !allowEnderPearls) {
            event.setCancelled(true);
        }
        
        // Block unauthorized teleportation into protected worlds
        if (!isAuthorized(event.getPlayer(), event.getTo().getWorld())) {
            // Allow if teleporting from the same world (internal movement)
            if (event.getFrom().getWorld().equals(event.getTo().getWorld())) {
                return;
            }
            
            event.setCancelled(true);
        }
    }
    
    // Configuration getters and setters
    
    public boolean isAllowBlockBreaking() {
        return allowBlockBreaking;
    }
    
    public void setAllowBlockBreaking(boolean allowBlockBreaking) {
        this.allowBlockBreaking = allowBlockBreaking;
    }
    
    public boolean isAllowBlockPlacing() {
        return allowBlockPlacing;
    }
    
    public void setAllowBlockPlacing(boolean allowBlockPlacing) {
        this.allowBlockPlacing = allowBlockPlacing;
    }
    
    public boolean isAllowExplosions() {
        return allowExplosions;
    }
    
    public void setAllowExplosions(boolean allowExplosions) {
        this.allowExplosions = allowExplosions;
    }
    
    public boolean isAllowFluidFlow() {
        return allowFluidFlow;
    }
    
    public void setAllowFluidFlow(boolean allowFluidFlow) {
        this.allowFluidFlow = allowFluidFlow;
    }
    
    public boolean isAllowHopperTransfer() {
        return allowHopperTransfer;
    }
    
    public void setAllowHopperTransfer(boolean allowHopperTransfer) {
        this.allowHopperTransfer = allowHopperTransfer;
    }
    
    public boolean isAllowPvP() {
        return allowPvP;
    }
    
    public void setAllowPvP(boolean allowPvP) {
        this.allowPvP = allowPvP;
    }
    
    public boolean isAllowEnderPearls() {
        return allowEnderPearls;
    }
    
    public void setAllowEnderPearls(boolean allowEnderPearls) {
        this.allowEnderPearls = allowEnderPearls;
    }
    
    public boolean isAllowOutsideEntitySpawning() {
        return allowOutsideEntitySpawning;
    }
    
    public void setAllowOutsideEntitySpawning(boolean allowOutsideEntitySpawning) {
        this.allowOutsideEntitySpawning = allowOutsideEntitySpawning;
    }
}

package com.ultimatedungeon.udx.spawner.behavior;

import org.bukkit.Location;
import org.bukkit.attribute.Attribute;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Mob;
import org.bukkit.util.Vector;
import org.jetbrains.annotations.NotNull;

import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Collection of basic mob behaviors.
 * 
 * <p>Provides common AI behaviors that can be applied to mobs
 * for various movement and combat patterns.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class BasicBehaviors {
    
    private BasicBehaviors() {
        // Utility class
    }
    
    /**
     * Roaming behavior - makes mobs wander around randomly.
     */
    public static final class RoamBehavior implements MobBehavior {
        
        private static final String ID = "roam";
        private static final int PRIORITY = 1;
        
        @Override
        @NotNull
        public String getId() {
            return ID;
        }
        
        @Override
        public int getPriority() {
            return PRIORITY;
        }
        
        @Override
        public boolean canApply(@NotNull LivingEntity entity, @NotNull Map<String, Object> parameters) {
            return entity instanceof Mob; // Only apply to mobs
        }
        
        @Override
        public boolean isActive(@NotNull LivingEntity entity) {
            if (!(entity instanceof Mob mob)) return false;
            return mob.getTarget() == null; // Only roam when not targeting
        }
        
        @Override
        public void apply(@NotNull LivingEntity entity, @NotNull Map<String, Object> parameters) {
            // Store roam center
            entity.setMetadata("udx_roam_center", new org.bukkit.metadata.FixedMetadataValue(
                org.bukkit.Bukkit.getPluginManager().getPlugin("UltimateDungeonX"), 
                entity.getLocation().clone()
            ));
        }
        
        @Override
        public void update(@NotNull LivingEntity entity, @NotNull Map<String, Object> parameters) {
            if (!(entity instanceof Mob mob)) return;
            
            // Get roam parameters
            double radius = getParameter(parameters, "radius", 8.0);
            
            // Get roam center
            Location center = entity.getLocation();
            if (entity.hasMetadata("udx_roam_center")) {
                center = (Location) entity.getMetadata("udx_roam_center").get(0).value();
            }
            
            // Random chance to start roaming
            if (ThreadLocalRandom.current().nextDouble() < 0.1) { // 10% chance per update
                Location target = generateRandomLocation(center, radius);
                mob.getPathfinder().moveTo(target);
            }
        }
        
        @Override
        public void remove(@NotNull LivingEntity entity) {
            if (entity instanceof Mob mob) {
                mob.getPathfinder().stopPathfinding();
            }
        }
        
        private Location generateRandomLocation(Location center, double radius) {
            double angle = ThreadLocalRandom.current().nextDouble() * 2 * Math.PI;
            double distance = ThreadLocalRandom.current().nextDouble() * radius;
            
            double x = center.getX() + Math.cos(angle) * distance;
            double z = center.getZ() + Math.sin(angle) * distance;
            
            return new Location(center.getWorld(), x, center.getY(), z);
        }
    }
    
    /**
     * Guard behavior - makes mobs return to a guard point.
     */
    public static final class GuardBehavior implements MobBehavior {
        
        private static final String ID = "guard";
        private static final int PRIORITY = 2;
        
        @Override
        @NotNull
        public String getId() {
            return ID;
        }
        
        @Override
        public int getPriority() {
            return PRIORITY;
        }
        
        @Override
        public boolean canApply(@NotNull LivingEntity entity, @NotNull Map<String, Object> parameters) {
            return entity instanceof Mob;
        }
        
        @Override
        public boolean isActive(@NotNull LivingEntity entity) {
            return true; // Always active
        }
        
        @Override
        public void apply(@NotNull LivingEntity entity, @NotNull Map<String, Object> parameters) {
            // Store guard point
            entity.setMetadata("udx_guard_point", new org.bukkit.metadata.FixedMetadataValue(
                org.bukkit.Bukkit.getPluginManager().getPlugin("UltimateDungeonX"), 
                entity.getLocation().clone()
            ));
        }
        
        @Override
        public void update(@NotNull LivingEntity entity, @NotNull Map<String, Object> parameters) {
            if (!(entity instanceof Mob mob)) return;
            
            // Get guard parameters
            double maxDistance = getParameter(parameters, "maxDistance", 16.0);
            
            // Get guard point
            if (!entity.hasMetadata("udx_guard_point")) return;
            Location guardPoint = (Location) entity.getMetadata("udx_guard_point").get(0).value();
            
            // Check if too far from guard point
            if (entity.getLocation().distance(guardPoint) > maxDistance) {
                mob.getPathfinder().moveTo(guardPoint);
            }
        }
        
        @Override
        public void remove(@NotNull LivingEntity entity) {
            // No cleanup needed
        }
    }
    
    /**
     * Leap behavior - makes mobs leap at targets.
     */
    public static final class LeapBehavior implements MobBehavior {
        
        private static final String ID = "leap";
        private static final int PRIORITY = 5;
        
        @Override
        @NotNull
        public String getId() {
            return ID;
        }
        
        @Override
        public int getPriority() {
            return PRIORITY;
        }
        
        @Override
        public boolean canApply(@NotNull LivingEntity entity, @NotNull Map<String, Object> parameters) {
            return entity instanceof Mob;
        }
        
        @Override
        public boolean isActive(@NotNull LivingEntity entity) {
            if (!(entity instanceof Mob mob)) return false;
            LivingEntity target = mob.getTarget();
            return target != null && entity.getLocation().distance(target.getLocation()) <= 8.0;
        }
        
        @Override
        public void apply(@NotNull LivingEntity entity, @NotNull Map<String, Object> parameters) {
            // Initialize leap cooldown
            entity.setMetadata("udx_leap_cooldown", new org.bukkit.metadata.FixedMetadataValue(
                org.bukkit.Bukkit.getPluginManager().getPlugin("UltimateDungeonX"), 
                0L
            ));
        }
        
        @Override
        public void update(@NotNull LivingEntity entity, @NotNull Map<String, Object> parameters) {
            if (!(entity instanceof Mob mob)) return;
            
            LivingEntity target = mob.getTarget();
            if (target == null) return;
            
            // Check cooldown
            long cooldown = getParameter(parameters, "cooldown", 3000L);
            long lastLeap = entity.hasMetadata("udx_leap_cooldown") ? 
                (Long) entity.getMetadata("udx_leap_cooldown").get(0).value() : 0L;
            
            if (System.currentTimeMillis() - lastLeap < cooldown) return;
            
            // Perform leap
            Vector direction = target.getLocation().toVector().subtract(entity.getLocation().toVector()).normalize();
            direction.setY(0.5); // Add upward component
            direction.multiply(1.5); // Leap strength
            
            entity.setVelocity(direction);
            
            // Update cooldown
            entity.setMetadata("udx_leap_cooldown", new org.bukkit.metadata.FixedMetadataValue(
                org.bukkit.Bukkit.getPluginManager().getPlugin("UltimateDungeonX"), 
                System.currentTimeMillis()
            ));
        }
        
        @Override
        public void remove(@NotNull LivingEntity entity) {
            // No cleanup needed
        }
    }
    
    /**
     * Charge behavior - makes mobs charge at targets.
     */
    public static final class ChargeBehavior implements MobBehavior {
        
        private static final String ID = "charge";
        private static final int PRIORITY = 4;
        
        @Override
        @NotNull
        public String getId() {
            return ID;
        }
        
        @Override
        public int getPriority() {
            return PRIORITY;
        }
        
        @Override
        public boolean canApply(@NotNull LivingEntity entity, @NotNull Map<String, Object> parameters) {
            return entity instanceof Mob;
        }
        
        @Override
        public boolean isActive(@NotNull LivingEntity entity) {
            if (!(entity instanceof Mob mob)) return false;
            LivingEntity target = mob.getTarget();
            return target != null && entity.getLocation().distance(target.getLocation()) > 3.0;
        }
        
        @Override
        public void apply(@NotNull LivingEntity entity, @NotNull Map<String, Object> parameters) {
            // Initialize charge cooldown
            entity.setMetadata("udx_charge_cooldown", new org.bukkit.metadata.FixedMetadataValue(
                org.bukkit.Bukkit.getPluginManager().getPlugin("UltimateDungeonX"), 
                0L
            ));
        }
        
        @Override
        public void update(@NotNull LivingEntity entity, @NotNull Map<String, Object> parameters) {
            if (!(entity instanceof Mob mob)) return;
            
            LivingEntity target = mob.getTarget();
            if (target == null) return;
            
            // Check cooldown
            long cooldown = getParameter(parameters, "cooldown", 5000L);
            long lastCharge = entity.hasMetadata("udx_charge_cooldown") ? 
                (Long) entity.getMetadata("udx_charge_cooldown").get(0).value() : 0L;
            
            if (System.currentTimeMillis() - lastCharge < cooldown) return;
            
            // Increase movement speed temporarily
            if (entity.getAttribute(Attribute.MOVEMENT_SPEED) != null) {
                double originalSpeed = entity.getAttribute(Attribute.MOVEMENT_SPEED).getBaseValue();
                double chargeSpeed = originalSpeed * 2.0;
                
                entity.getAttribute(Attribute.MOVEMENT_SPEED).setBaseValue(chargeSpeed);
                mob.getPathfinder().moveTo(target.getLocation(), chargeSpeed);
                
                // Reset speed after charge
                org.bukkit.Bukkit.getScheduler().runTaskLater(
                    org.bukkit.Bukkit.getPluginManager().getPlugin("UltimateDungeonX"),
                    () -> {
                        if (entity.getAttribute(Attribute.MOVEMENT_SPEED) != null) {
                            entity.getAttribute(Attribute.MOVEMENT_SPEED).setBaseValue(originalSpeed);
                        }
                    }, 40L // 2 seconds
                );
            }
            
            // Update cooldown
            entity.setMetadata("udx_charge_cooldown", new org.bukkit.metadata.FixedMetadataValue(
                org.bukkit.Bukkit.getPluginManager().getPlugin("UltimateDungeonX"), 
                System.currentTimeMillis()
            ));
        }
        
        @Override
        public void remove(@NotNull LivingEntity entity) {
            // Reset movement speed
            if (entity.getAttribute(Attribute.MOVEMENT_SPEED) != null) {
                // This would ideally restore the original speed, but we'll use a default
                entity.getAttribute(Attribute.MOVEMENT_SPEED).setBaseValue(0.25);
            }
        }
    }
    
    /**
     * Ranged behavior - makes mobs keep distance and attack from range.
     */
    public static final class RangedBehavior implements MobBehavior {
        
        private static final String ID = "ranged";
        private static final int PRIORITY = 3;
        
        @Override
        @NotNull
        public String getId() {
            return ID;
        }
        
        @Override
        public int getPriority() {
            return PRIORITY;
        }
        
        @Override
        public boolean canApply(@NotNull LivingEntity entity, @NotNull Map<String, Object> parameters) {
            return entity instanceof Mob;
        }
        
        @Override
        public boolean isActive(@NotNull LivingEntity entity) {
            if (!(entity instanceof Mob mob)) return false;
            return mob.getTarget() != null;
        }
        
        @Override
        public void apply(@NotNull LivingEntity entity, @NotNull Map<String, Object> parameters) {
            // No initialization needed
        }
        
        @Override
        public void update(@NotNull LivingEntity entity, @NotNull Map<String, Object> parameters) {
            if (!(entity instanceof Mob mob)) return;
            
            LivingEntity target = mob.getTarget();
            if (target == null) return;
            
            double distance = entity.getLocation().distance(target.getLocation());
            double optimalRange = getParameter(parameters, "optimalRange", 8.0);
            double minRange = getParameter(parameters, "minRange", 4.0);
            
            if (distance < minRange) {
                // Too close, back away
                Vector direction = entity.getLocation().toVector().subtract(target.getLocation().toVector()).normalize();
                Location backAwayPoint = entity.getLocation().add(direction.multiply(2));
                mob.getPathfinder().moveTo(backAwayPoint);
            } else if (distance > optimalRange) {
                // Too far, move closer
                mob.getPathfinder().moveTo(target.getLocation());
            }
            // If in optimal range, stay put and let natural AI handle attacking
        }
        
        @Override
        public void remove(@NotNull LivingEntity entity) {
            // No cleanup needed
        }
    }
    
    /**
     * Helper method to get parameter with default value.
     */
    @SuppressWarnings("unchecked")
    private static <T> T getParameter(@NotNull Map<String, Object> parameters, @NotNull String key, @NotNull T defaultValue) {
        Object value = parameters.get(key);
        if (value != null && defaultValue.getClass().isInstance(value)) {
            return (T) value;
        }
        return defaultValue;
    }
}

package com.ultimatedungeon.udx.instance;

/**
 * Represents the lifecycle state of a dungeon instance.
 * 
 * <p>Instances follow a strict state machine pattern:
 * CREATING → READY → RUNNING → COMPLETING → DISPOSING</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public enum InstanceState {
    
    /**
     * Instance is being created (world generation, room placement).
     */
    CREATING,
    
    /**
     * Instance is ready for players to join.
     */
    READY,
    
    /**
     * Instance is actively running with players inside.
     */
    RUNNING,
    
    /**
     * Instance is completing (victory/defeat processing).
     */
    COMPLETING,
    
    /**
     * Instance is being disposed (cleanup, world deletion).
     */
    DISPOSING;
    
    /**
     * Checks if this state can transition to the target state.
     * 
     * @param target the target state
     * @return true if transition is valid
     */
    public boolean canTransitionTo(InstanceState target) {
        return switch (this) {
            case CREATING -> target == READY || target == DISPOSING;
            case READY -> target == RUNNING || target == DISPOSING;
            case RUNNING -> target == COMPLETING || target == DISPOSING;
            case COMPLETING -> target == DISPOSING;
            case DISPOSING -> false; // Terminal state
        };
    }
    
    /**
     * Checks if players can join in this state.
     * 
     * @return true if players can join
     */
    public boolean canJoin() {
        return this == READY;
    }
    
    /**
     * Checks if the instance is active (has players or is running).
     * 
     * @return true if active
     */
    public boolean isActive() {
        return this == RUNNING || this == COMPLETING;
    }
    
    /**
     * Checks if the instance is in a terminal state.
     * 
     * @return true if terminal
     */
    public boolean isTerminal() {
        return this == DISPOSING;
    }
}

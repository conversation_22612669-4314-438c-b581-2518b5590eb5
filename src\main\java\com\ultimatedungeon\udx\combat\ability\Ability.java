package com.ultimatedungeon.udx.combat.ability;

import com.ultimatedungeon.udx.combat.DamageType;
import com.ultimatedungeon.udx.combat.StatusEffect;
import org.bukkit.Location;
import org.bukkit.entity.LivingEntity;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.Map;

/**
 * Represents an ability that can be used by entities.
 * 
 * <p>Abilities can deal damage, apply status effects, create visual effects,
 * and perform various other actions. They have cooldowns, costs, and targeting rules.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public record Ability(
    @NotNull String id,
    @NotNull String name,
    @NotNull String description,
    @NotNull AbilityType type,
    @NotNull TargetType targetType,
    @NotNull CastType castType,
    long cooldownMs,
    long castTimeMs,
    double range,
    double cost,
    @NotNull String costType,
    @NotNull List<AbilityEffect> effects,
    @NotNull Map<String, Object> parameters,
    @Nullable AbilityVisuals visuals
) {
    
    /**
     * Types of abilities.
     */
    public enum AbilityType {
        OFFENSIVE,
        DEFENSIVE,
        UTILITY,
        MOVEMENT,
        HEALING,
        BUFF,
        DEBUFF
    }
    
    /**
     * Target types for abilities.
     */
    public enum TargetType {
        SELF,
        SINGLE_ENEMY,
        SINGLE_ALLY,
        SINGLE_ANY,
        AREA_ENEMY,
        AREA_ALLY,
        AREA_ANY,
        CONE,
        LINE,
        GROUND_TARGET
    }
    
    /**
     * Cast types for abilities.
     */
    public enum CastType {
        INSTANT,
        CHANNELED,
        CAST_TIME,
        TOGGLE
    }
    
    /**
     * Represents an effect that an ability can have.
     */
    public record AbilityEffect(
        @NotNull EffectType type,
        double value,
        @Nullable DamageType damageType,
        @Nullable StatusEffect statusEffect,
        @NotNull Map<String, Object> parameters
    ) {
        
        public enum EffectType {
            DAMAGE,
            HEAL,
            STATUS_EFFECT,
            KNOCKBACK,
            TELEPORT,
            SUMMON,
            CUSTOM
        }
    }
    
    /**
     * Visual effects for abilities.
     */
    public record AbilityVisuals(
        @Nullable String castParticle,
        @Nullable String hitParticle,
        @Nullable String areaParticle,
        @Nullable String castSound,
        @Nullable String hitSound,
        @NotNull Map<String, Object> visualParameters
    ) {}
    
    /**
     * Checks if this ability can be used by the given entity.
     * 
     * @param caster the entity trying to use the ability
     * @return true if the ability can be used, false otherwise
     */
    public boolean canUse(@NotNull LivingEntity caster) {
        // Check if entity has enough resources (mana, stamina, etc.)
        return hasEnoughCost(caster) && !isOnCooldown(caster);
    }
    
    /**
     * Checks if the caster has enough resources to use this ability.
     * 
     * @param caster the caster entity
     * @return true if they have enough resources
     */
    private boolean hasEnoughCost(@NotNull LivingEntity caster) {
        // Implementation would check based on costType (mana, health, etc.)
        return true; // Placeholder
    }
    
    /**
     * Checks if this ability is on cooldown for the caster.
     * 
     * @param caster the caster entity
     * @return true if on cooldown
     */
    private boolean isOnCooldown(@NotNull LivingEntity caster) {
        // Implementation would check cooldown tracking
        return false; // Placeholder
    }
    
    /**
     * Gets the effective range of this ability.
     * 
     * @param caster the caster entity
     * @return the effective range
     */
    public double getEffectiveRange(@NotNull LivingEntity caster) {
        // Could be modified by caster's stats or equipment
        return range;
    }
    
    /**
     * Gets the effective cooldown of this ability.
     * 
     * @param caster the caster entity
     * @return the effective cooldown in milliseconds
     */
    public long getEffectiveCooldown(@NotNull LivingEntity caster) {
        // Could be modified by caster's stats or equipment
        return cooldownMs;
    }
    
    /**
     * Gets the effective cast time of this ability.
     * 
     * @param caster the caster entity
     * @return the effective cast time in milliseconds
     */
    public long getEffectiveCastTime(@NotNull LivingEntity caster) {
        // Could be modified by caster's stats or equipment
        return castTimeMs;
    }
    
    /**
     * Checks if this ability requires a target.
     * 
     * @return true if a target is required
     */
    public boolean requiresTarget() {
        return targetType != TargetType.SELF && 
               targetType != TargetType.GROUND_TARGET &&
               !targetType.name().startsWith("AREA");
    }
    
    /**
     * Checks if this ability can target the given entity.
     * 
     * @param caster the caster
     * @param target the potential target
     * @return true if the target is valid
     */
    public boolean canTarget(@NotNull LivingEntity caster, @NotNull LivingEntity target) {
        return switch (targetType) {
            case SELF -> caster.equals(target);
            case SINGLE_ENEMY -> isEnemy(caster, target);
            case SINGLE_ALLY -> isAlly(caster, target);
            case SINGLE_ANY -> true;
            case AREA_ENEMY -> isEnemy(caster, target);
            case AREA_ALLY -> isAlly(caster, target);
            case AREA_ANY -> true;
            default -> false;
        };
    }
    
    /**
     * Checks if this ability can target the given location.
     * 
     * @param caster the caster
     * @param location the target location
     * @return true if the location is valid
     */
    public boolean canTarget(@NotNull LivingEntity caster, @NotNull Location location) {
        if (targetType != TargetType.GROUND_TARGET) {
            return false;
        }
        
        double distance = caster.getLocation().distance(location);
        return distance <= getEffectiveRange(caster);
    }
    
    /**
     * Gets a parameter value with a default.
     * 
     * @param key the parameter key
     * @param defaultValue the default value
     * @param <T> the parameter type
     * @return the parameter value or default
     */
    @SuppressWarnings("unchecked")
    public <T> T getParameter(@NotNull String key, @NotNull T defaultValue) {
        Object value = parameters.get(key);
        if (value != null && defaultValue.getClass().isInstance(value)) {
            return (T) value;
        }
        return defaultValue;
    }
    
    /**
     * Validates the ability configuration.
     * 
     * @throws IllegalArgumentException if the configuration is invalid
     */
    public void validate() {
        if (id.isBlank()) {
            throw new IllegalArgumentException("Ability ID cannot be blank");
        }
        
        if (name.isBlank()) {
            throw new IllegalArgumentException("Ability name cannot be blank");
        }
        
        if (cooldownMs < 0) {
            throw new IllegalArgumentException("Ability cooldown cannot be negative");
        }
        
        if (castTimeMs < 0) {
            throw new IllegalArgumentException("Ability cast time cannot be negative");
        }
        
        if (range < 0) {
            throw new IllegalArgumentException("Ability range cannot be negative");
        }
        
        if (cost < 0) {
            throw new IllegalArgumentException("Ability cost cannot be negative");
        }
        
        if (costType.isBlank()) {
            throw new IllegalArgumentException("Ability cost type cannot be blank");
        }
        
        // Validate effects
        for (AbilityEffect effect : effects) {
            validateEffect(effect);
        }
    }
    
    private void validateEffect(@NotNull AbilityEffect effect) {
        if (effect.type() == AbilityEffect.EffectType.DAMAGE && effect.damageType() == null) {
            throw new IllegalArgumentException("Damage effect must have a damage type");
        }
        
        if (effect.type() == AbilityEffect.EffectType.STATUS_EFFECT && effect.statusEffect() == null) {
            throw new IllegalArgumentException("Status effect must have a status effect definition");
        }
        
        if (effect.value() < 0 && effect.type() != AbilityEffect.EffectType.CUSTOM) {
            throw new IllegalArgumentException("Effect value cannot be negative for type: " + effect.type());
        }
    }
    
    // Helper methods for targeting
    
    private boolean isEnemy(@NotNull LivingEntity caster, @NotNull LivingEntity target) {
        // Implementation would check faction/team relationships
        // For now, assume players are allies and mobs are enemies
        return !(caster instanceof org.bukkit.entity.Player && target instanceof org.bukkit.entity.Player);
    }
    
    private boolean isAlly(@NotNull LivingEntity caster, @NotNull LivingEntity target) {
        // Implementation would check faction/team relationships
        return !isEnemy(caster, target);
    }
}

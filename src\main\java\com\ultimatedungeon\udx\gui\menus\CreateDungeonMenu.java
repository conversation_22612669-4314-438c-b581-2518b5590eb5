package com.ultimatedungeon.udx.gui.menus;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.gui.Menu;
import com.ultimatedungeon.udx.util.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.WorldCreator;
import org.bukkit.WorldType;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.util.concurrent.CompletableFuture;

/**
 * Menu for creating new custom dungeons.
 * 
 * <p>This menu allows admins to create new dungeon worlds with custom names
 * and automatically generates superflat worlds for building.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class CreateDungeonMenu extends Menu {
    
    private final UltimateDungeonX plugin;
    private String dungeonName = "";
    
    public CreateDungeonMenu(@NotNull UltimateDungeonX plugin, @NotNull Player player) {
        super(player, Component.text("Create Dungeon").color(NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true), 
              27, plugin.getMenuRegistry());
        this.plugin = plugin;
    }
    
    @Override
    protected void setupMenu() {
        // Clear inventory
        inventory.clear();
        clickHandlers.clear();
        
        // Name Input (using anvil GUI simulation)
        ItemStack nameInput = new ItemBuilder(Material.NAME_TAG)
            .name(Component.text("Dungeon Name").color(NamedTextColor.YELLOW).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Current name: " + (dungeonName.isEmpty() ? "Not set" : dungeonName)).color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to enter dungeon name").color(NamedTextColor.GREEN),
                Component.text("Use chat to type the name").color(NamedTextColor.GRAY)
            )
            .build();
        
        setItem(11, nameInput, clickType -> {
            if (clickType == ClickType.LEFT) {
                close();
                player.sendMessage(Component.text("Enter the dungeon name in chat (or 'cancel' to abort):").color(NamedTextColor.YELLOW));
                
                // Set up chat listener for name input
                plugin.getSchedulerUtil().runTaskLater(() -> {
                    // This is a simplified approach - in a full implementation you'd use AsyncPlayerChatEvent
                    // For now, we'll use a default name system
                    openNameSelectionMenu();
                }, 1L);
            }
        });
        
        // Quick Name Options
        ItemStack quickName1 = new ItemBuilder(Material.PAPER)
            .name(Component.text("Quick Name: MyDungeon").color(NamedTextColor.AQUA))
            .lore(Component.text("Click to use this name").color(NamedTextColor.GRAY))
            .build();
        
        setItem(13, quickName1, clickType -> {
            if (clickType == ClickType.LEFT) {
                setDungeonName("MyDungeon");
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
            }
        });
        
        ItemStack quickName2 = new ItemBuilder(Material.PAPER)
            .name(Component.text("Quick Name: TestDungeon").color(NamedTextColor.AQUA))
            .lore(Component.text("Click to use this name").color(NamedTextColor.GRAY))
            .build();
        
        setItem(15, quickName2, clickType -> {
            if (clickType == ClickType.LEFT) {
                setDungeonName("TestDungeon");
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
            }
        });
        
        // Create Button
        ItemStack createButton = new ItemBuilder(dungeonName.isEmpty() ? Material.GRAY_DYE : Material.LIME_DYE)
            .name(Component.text("Create Dungeon").color(dungeonName.isEmpty() ? NamedTextColor.GRAY : NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true))
            .lore(
                dungeonName.isEmpty() ? 
                    Component.text("Please set a dungeon name first").color(NamedTextColor.RED) :
                    Component.text("Create '" + dungeonName + "' dungeon world").color(NamedTextColor.GREEN),
                Component.empty(),
                dungeonName.isEmpty() ? 
                    Component.text("Name required").color(NamedTextColor.RED) :
                    Component.text("Click to create superflat world").color(NamedTextColor.YELLOW)
            )
            .glow(!dungeonName.isEmpty())
            .build();
        
        setItem(22, createButton, clickType -> {
            if (clickType == ClickType.LEFT && !dungeonName.isEmpty()) {
                createDungeonWorld();
            } else if (dungeonName.isEmpty()) {
                player.sendMessage(Component.text("Please set a dungeon name first!").color(NamedTextColor.RED));
                player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
            }
        });
        
        // Back Button
        ItemStack backButton = new ItemBuilder(Material.BARRIER)
            .name(Component.text("Back").color(NamedTextColor.RED))
            .lore(Component.text("Return to admin hub").color(NamedTextColor.GRAY))
            .build();
        
        setItem(18, backButton, clickType -> {
            if (clickType == ClickType.LEFT) {
                new AdminHubMenu(plugin, player).open();
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 0.8f);
            }
        });
        
        // Fill empty slots
        fillEmptySlots();
    }
    
    private void openNameSelectionMenu() {
        // Reopen this menu for name selection
        new CreateDungeonMenu(plugin, player).open();
    }
    
    private void setDungeonName(@NotNull String name) {
        // Validate name
        if (name.matches("^[a-zA-Z0-9_-]+$") && name.length() <= 16) {
            this.dungeonName = name;
            setupMenu(); // Refresh the menu
            player.sendMessage(Component.text("Dungeon name set to: " + name).color(NamedTextColor.GREEN));
        } else {
            player.sendMessage(Component.text("Invalid name! Use only letters, numbers, _ and - (max 16 chars)").color(NamedTextColor.RED));
            player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
        }
    }
    
    private void createDungeonWorld() {
        close();
        
        player.sendMessage(Component.text("Creating dungeon world '" + dungeonName + "'...").color(NamedTextColor.YELLOW));
        player.playSound(player.getLocation(), Sound.BLOCK_PORTAL_AMBIENT, 1.0f, 1.0f);
        
        // Create world asynchronously
        CompletableFuture.runAsync(() -> {
            try {
                String worldName = "udx_dungeon_" + dungeonName.toLowerCase();
                
                // Check if world already exists
                if (Bukkit.getWorld(worldName) != null) {
                    plugin.getSchedulerUtil().runTask(() -> {
                        player.sendMessage(Component.text("A dungeon with that name already exists!").color(NamedTextColor.RED));
                        player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
                    });
                    return;
                }
                
                // Create superflat world
                plugin.getSchedulerUtil().runTask(() -> {
                    WorldCreator creator = new WorldCreator(worldName);
                    creator.type(WorldType.FLAT);
                    creator.generatorSettings("minecraft:bedrock,2*minecraft:dirt,minecraft:grass_block;minecraft:plains;village");
                    
                    World world = creator.createWorld();
                    
                    if (world != null) {
                        // Set world properties
                        world.setDifficulty(org.bukkit.Difficulty.PEACEFUL);
                        world.setSpawnFlags(false, false);
                        world.setPVP(false);
                        world.setGameRule(org.bukkit.GameRule.DO_MOB_SPAWNING, false);
                        world.setGameRule(org.bukkit.GameRule.DO_DAYLIGHT_CYCLE, false);
                        world.setGameRule(org.bukkit.GameRule.DO_WEATHER_CYCLE, false);
                        world.setTime(6000); // Noon
                        
                        // Save dungeon info
                        plugin.getDungeonService().registerCustomDungeon(dungeonName, worldName);
                        
                        player.sendMessage(Component.text("Dungeon world '" + dungeonName + "' created successfully!").color(NamedTextColor.GREEN));
                        player.sendMessage(Component.text("Use '/udx tp " + dungeonName + "' to teleport there").color(NamedTextColor.YELLOW));
                        player.sendMessage(Component.text("Use '/udx tools' in the world to access builder tools").color(NamedTextColor.AQUA));
                        player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.0f);
                        
                        // Open admin hub
                        new AdminHubMenu(plugin, player).open();
                    } else {
                        player.sendMessage(Component.text("Failed to create dungeon world!").color(NamedTextColor.RED));
                        player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
                    }
                });
                
            } catch (Exception e) {
                plugin.getLogger().severe("Error creating dungeon world: " + e.getMessage());
                plugin.getSchedulerUtil().runTask(() -> {
                    player.sendMessage(Component.text("Error creating dungeon world: " + e.getMessage()).color(NamedTextColor.RED));
                    player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
                });
            }
        });
    }
}

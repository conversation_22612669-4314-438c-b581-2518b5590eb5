package com.ultimatedungeon.udx.affix;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;

/**
 * Represents an affix rotation for a specific time period.
 * 
 * <p>Affix rotations define which affixes are active during
 * daily or weekly periods, providing variety and scheduled challenges.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class AffixRotation {
    
    private final String id;
    private final RotationType type;
    private final LocalDate startDate;
    private final LocalDate endDate;
    private final List<String> affixIds;
    private final Map<String, List<String>> dungeonSpecificAffixes;
    private final boolean active;
    
    /**
     * Creates a new affix rotation.
     * 
     * @param id the rotation ID
     * @param type the rotation type
     * @param startDate the start date
     * @param endDate the end date
     * @param affixIds the list of affix IDs
     * @param dungeonSpecificAffixes dungeon-specific affix overrides
     * @param active whether the rotation is active
     */
    public AffixRotation(@NotNull String id, @NotNull RotationType type,
                        @NotNull LocalDate startDate, @NotNull LocalDate endDate,
                        @NotNull List<String> affixIds,
                        @NotNull Map<String, List<String>> dungeonSpecificAffixes,
                        boolean active) {
        this.id = id;
        this.type = type;
        this.startDate = startDate;
        this.endDate = endDate;
        this.affixIds = affixIds;
        this.dungeonSpecificAffixes = dungeonSpecificAffixes;
        this.active = active;
    }
    
    /**
     * Gets the rotation ID.
     * 
     * @return the rotation ID
     */
    @NotNull
    public String getId() {
        return id;
    }
    
    /**
     * Gets the rotation type.
     * 
     * @return the rotation type
     */
    @NotNull
    public RotationType getType() {
        return type;
    }
    
    /**
     * Gets the start date.
     * 
     * @return the start date
     */
    @NotNull
    public LocalDate getStartDate() {
        return startDate;
    }
    
    /**
     * Gets the end date.
     * 
     * @return the end date
     */
    @NotNull
    public LocalDate getEndDate() {
        return endDate;
    }
    
    /**
     * Gets the list of affix IDs.
     * 
     * @return the affix IDs
     */
    @NotNull
    public List<String> getAffixIds() {
        return affixIds;
    }
    
    /**
     * Gets dungeon-specific affix overrides.
     * 
     * @return the dungeon-specific affixes map
     */
    @NotNull
    public Map<String, List<String>> getDungeonSpecificAffixes() {
        return dungeonSpecificAffixes;
    }
    
    /**
     * Checks if the rotation is active.
     * 
     * @return true if active
     */
    public boolean isActive() {
        return active;
    }
    
    /**
     * Gets the affixes for a specific dungeon.
     * 
     * @param dungeonId the dungeon ID
     * @return the affix IDs for the dungeon, or global affixes if no override
     */
    @NotNull
    public List<String> getAffixesForDungeon(@NotNull String dungeonId) {
        return dungeonSpecificAffixes.getOrDefault(dungeonId, affixIds);
    }
    
    /**
     * Checks if the rotation is currently active based on date.
     * 
     * @param currentDate the current date
     * @return true if the rotation is active on the given date
     */
    public boolean isActiveOn(@NotNull LocalDate currentDate) {
        return active && 
               !currentDate.isBefore(startDate) && 
               !currentDate.isAfter(endDate);
    }
    
    /**
     * Gets the number of days remaining in this rotation.
     * 
     * @param currentDate the current date
     * @return the days remaining, or 0 if expired
     */
    public long getDaysRemaining(@NotNull LocalDate currentDate) {
        if (currentDate.isAfter(endDate)) {
            return 0;
        }
        return ChronoUnit.DAYS.between(currentDate, endDate) + 1;
    }
    
    /**
     * Gets the total duration of this rotation in days.
     * 
     * @return the duration in days
     */
    public long getDurationDays() {
        return ChronoUnit.DAYS.between(startDate, endDate) + 1;
    }
    
    /**
     * Gets the progress of this rotation (0.0 to 1.0).
     * 
     * @param currentDate the current date
     * @return the progress percentage
     */
    public double getProgress(@NotNull LocalDate currentDate) {
        if (currentDate.isBefore(startDate)) {
            return 0.0;
        }
        if (currentDate.isAfter(endDate)) {
            return 1.0;
        }
        
        long totalDays = getDurationDays();
        long elapsedDays = ChronoUnit.DAYS.between(startDate, currentDate);
        return (double) elapsedDays / totalDays;
    }
    
    /**
     * Creates a preview string for this rotation.
     * 
     * @return the preview string
     */
    @NotNull
    public String getPreview() {
        StringBuilder sb = new StringBuilder();
        sb.append("§6").append(type.getDisplayName()).append(" Rotation");
        sb.append("\n§7Period: ").append(startDate).append(" - ").append(endDate);
        sb.append("\n§7Affixes: ");
        
        if (affixIds.isEmpty()) {
            sb.append("§8None");
        } else {
            for (int i = 0; i < affixIds.size(); i++) {
                if (i > 0) sb.append("§7, ");
                sb.append("§e").append(affixIds.get(i));
            }
        }
        
        if (!dungeonSpecificAffixes.isEmpty()) {
            sb.append("\n§7Dungeon Overrides: ").append(dungeonSpecificAffixes.size());
        }
        
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return "AffixRotation{" +
                "id='" + id + '\'' +
                ", type=" + type +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", affixCount=" + affixIds.size() +
                ", active=" + active +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        AffixRotation rotation = (AffixRotation) obj;
        return id.equals(rotation.id);
    }
    
    @Override
    public int hashCode() {
        return id.hashCode();
    }
    
    /**
     * Represents the type of rotation.
     */
    public enum RotationType {
        
        /**
         * Daily rotation - changes every day.
         */
        DAILY("Daily", 1),
        
        /**
         * Weekly rotation - changes every week.
         */
        WEEKLY("Weekly", 7),
        
        /**
         * Monthly rotation - changes every month.
         */
        MONTHLY("Monthly", 30),
        
        /**
         * Seasonal rotation - changes every season.
         */
        SEASONAL("Seasonal", 90),
        
        /**
         * Custom rotation - manually defined period.
         */
        CUSTOM("Custom", -1);
        
        private final String displayName;
        private final int defaultDurationDays;
        
        RotationType(String displayName, int defaultDurationDays) {
            this.displayName = displayName;
            this.defaultDurationDays = defaultDurationDays;
        }
        
        /**
         * Gets the display name.
         * 
         * @return the display name
         */
        public String getDisplayName() {
            return displayName;
        }
        
        /**
         * Gets the default duration in days.
         * 
         * @return the default duration, or -1 for custom
         */
        public int getDefaultDurationDays() {
            return defaultDurationDays;
        }
    }
}

package com.ultimatedungeon.udx.loot;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.jetbrains.annotations.NotNull;

/**
 * Represents item rarity tiers with associated colors and multipliers.
 * 
 * <p>Each rarity has a display name, color, drop chance multiplier,
 * and visual effects for items of that rarity.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public enum Rarity {
    
    COMMON("Common", NamedTextColor.WHITE, 1.0, false),
    UNCOMMON("Uncommon", NamedTextColor.GREEN, 0.7, false),
    RARE("Rare", NamedTextColor.BLUE, 0.4, false),
    EPIC("Epic", NamedTextColor.DARK_PURPLE, 0.15, true),
    LEGENDARY("Legendary", NamedTextColor.GOLD, 0.05, true),
    MYTH<PERSON>("Mythic", NamedTextColor.RED, 0.01, true);
    
    private final String displayName;
    private final TextColor color;
    private final double dropChanceMultiplier;
    private final boolean hasGlow;
    
    Rarity(@NotNull String displayName, @NotNull TextColor color, 
           double dropChanceMultiplier, boolean hasGlow) {
        this.displayName = displayName;
        this.color = color;
        this.dropChanceMultiplier = dropChanceMultiplier;
        this.hasGlow = hasGlow;
    }
    
    /**
     * Gets the display name of this rarity.
     * 
     * @return the display name
     */
    @NotNull
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * Gets the color associated with this rarity.
     * 
     * @return the text color
     */
    @NotNull
    public TextColor getColor() {
        return color;
    }
    
    /**
     * Gets the drop chance multiplier for this rarity.
     * Lower values mean rarer drops.
     * 
     * @return the drop chance multiplier
     */
    public double getDropChanceMultiplier() {
        return dropChanceMultiplier;
    }
    
    /**
     * Checks if items of this rarity should have a glow effect.
     * 
     * @return true if items should glow
     */
    public boolean hasGlow() {
        return hasGlow;
    }
    
    /**
     * Creates a formatted component with this rarity's styling.
     * 
     * @param text the text to format
     * @return the formatted component
     */
    @NotNull
    public Component formatText(@NotNull String text) {
        Component component = Component.text(text, color);
        if (hasGlow) {
            component = component.decoration(TextDecoration.BOLD, true);
        }
        return component;
    }
    
    /**
     * Gets the rarity prefix for item names.
     * 
     * @return the formatted rarity prefix
     */
    @NotNull
    public Component getPrefix() {
        return formatText("[" + displayName + "] ");
    }
    
    /**
     * Gets a rarity by name (case-insensitive).
     * 
     * @param name the rarity name
     * @return the rarity, or COMMON if not found
     */
    @NotNull
    public static Rarity fromString(@NotNull String name) {
        try {
            return valueOf(name.toUpperCase());
        } catch (IllegalArgumentException e) {
            return COMMON;
        }
    }
    
    /**
     * Gets the next higher rarity tier.
     * 
     * @return the next rarity, or this rarity if already at maximum
     */
    @NotNull
    public Rarity getNextTier() {
        Rarity[] values = values();
        int currentIndex = ordinal();
        if (currentIndex < values.length - 1) {
            return values[currentIndex + 1];
        }
        return this;
    }
    
    /**
     * Gets the previous lower rarity tier.
     * 
     * @return the previous rarity, or this rarity if already at minimum
     */
    @NotNull
    public Rarity getPreviousTier() {
        int currentIndex = ordinal();
        if (currentIndex > 0) {
            return values()[currentIndex - 1];
        }
        return this;
    }
}

package com.ultimatedungeon.udx.gen;

import org.jetbrains.annotations.NotNull;

import java.util.*;

/**
 * Defines constraints for dungeon generation.
 * 
 * <p>This class specifies the parameters and limits for procedural
 * dungeon generation, including size, difficulty, and structural requirements.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class GenerationConstraints {
    
    private final int minRooms;
    private final int maxRooms;
    private final int targetRooms;
    private final int maxDepth;
    private final double branchingFactor;
    private final int targetDifficulty;
    private final Set<String> requiredTags;
    private final Set<String> forbiddenTags;
    private final long seed;
    private final boolean allowOverlaps;
    private final int maxRetries;
    private final int roomSpacing;
    
    private GenerationConstraints(@NotNull Builder builder) {
        this.minRooms = builder.minRooms;
        this.maxRooms = builder.maxRooms;
        this.targetRooms = builder.targetRooms;
        this.maxDepth = builder.maxDepth;
        this.branchingFactor = builder.branchingFactor;
        this.targetDifficulty = builder.targetDifficulty;
        this.requiredTags = Set.copyOf(builder.requiredTags);
        this.forbiddenTags = Set.copyOf(builder.forbiddenTags);
        this.seed = builder.seed;
        this.allowOverlaps = builder.allowOverlaps;
        this.maxRetries = builder.maxRetries;
        this.roomSpacing = builder.roomSpacing;
    }
    
    /**
     * Gets the minimum number of rooms.
     * 
     * @return minimum rooms
     */
    public int getMinRooms() {
        return minRooms;
    }
    
    /**
     * Gets the maximum number of rooms.
     * 
     * @return maximum rooms
     */
    public int getMaxRooms() {
        return maxRooms;
    }
    
    /**
     * Gets the target number of rooms.
     * 
     * @return target rooms
     */
    public int getTargetRooms() {
        return targetRooms;
    }
    
    /**
     * Gets the maximum depth from entrance.
     * 
     * @return maximum depth
     */
    public int getMaxDepth() {
        return maxDepth;
    }
    
    /**
     * Gets the branching factor (average branches per room).
     * 
     * @return branching factor (0.0 = linear, 2.0 = highly branched)
     */
    public double getBranchingFactor() {
        return branchingFactor;
    }
    
    /**
     * Gets the target difficulty level.
     * 
     * @return difficulty (1-10)
     */
    public int getTargetDifficulty() {
        return targetDifficulty;
    }
    
    /**
     * Gets the required room tags.
     * 
     * @return set of required tags
     */
    @NotNull
    public Set<String> getRequiredTags() {
        return requiredTags;
    }
    
    /**
     * Gets the forbidden room tags.
     * 
     * @return set of forbidden tags
     */
    @NotNull
    public Set<String> getForbiddenTags() {
        return forbiddenTags;
    }
    
    /**
     * Gets the generation seed.
     * 
     * @return random seed
     */
    public long getSeed() {
        return seed;
    }
    
    /**
     * Checks if room overlaps are allowed.
     * 
     * @return true if overlaps allowed
     */
    public boolean isAllowOverlaps() {
        return allowOverlaps;
    }
    
    /**
     * Gets the maximum number of retries for placement.
     * 
     * @return max retries
     */
    public int getMaxRetries() {
        return maxRetries;
    }
    
    /**
     * Gets the minimum spacing between rooms.
     * 
     * @return room spacing in blocks
     */
    public int getRoomSpacing() {
        return roomSpacing;
    }
    
    /**
     * Validates the constraints.
     * 
     * @return list of validation errors (empty if valid)
     */
    @NotNull
    public List<String> validate() {
        List<String> errors = new ArrayList<>();
        
        if (minRooms < 1) {
            errors.add("Minimum rooms must be at least 1");
        }
        
        if (maxRooms < minRooms) {
            errors.add("Maximum rooms must be >= minimum rooms");
        }
        
        if (targetRooms < minRooms || targetRooms > maxRooms) {
            errors.add("Target rooms must be between min and max rooms");
        }
        
        if (maxDepth < 1) {
            errors.add("Maximum depth must be at least 1");
        }
        
        if (branchingFactor < 0.0 || branchingFactor > 5.0) {
            errors.add("Branching factor must be between 0.0 and 5.0");
        }
        
        if (targetDifficulty < 1 || targetDifficulty > 10) {
            errors.add("Target difficulty must be between 1 and 10");
        }
        
        if (maxRetries < 1) {
            errors.add("Max retries must be at least 1");
        }
        
        if (roomSpacing < 0) {
            errors.add("Room spacing must be non-negative");
        }
        
        // Check for conflicting tags
        Set<String> intersection = new HashSet<>(requiredTags);
        intersection.retainAll(forbiddenTags);
        if (!intersection.isEmpty()) {
            errors.add("Tags cannot be both required and forbidden: " + intersection);
        }
        
        return errors;
    }
    
    /**
     * Creates a new builder.
     * 
     * @return new builder instance
     */
    @NotNull
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * Creates a builder from existing constraints.
     * 
     * @param constraints the constraints to copy
     * @return new builder with copied values
     */
    @NotNull
    public static Builder builder(@NotNull GenerationConstraints constraints) {
        return new Builder()
            .minRooms(constraints.minRooms)
            .maxRooms(constraints.maxRooms)
            .targetRooms(constraints.targetRooms)
            .maxDepth(constraints.maxDepth)
            .branchingFactor(constraints.branchingFactor)
            .targetDifficulty(constraints.targetDifficulty)
            .requiredTags(constraints.requiredTags)
            .forbiddenTags(constraints.forbiddenTags)
            .seed(constraints.seed)
            .allowOverlaps(constraints.allowOverlaps)
            .maxRetries(constraints.maxRetries)
            .roomSpacing(constraints.roomSpacing);
    }
    
    /**
     * Builder for generation constraints.
     */
    public static final class Builder {
        private int minRooms = 3;
        private int maxRooms = 15;
        private int targetRooms = 8;
        private int maxDepth = 6;
        private double branchingFactor = 1.2;
        private int targetDifficulty = 5;
        private final Set<String> requiredTags = new HashSet<>();
        private final Set<String> forbiddenTags = new HashSet<>();
        private long seed = System.currentTimeMillis();
        private boolean allowOverlaps = false;
        private int maxRetries = 100;
        private int roomSpacing = 2;
        
        private Builder() {}
        
        /**
         * Sets the minimum number of rooms.
         * 
         * @param minRooms minimum rooms
         * @return this builder
         */
        @NotNull
        public Builder minRooms(int minRooms) {
            this.minRooms = minRooms;
            return this;
        }
        
        /**
         * Sets the maximum number of rooms.
         * 
         * @param maxRooms maximum rooms
         * @return this builder
         */
        @NotNull
        public Builder maxRooms(int maxRooms) {
            this.maxRooms = maxRooms;
            return this;
        }
        
        /**
         * Sets the target number of rooms.
         * 
         * @param targetRooms target rooms
         * @return this builder
         */
        @NotNull
        public Builder targetRooms(int targetRooms) {
            this.targetRooms = targetRooms;
            return this;
        }
        
        /**
         * Sets the maximum depth.
         * 
         * @param maxDepth maximum depth
         * @return this builder
         */
        @NotNull
        public Builder maxDepth(int maxDepth) {
            this.maxDepth = maxDepth;
            return this;
        }
        
        /**
         * Sets the branching factor.
         * 
         * @param branchingFactor branching factor
         * @return this builder
         */
        @NotNull
        public Builder branchingFactor(double branchingFactor) {
            this.branchingFactor = branchingFactor;
            return this;
        }
        
        /**
         * Sets the target difficulty.
         * 
         * @param targetDifficulty target difficulty
         * @return this builder
         */
        @NotNull
        public Builder targetDifficulty(int targetDifficulty) {
            this.targetDifficulty = targetDifficulty;
            return this;
        }
        
        /**
         * Adds a required tag.
         * 
         * @param tag the tag
         * @return this builder
         */
        @NotNull
        public Builder requireTag(@NotNull String tag) {
            this.requiredTags.add(tag);
            return this;
        }
        
        /**
         * Adds required tags.
         * 
         * @param tags the tags
         * @return this builder
         */
        @NotNull
        public Builder requiredTags(@NotNull Collection<String> tags) {
            this.requiredTags.addAll(tags);
            return this;
        }
        
        /**
         * Adds a forbidden tag.
         * 
         * @param tag the tag
         * @return this builder
         */
        @NotNull
        public Builder forbidTag(@NotNull String tag) {
            this.forbiddenTags.add(tag);
            return this;
        }
        
        /**
         * Adds forbidden tags.
         * 
         * @param tags the tags
         * @return this builder
         */
        @NotNull
        public Builder forbiddenTags(@NotNull Collection<String> tags) {
            this.forbiddenTags.addAll(tags);
            return this;
        }
        
        /**
         * Sets the generation seed.
         * 
         * @param seed the seed
         * @return this builder
         */
        @NotNull
        public Builder seed(long seed) {
            this.seed = seed;
            return this;
        }
        
        /**
         * Sets whether overlaps are allowed.
         * 
         * @param allowOverlaps allow overlaps
         * @return this builder
         */
        @NotNull
        public Builder allowOverlaps(boolean allowOverlaps) {
            this.allowOverlaps = allowOverlaps;
            return this;
        }
        
        /**
         * Sets the maximum retries.
         * 
         * @param maxRetries max retries
         * @return this builder
         */
        @NotNull
        public Builder maxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
            return this;
        }
        
        /**
         * Sets the room spacing.
         * 
         * @param roomSpacing room spacing
         * @return this builder
         */
        @NotNull
        public Builder roomSpacing(int roomSpacing) {
            this.roomSpacing = roomSpacing;
            return this;
        }
        
        /**
         * Builds the constraints.
         * 
         * @return new constraints instance
         * @throws IllegalArgumentException if constraints are invalid
         */
        @NotNull
        public GenerationConstraints build() {
            GenerationConstraints constraints = new GenerationConstraints(this);
            
            List<String> errors = constraints.validate();
            if (!errors.isEmpty()) {
                throw new IllegalArgumentException("Invalid constraints: " + String.join(", ", errors));
            }
            
            return constraints;
        }
    }
    
    @Override
    public String toString() {
        return "GenerationConstraints{" +
               "rooms=" + minRooms + "-" + maxRooms + " (target=" + targetRooms + ")" +
               ", maxDepth=" + maxDepth +
               ", branchingFactor=" + branchingFactor +
               ", difficulty=" + targetDifficulty +
               ", seed=" + seed +
               '}';
    }
}

package com.ultimatedungeon.udx.gui.menus;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.gui.Menu;
import com.ultimatedungeon.udx.util.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;

import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * GUI menu for browsing and selecting dungeons to queue for.
 * 
 * <p>This menu displays available dungeons with working click handlers
 * and functional queue system.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class DungeonBrowserMenu extends Menu {
    
    private final UltimateDungeonX plugin;
    
    public DungeonBrowserMenu(@NotNull UltimateDungeonX plugin, @NotNull Player player) {
        super(player, Component.text("Dungeon Browser").color(NamedTextColor.DARK_PURPLE), 54, plugin.getMenuRegistry());
        this.plugin = plugin;
    }
    
    @Override
    protected void setupMenu() {
        // Sample dungeons with working functionality
        setItem(10, createDungeonItem("Crypt of Echoes", "An ancient burial ground filled with restless spirits", 
                Material.SKELETON_SKULL, "crypt", "undead", 5, 8), 
                clickType -> handleDungeonClick("Crypt of Echoes", clickType));
        
        setItem(12, createDungeonItem("Ember Foundry", "A blazing forge where fire elementals dwell", 
                Material.BLAZE_POWDER, "fire", "nether", 6, 10),
                clickType -> handleDungeonClick("Ember Foundry", clickType));
        
        setItem(14, createDungeonItem("Skyfane Spire", "A towering structure reaching into the clouds", 
                Material.FEATHER, "sky", "air", 8, 12),
                clickType -> handleDungeonClick("Skyfane Spire", clickType));
        
        setItem(16, createDungeonItem("Crystal Caverns", "Deep caves filled with magical crystals", 
                Material.AMETHYST_SHARD, "crystal", "magic", 4, 7),
                clickType -> handleDungeonClick("Crystal Caverns", clickType));
        
        setItem(28, createDungeonItem("Shadow Realm", "A dark dimension of nightmares", 
                Material.OBSIDIAN, "shadow", "dark", 10, 15),
                clickType -> handleDungeonClick("Shadow Realm", clickType));
        
        setItem(30, createDungeonItem("Frozen Wastes", "An icy wasteland of eternal winter", 
                Material.ICE, "ice", "cold", 7, 11),
                clickType -> handleDungeonClick("Frozen Wastes", clickType));
        
        setItem(32, createDungeonItem("Jungle Temple", "Ancient ruins hidden in dense jungle", 
                Material.MOSSY_COBBLESTONE, "jungle", "nature", 6, 9),
                clickType -> handleDungeonClick("Jungle Temple", clickType));
        
        setItem(34, createDungeonItem("Void Fortress", "A fortress floating in the endless void", 
                Material.END_STONE, "void", "end", 12, 18),
                clickType -> handleDungeonClick("Void Fortress", clickType));
        
        // Back button
        setItem(49, new ItemBuilder(Material.BARRIER)
            .name(Component.text("Back to Main Menu").color(NamedTextColor.RED))
            .lore(List.of(Component.text("Click to return").color(NamedTextColor.GRAY)))
            .build(), clickType -> {
                if (clickType.isLeftClick()) {
                    plugin.getMenuRegistry().openMainMenu(player);
                    player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                }
            });
        
        // Fill empty slots with glass panes
        fillEmptySlots();
    }
    
    private ItemStack createDungeonItem(String name, String description, Material material, 
                                       String theme1, String theme2, int minRooms, int maxRooms) {
        return new ItemBuilder(material)
            .name(Component.text(name).color(NamedTextColor.WHITE).decoration(TextDecoration.BOLD, true))
            .lore(List.of(
                Component.text(description).color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Length: " + minRooms + "-" + maxRooms + " rooms").color(NamedTextColor.AQUA),
                Component.text("Theme: " + theme1 + ", " + theme2).color(NamedTextColor.GREEN),
                Component.empty(),
                Component.text("Difficulty: Normal").color(NamedTextColor.YELLOW),
                Component.text("Reward Multiplier: +0%").color(NamedTextColor.GOLD),
                Component.empty(),
                Component.text("Left Click: Queue for dungeon").color(NamedTextColor.GREEN),
                Component.text("Right Click: View details").color(NamedTextColor.YELLOW)
            ))
            .glow(true)
            .build();
    }
    

    
    private void queueForDungeon(@NotNull Player player, @NotNull String dungeonName) {
        // Open queue menu
        plugin.getMenuRegistry().openMenu(player, new QueueMenu(plugin, player));
        player.sendMessage(Component.text("Queuing for " + dungeonName + "...").color(NamedTextColor.GREEN));
        player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
    }
    
    private void handleDungeonClick(@NotNull String dungeonName, @NotNull ClickType clickType) {
        if (clickType.isLeftClick()) {
            // Queue for dungeon
            queueForDungeon(player, dungeonName);
        } else if (clickType.isRightClick()) {
            // Show dungeon details
            showDungeonDetails(player, dungeonName);
        }
    }
    
    private void showDungeonDetails(@NotNull Player player, @NotNull String dungeonName) {
        player.sendMessage(Component.text("=== " + dungeonName + " Details ===").color(NamedTextColor.GOLD));
        player.sendMessage(Component.text("This dungeon offers challenging encounters and valuable rewards!").color(NamedTextColor.YELLOW));
        player.sendMessage(Component.text("Use left-click to queue for this dungeon.").color(NamedTextColor.GREEN));
        player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
    }
}

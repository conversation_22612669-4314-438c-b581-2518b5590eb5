package com.ultimatedungeon.udx.affix;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.Map;

/**
 * Represents a dungeon affix that modifies gameplay mechanics.
 * 
 * <p>Affixes are temporary modifiers that can be applied to dungeons
 * to change mob behavior, environmental effects, or loot rewards.
 * They are typically rotated daily or weekly to provide variety.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class Affix {
    
    private final String id;
    private final String name;
    private final String description;
    private final AffixType type;
    private final AffixRarity rarity;
    private final String iconMaterial;
    private final List<String> effects;
    private final Map<String, Object> parameters;
    private final double lootMultiplier;
    private final double experienceMultiplier;
    private final int difficultyModifier;
    
    /**
     * Creates a new affix.
     * 
     * @param id the unique affix ID
     * @param name the display name
     * @param description the description
     * @param type the affix type
     * @param rarity the affix rarity
     * @param iconMaterial the icon material
     * @param effects the list of effects
     * @param parameters the affix parameters
     * @param lootMultiplier the loot reward multiplier
     * @param experienceMultiplier the experience multiplier
     * @param difficultyModifier the difficulty modifier
     */
    public Affix(@NotNull String id, @NotNull String name, @NotNull String description,
                 @NotNull AffixType type, @NotNull AffixRarity rarity, @NotNull String iconMaterial,
                 @NotNull List<String> effects, @NotNull Map<String, Object> parameters,
                 double lootMultiplier, double experienceMultiplier, int difficultyModifier) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.type = type;
        this.rarity = rarity;
        this.iconMaterial = iconMaterial;
        this.effects = effects;
        this.parameters = parameters;
        this.lootMultiplier = lootMultiplier;
        this.experienceMultiplier = experienceMultiplier;
        this.difficultyModifier = difficultyModifier;
    }
    
    /**
     * Gets the affix ID.
     * 
     * @return the affix ID
     */
    @NotNull
    public String getId() {
        return id;
    }
    
    /**
     * Gets the display name.
     * 
     * @return the display name
     */
    @NotNull
    public String getName() {
        return name;
    }
    
    /**
     * Gets the description.
     * 
     * @return the description
     */
    @NotNull
    public String getDescription() {
        return description;
    }
    
    /**
     * Gets the affix type.
     * 
     * @return the affix type
     */
    @NotNull
    public AffixType getType() {
        return type;
    }
    
    /**
     * Gets the affix rarity.
     * 
     * @return the affix rarity
     */
    @NotNull
    public AffixRarity getRarity() {
        return rarity;
    }
    
    /**
     * Gets the icon material.
     * 
     * @return the icon material
     */
    @NotNull
    public String getIconMaterial() {
        return iconMaterial;
    }
    
    /**
     * Gets the list of effects.
     * 
     * @return the effects list
     */
    @NotNull
    public List<String> getEffects() {
        return effects;
    }
    
    /**
     * Gets the affix parameters.
     * 
     * @return the parameters map
     */
    @NotNull
    public Map<String, Object> getParameters() {
        return parameters;
    }
    
    /**
     * Gets a parameter value.
     * 
     * @param key the parameter key
     * @return the parameter value, or null if not found
     */
    @Nullable
    public Object getParameter(@NotNull String key) {
        return parameters.get(key);
    }
    
    /**
     * Gets a parameter value as a string.
     * 
     * @param key the parameter key
     * @param defaultValue the default value
     * @return the parameter value as string
     */
    @NotNull
    public String getParameterAsString(@NotNull String key, @NotNull String defaultValue) {
        Object value = parameters.get(key);
        return value != null ? value.toString() : defaultValue;
    }
    
    /**
     * Gets a parameter value as an integer.
     * 
     * @param key the parameter key
     * @param defaultValue the default value
     * @return the parameter value as integer
     */
    public int getParameterAsInt(@NotNull String key, int defaultValue) {
        Object value = parameters.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return defaultValue;
    }
    
    /**
     * Gets a parameter value as a double.
     * 
     * @param key the parameter key
     * @param defaultValue the default value
     * @return the parameter value as double
     */
    public double getParameterAsDouble(@NotNull String key, double defaultValue) {
        Object value = parameters.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return defaultValue;
    }
    
    /**
     * Gets a parameter value as a boolean.
     * 
     * @param key the parameter key
     * @param defaultValue the default value
     * @return the parameter value as boolean
     */
    public boolean getParameterAsBoolean(@NotNull String key, boolean defaultValue) {
        Object value = parameters.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return defaultValue;
    }
    
    /**
     * Gets the loot multiplier.
     * 
     * @return the loot multiplier
     */
    public double getLootMultiplier() {
        return lootMultiplier;
    }
    
    /**
     * Gets the experience multiplier.
     * 
     * @return the experience multiplier
     */
    public double getExperienceMultiplier() {
        return experienceMultiplier;
    }
    
    /**
     * Gets the difficulty modifier.
     * 
     * @return the difficulty modifier
     */
    public int getDifficultyModifier() {
        return difficultyModifier;
    }
    
    /**
     * Checks if this affix has a specific effect.
     * 
     * @param effect the effect to check
     * @return true if the affix has the effect
     */
    public boolean hasEffect(@NotNull String effect) {
        return effects.contains(effect);
    }
    
    /**
     * Gets the formatted display name with rarity color.
     * 
     * @return the formatted display name
     */
    @NotNull
    public String getFormattedName() {
        return rarity.getColorCode() + name;
    }
    
    /**
     * Gets the formatted description with effects.
     * 
     * @return the formatted description
     */
    @NotNull
    public String getFormattedDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append("§7").append(description);
        
        if (!effects.isEmpty()) {
            sb.append("\n§8Effects:");
            for (String effect : effects) {
                sb.append("\n§8• ").append(effect);
            }
        }
        
        if (lootMultiplier != 1.0) {
            sb.append("\n§6Loot: ").append(String.format("%.0f%%", lootMultiplier * 100));
        }
        
        if (experienceMultiplier != 1.0) {
            sb.append("\n§b Experience: ").append(String.format("%.0f%%", experienceMultiplier * 100));
        }
        
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return "Affix{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", type=" + type +
                ", rarity=" + rarity +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Affix affix = (Affix) obj;
        return id.equals(affix.id);
    }
    
    @Override
    public int hashCode() {
        return id.hashCode();
    }
}

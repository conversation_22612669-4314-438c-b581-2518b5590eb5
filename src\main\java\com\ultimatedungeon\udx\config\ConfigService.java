package com.ultimatedungeon.udx.config;

import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.List;
import java.util.logging.Level;

/**
 * Service for managing plugin configuration with versioning and auto-migration.
 * 
 * <p>This service handles loading, saving, and migrating configuration files.
 * It supports versioned configurations with automatic migration between versions.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class ConfigService {
    
    private static final String CONFIG_FILE = "config.yml";
    private static final int CURRENT_CONFIG_VERSION = 1;
    
    private final Plugin plugin;
    private final File dataFolder;
    private final File configFile;
    
    private FileConfiguration config;
    private boolean firstRun = false;
    
    public ConfigService(@NotNull Plugin plugin) {
        this.plugin = plugin;
        this.dataFolder = plugin.getDataFolder();
        this.configFile = new File(dataFolder, CONFIG_FILE);
    }
    
    /**
     * Loads the configuration from file, creating defaults if necessary.
     */
    public void loadConfig() {
        // Ensure data folder exists
        if (!dataFolder.exists()) {
            dataFolder.mkdirs();
            firstRun = true;
        }
        
        // Create default config if it doesn't exist
        if (!configFile.exists()) {
            createDefaultConfig();
            firstRun = true;
        }
        
        // Load configuration
        config = YamlConfiguration.loadConfiguration(configFile);
        
        // Check version and migrate if necessary
        int configVersion = config.getInt("config-version", 0);
        if (configVersion < CURRENT_CONFIG_VERSION) {
            migrateConfig(configVersion);
        }
        
        plugin.getLogger().info("Configuration loaded (version " + CURRENT_CONFIG_VERSION + ")");
    }
    
    /**
     * Creates the default configuration file.
     */
    private void createDefaultConfig() {
        try {
            // Copy default config from resources
            InputStream defaultConfig = plugin.getResource(CONFIG_FILE);
            if (defaultConfig != null) {
                Files.copy(defaultConfig, configFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                defaultConfig.close();
            } else {
                // Create minimal config if resource doesn't exist
                createMinimalConfig();
            }
            
            plugin.getLogger().info("Created default configuration file");
            
        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to create default config", e);
            createMinimalConfig();
        }
    }
    
    /**
     * Creates a minimal configuration programmatically.
     */
    private void createMinimalConfig() {
        config = new YamlConfiguration();
        
        // Version
        config.set("config-version", CURRENT_CONFIG_VERSION);
        
        // General settings
        config.set("general.build-world", "udx_build");
        config.set("general.max-instances", 50);
        config.set("general.instance-timeout-minutes", 60);
        config.set("general.debug-mode", false);
        
        // Performance settings
        config.set("performance.blocks-per-tick", 4096);
        config.set("performance.max-entities-per-instance", 200);
        config.set("performance.async-generation", true);
        config.set("performance.chunk-preload-radius", 2);
        
        // Database settings
        config.set("database.type", "sqlite");
        config.set("database.sqlite.file", "players.db");
        config.set("database.mysql.host", "localhost");
        config.set("database.mysql.port", 3306);
        config.set("database.mysql.database", "udx");
        config.set("database.mysql.username", "udx");
        config.set("database.mysql.password", "password");
        
        // GUI settings
        config.set("gui.sounds-enabled", true);
        config.set("gui.particles-enabled", true);
        config.set("gui.animations-enabled", true);
        config.set("gui.menu-size", 54);
        
        // Party settings
        config.set("party.max-size", 5);
        config.set("party.invite-timeout-seconds", 30);
        config.set("party.ready-check-timeout-seconds", 15);
        
        // Instance settings
        config.set("instances.protection.block-break", false);
        config.set("instances.protection.block-place", false);
        config.set("instances.protection.pvp", false);
        config.set("instances.protection.mob-griefing", false);
        config.set("instances.protection.fire-spread", false);
        config.set("instances.protection.liquid-flow", false);
        
        // Dungeon settings
        config.set("dungeons.default-difficulty", "NORMAL");
        config.set("dungeons.max-length", 15);
        config.set("dungeons.max-branching", 2);
        config.set("dungeons.generation-timeout-seconds", 30);
        
        // Mob settings
        config.set("mobs.max-per-spawner", 10);
        config.set("mobs.despawn-on-chunk-unload", true);
        config.set("mobs.leash-radius", 32);
        config.set("mobs.ability-cooldown-seconds", 5);
        
        // Boss settings
        config.set("bosses.bossbar-enabled", true);
        config.set("bosses.telegraph-particles", true);
        config.set("bosses.phase-announcements", true);
        config.set("bosses.enrage-timer-minutes", 10);
        
        // Loot settings
        config.set("loot.chest-regeneration", true);
        config.set("loot.per-player-loot", true);
        config.set("loot.rarity-glow", true);
        config.set("loot.jackpot-chance", 0.01);
        
        // Affix settings
        config.set("affixes.rotation-hours", 24);
        config.set("affixes.max-per-dungeon", 3);
        config.set("affixes.difficulty-scaling", true);
        
        saveConfig();
    }
    
    /**
     * Migrates configuration from an older version.
     * 
     * @param fromVersion the version to migrate from
     */
    private void migrateConfig(int fromVersion) {
        plugin.getLogger().info("Migrating configuration from version " + fromVersion + " to " + CURRENT_CONFIG_VERSION);
        
        // Backup old config
        try {
            File backup = new File(dataFolder, "config.yml.backup.v" + fromVersion);
            Files.copy(configFile.toPath(), backup.toPath(), StandardCopyOption.REPLACE_EXISTING);
            plugin.getLogger().info("Backed up old configuration to " + backup.getName());
        } catch (IOException e) {
            plugin.getLogger().log(Level.WARNING, "Failed to backup old configuration", e);
        }
        
        // Perform migrations
        switch (fromVersion) {
            case 0:
                // Migration from version 0 (no version) to 1
                migrateFromV0ToV1();
                break;
            // Add more migration cases as needed
        }
        
        // Update version
        config.set("config-version", CURRENT_CONFIG_VERSION);
        saveConfig();
        
        plugin.getLogger().info("Configuration migration completed");
    }
    
    /**
     * Migrates from version 0 to version 1.
     */
    private void migrateFromV0ToV1() {
        // Add any missing keys with defaults
        if (!config.contains("performance.chunk-preload-radius")) {
            config.set("performance.chunk-preload-radius", 2);
        }
        if (!config.contains("affixes.rotation-hours")) {
            config.set("affixes.rotation-hours", 24);
        }
        // Add more migrations as needed
    }
    
    /**
     * Saves the configuration to file.
     */
    public void saveConfig() {
        try {
            config.save(configFile);
        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to save configuration", e);
        }
    }
    
    /**
     * Reloads the configuration from file.
     */
    public void reloadConfig() {
        config = YamlConfiguration.loadConfiguration(configFile);
        plugin.getLogger().info("Configuration reloaded");
    }
    
    // Getters for configuration values
    
    public boolean isFirstRun() {
        return firstRun;
    }
    
    public void setFirstRun(boolean firstRun) {
        this.firstRun = firstRun;
    }
    
    @NotNull
    public String getBuildWorld() {
        return config.getString("general.build-world", "udx_build");
    }
    
    public int getMaxInstances() {
        return config.getInt("general.max-instances", 50);
    }
    
    public int getInstanceTimeoutMinutes() {
        return config.getInt("general.instance-timeout-minutes", 60);
    }
    
    public boolean isDebugMode() {
        return config.getBoolean("general.debug-mode", false);
    }
    
    public int getBlocksPerTick() {
        return config.getInt("performance.blocks-per-tick", 4096);
    }
    
    public int getMaxEntitiesPerInstance() {
        return config.getInt("performance.max-entities-per-instance", 200);
    }
    
    public boolean isAsyncGeneration() {
        return config.getBoolean("performance.async-generation", true);
    }
    
    public int getChunkPreloadRadius() {
        return config.getInt("performance.chunk-preload-radius", 2);
    }
    
    @NotNull
    public String getDatabaseType() {
        return config.getString("database.type", "sqlite");
    }
    
    @NotNull
    public String getSqliteFile() {
        return config.getString("database.sqlite.file", "players.db");
    }
    
    @NotNull
    public String getMysqlHost() {
        return config.getString("database.mysql.host", "localhost");
    }
    
    public int getMysqlPort() {
        return config.getInt("database.mysql.port", 3306);
    }
    
    @NotNull
    public String getMysqlDatabase() {
        return config.getString("database.mysql.database", "udx");
    }
    
    @NotNull
    public String getMysqlUsername() {
        return config.getString("database.mysql.username", "udx");
    }
    
    @NotNull
    public String getMysqlPassword() {
        return config.getString("database.mysql.password", "password");
    }
    
    public boolean isSoundsEnabled() {
        return config.getBoolean("gui.sounds-enabled", true);
    }
    
    public boolean isParticlesEnabled() {
        return config.getBoolean("gui.particles-enabled", true);
    }
    
    public boolean isAnimationsEnabled() {
        return config.getBoolean("gui.animations-enabled", true);
    }
    
    public int getMenuSize() {
        return config.getInt("gui.menu-size", 54);
    }
    
    public int getMaxPartySize() {
        return config.getInt("party.max-size", 5);
    }
    
    public int getInviteTimeoutSeconds() {
        return config.getInt("party.invite-timeout-seconds", 30);
    }
    
    public int getReadyCheckTimeoutSeconds() {
        return config.getInt("party.ready-check-timeout-seconds", 15);
    }
    
    public boolean isBlockBreakProtected() {
        return !config.getBoolean("instances.protection.block-break", false);
    }
    
    public boolean isBlockPlaceProtected() {
        return !config.getBoolean("instances.protection.block-place", false);
    }
    
    public boolean isPvpEnabled() {
        return config.getBoolean("instances.protection.pvp", false);
    }
    
    public boolean isMobGriefingEnabled() {
        return config.getBoolean("instances.protection.mob-griefing", false);
    }
    
    public boolean isFireSpreadEnabled() {
        return config.getBoolean("instances.protection.fire-spread", false);
    }
    
    public boolean isLiquidFlowEnabled() {
        return config.getBoolean("instances.protection.liquid-flow", false);
    }
    
    @NotNull
    public String getDefaultDifficulty() {
        return config.getString("dungeons.default-difficulty", "NORMAL");
    }
    
    public int getMaxDungeonLength() {
        return config.getInt("dungeons.max-length", 15);
    }
    
    public int getMaxBranching() {
        return config.getInt("dungeons.max-branching", 2);
    }
    
    public int getGenerationTimeoutSeconds() {
        return config.getInt("dungeons.generation-timeout-seconds", 30);
    }
    
    public int getMaxMobsPerSpawner() {
        return config.getInt("mobs.max-per-spawner", 10);
    }
    
    public boolean isDespawnOnChunkUnload() {
        return config.getBoolean("mobs.despawn-on-chunk-unload", true);
    }
    
    public int getMobLeashRadius() {
        return config.getInt("mobs.leash-radius", 32);
    }
    
    public int getAbilityCooldownSeconds() {
        return config.getInt("mobs.ability-cooldown-seconds", 5);
    }
    
    public boolean isBossbarEnabled() {
        return config.getBoolean("bosses.bossbar-enabled", true);
    }
    
    public boolean isTelegraphParticles() {
        return config.getBoolean("bosses.telegraph-particles", true);
    }
    
    public boolean isPhaseAnnouncements() {
        return config.getBoolean("bosses.phase-announcements", true);
    }
    
    public int getEnrageTimerMinutes() {
        return config.getInt("bosses.enrage-timer-minutes", 10);
    }
    
    public boolean isChestRegeneration() {
        return config.getBoolean("loot.chest-regeneration", true);
    }
    
    public boolean isPerPlayerLoot() {
        return config.getBoolean("loot.per-player-loot", true);
    }
    
    public boolean isRarityGlow() {
        return config.getBoolean("loot.rarity-glow", true);
    }
    
    public double getJackpotChance() {
        return config.getDouble("loot.jackpot-chance", 0.01);
    }
    
    public int getAffixRotationHours() {
        return config.getInt("affixes.rotation-hours", 24);
    }
    
    public int getMaxAffixesPerDungeon() {
        return config.getInt("affixes.max-per-dungeon", 3);
    }
    
    public boolean isAffixDifficultyScaling() {
        return config.getBoolean("affixes.difficulty-scaling", true);
    }
    
    // Generic getters
    
    @Nullable
    public String getString(@NotNull String path) {
        return config.getString(path);
    }
    
    @NotNull
    public String getString(@NotNull String path, @NotNull String defaultValue) {
        return config.getString(path, defaultValue);
    }
    
    public int getInt(@NotNull String path) {
        return config.getInt(path);
    }
    
    public int getInt(@NotNull String path, int defaultValue) {
        return config.getInt(path, defaultValue);
    }
    
    public boolean getBoolean(@NotNull String path) {
        return config.getBoolean(path);
    }
    
    public boolean getBoolean(@NotNull String path, boolean defaultValue) {
        return config.getBoolean(path, defaultValue);
    }
    
    public double getDouble(@NotNull String path) {
        return config.getDouble(path);
    }
    
    public double getDouble(@NotNull String path, double defaultValue) {
        return config.getDouble(path, defaultValue);
    }
    
    @Nullable
    public List<String> getStringList(@NotNull String path) {
        return config.getStringList(path);
    }
    
    public void set(@NotNull String path, @Nullable Object value) {
        config.set(path, value);
    }
}

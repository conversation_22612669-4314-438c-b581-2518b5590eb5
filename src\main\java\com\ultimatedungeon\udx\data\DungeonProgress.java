package com.ultimatedungeon.udx.data;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

/**
 * Immutable record representing a player's progress in a specific dungeon and difficulty.
 * 
 * <p>This record tracks completion status, best times, statistics, and unlock status
 * for a player's progress in a particular dungeon configuration.</p>
 * 
 * @param playerUuid the player's unique identifier
 * @param dungeonId the dungeon identifier
 * @param difficulty the difficulty level
 * @param completed whether the dungeon has been completed
 * @param bestTime the best completion time in milliseconds (null if never completed)
 * @param completions total number of completions
 * @param deaths total deaths in this dungeon/difficulty
 * @param lastAttempt timestamp of last attempt (null if never attempted)
 * @param unlockedAt timestamp when this dungeon/difficulty was unlocked
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public record DungeonProgress(
    @NotNull UUID playerUuid,
    @NotNull String dungeonId,
    @NotNull String difficulty,
    boolean completed,
    @Nullable Long bestTime,
    int completions,
    int deaths,
    @Nullable Long lastAttempt,
    long unlockedAt
) {
    
    /**
     * Creates a DungeonProgress from a database ResultSet.
     * 
     * @param rs the ResultSet containing progress data
     * @return a new DungeonProgress instance
     * @throws SQLException if database access fails
     */
    @NotNull
    public static DungeonProgress fromResultSet(@NotNull ResultSet rs) throws SQLException {
        Long bestTime = rs.getLong("best_time");
        if (rs.wasNull()) {
            bestTime = null;
        }
        
        Long lastAttempt = rs.getLong("last_attempt");
        if (rs.wasNull()) {
            lastAttempt = null;
        }
        
        return new DungeonProgress(
            UUID.fromString(rs.getString("player_uuid")),
            rs.getString("dungeon_id"),
            rs.getString("difficulty"),
            rs.getBoolean("completed"),
            bestTime,
            rs.getInt("completions"),
            rs.getInt("deaths"),
            lastAttempt,
            rs.getLong("unlocked_at")
        );
    }
    
    /**
     * Creates a new DungeonProgress with completion data.
     * 
     * @param completionTime the completion time in milliseconds
     * @return a new DungeonProgress instance
     */
    @NotNull
    public DungeonProgress withCompletion(long completionTime) {
        Long newBestTime = bestTime;
        if (newBestTime == null || completionTime < newBestTime) {
            newBestTime = completionTime;
        }
        
        return new DungeonProgress(
            playerUuid,
            dungeonId,
            difficulty,
            true,
            newBestTime,
            completions + 1,
            deaths,
            System.currentTimeMillis(),
            unlockedAt
        );
    }
    
    /**
     * Creates a new DungeonProgress with an additional death.
     * 
     * @return a new DungeonProgress instance
     */
    @NotNull
    public DungeonProgress withDeath() {
        return new DungeonProgress(
            playerUuid,
            dungeonId,
            difficulty,
            completed,
            bestTime,
            completions,
            deaths + 1,
            System.currentTimeMillis(),
            unlockedAt
        );
    }
    
    /**
     * Creates a new DungeonProgress with updated last attempt.
     * 
     * @return a new DungeonProgress instance
     */
    @NotNull
    public DungeonProgress withAttempt() {
        return withAttempt(System.currentTimeMillis());
    }
    
    /**
     * Creates a new DungeonProgress with updated last attempt.
     * 
     * @param timestamp the attempt timestamp
     * @return a new DungeonProgress instance
     */
    @NotNull
    public DungeonProgress withAttempt(long timestamp) {
        return new DungeonProgress(
            playerUuid,
            dungeonId,
            difficulty,
            completed,
            bestTime,
            completions,
            deaths,
            timestamp,
            unlockedAt
        );
    }
    
    /**
     * Creates a new DungeonProgress with reset statistics (for new season).
     * 
     * @return a new DungeonProgress instance
     */
    @NotNull
    public DungeonProgress withResetStats() {
        return new DungeonProgress(
            playerUuid,
            dungeonId,
            difficulty,
            false,
            null,
            0,
            0,
            null,
            unlockedAt
        );
    }
    
    /**
     * Gets the success rate as a percentage.
     * 
     * @return success rate (0.0 to 100.0), or 0.0 if no attempts
     */
    public double getSuccessRate() {
        if (completions == 0 && deaths == 0) {
            return 0.0;
        }
        int totalAttempts = completions + deaths;
        return (double) completions / totalAttempts * 100.0;
    }
    
    /**
     * Gets the average deaths per completion.
     * 
     * @return average deaths per completion, or 0.0 if no completions
     */
    public double getAverageDeathsPerCompletion() {
        if (completions == 0) {
            return 0.0;
        }
        return (double) deaths / completions;
    }
    
    /**
     * Checks if this dungeon has been attempted recently (within last 24 hours).
     * 
     * @return true if attempted recently, false otherwise
     */
    public boolean isRecentlyAttempted() {
        if (lastAttempt == null) {
            return false;
        }
        long twentyFourHoursAgo = System.currentTimeMillis() - (24 * 60 * 60 * 1000);
        return lastAttempt > twentyFourHoursAgo;
    }
    
    /**
     * Checks if this is a newly unlocked dungeon (unlocked within last hour).
     * 
     * @return true if newly unlocked, false otherwise
     */
    public boolean isNewlyUnlocked() {
        long oneHourAgo = System.currentTimeMillis() - (60 * 60 * 1000);
        return unlockedAt > oneHourAgo;
    }
    
    /**
     * Gets a formatted best time string.
     * 
     * @return formatted best time, or "Never" if no completion
     */
    @NotNull
    public String getFormattedBestTime() {
        if (bestTime == null) {
            return "Never";
        }
        
        long seconds = bestTime / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        
        if (hours > 0) {
            return String.format("%d:%02d:%02d", hours, minutes % 60, seconds % 60);
        } else {
            return String.format("%d:%02d", minutes, seconds % 60);
        }
    }
    
    /**
     * Gets a formatted success rate string.
     * 
     * @return formatted success rate with percentage
     */
    @NotNull
    public String getFormattedSuccessRate() {
        return String.format("%.1f%%", getSuccessRate());
    }
    
    /**
     * Gets the total attempts (completions + deaths).
     * 
     * @return total number of attempts
     */
    public int getTotalAttempts() {
        return completions + deaths;
    }
    
    /**
     * Checks if the player has any experience with this dungeon.
     * 
     * @return true if player has attempted this dungeon, false otherwise
     */
    public boolean hasExperience() {
        return getTotalAttempts() > 0;
    }
    
    /**
     * Gets a difficulty-based rank for this progress.
     * 
     * @return rank based on completions and best time
     */
    @NotNull
    public ProgressRank getRank() {
        if (!completed) {
            return ProgressRank.UNRANKED;
        }
        
        if (completions >= 50) {
            return ProgressRank.MASTER;
        } else if (completions >= 25) {
            return ProgressRank.EXPERT;
        } else if (completions >= 10) {
            return ProgressRank.VETERAN;
        } else if (completions >= 5) {
            return ProgressRank.EXPERIENCED;
        } else {
            return ProgressRank.NOVICE;
        }
    }
    
    /**
     * Enumeration of progress ranks.
     */
    public enum ProgressRank {
        UNRANKED("Unranked", "§7"),
        NOVICE("Novice", "§f"),
        EXPERIENCED("Experienced", "§a"),
        VETERAN("Veteran", "§b"),
        EXPERT("Expert", "§d"),
        MASTER("Master", "§6");
        
        private final String displayName;
        private final String color;
        
        ProgressRank(@NotNull String displayName, @NotNull String color) {
            this.displayName = displayName;
            this.color = color;
        }
        
        @NotNull
        public String getDisplayName() {
            return displayName;
        }
        
        @NotNull
        public String getColor() {
            return color;
        }
        
        @NotNull
        public String getColoredName() {
            return color + displayName;
        }
    }
    
    @Override
    public String toString() {
        return String.format("DungeonProgress{player=%s, dungeon='%s', difficulty='%s', completed=%s, completions=%d, deaths=%d}",
            playerUuid, dungeonId, difficulty, completed, completions, deaths);
    }
}

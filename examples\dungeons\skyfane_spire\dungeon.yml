# Skyfane Spire - Sample Dungeon Configuration
# A towering aerial fortress floating among the clouds, filled with wind elementals and sky pirates

id: "skyfane_spire"
name: "Skyfane Spire"
description: "A magnificent floating tower that pierces the clouds. Ancient wind magic keeps it aloft while sky pirates and air elementals battle for control of its treasures."

# Basic Properties
theme: "aerial"
environment: "sky"
biome: "THE_END"
difficulty_base: 7
length_min: 8
length_max: 12
branching_factor: 3

# Level Requirements
level_requirement:
  min: 40
  max: 80
  recommended: 55

# Party Configuration
party:
  min_size: 3
  max_size: 6
  recommended_size: 5
  scaling_enabled: true

# Room Configuration
rooms:
  start_room: "landing_platform"
  boss_room: "storm_lords_throne"
  available_rooms:
    - "wind_tunnel_corridor"
    - "sky_pirate_quarters"
    - "elemental_observatory"
    - "floating_gardens"
    - "crystal_wind_chamber"
    - "storm_generator_room"
    - "cloud_bridge"
    - "aerial_docking_bay"
    - "lightning_rod_platform"
    - "treasure_vault_suspended"

# Difficulty Scaling
difficulty_tiers:
  normal:
    mob_health_multiplier: 1.0
    mob_damage_multiplier: 1.0
    loot_multiplier: 1.0
    experience_multiplier: 1.0
    fall_damage_multiplier: 1.0
  hard:
    mob_health_multiplier: 1.7
    mob_damage_multiplier: 1.5
    loot_multiplier: 1.4
    experience_multiplier: 1.5
    fall_damage_multiplier: 1.3
  mythic:
    mob_health_multiplier: 2.5
    mob_damage_multiplier: 1.8
    loot_multiplier: 1.7
    experience_multiplier: 1.8
    fall_damage_multiplier: 1.5
  nightmare:
    mob_health_multiplier: 4.0
    mob_damage_multiplier: 2.5
    loot_multiplier: 2.5
    experience_multiplier: 2.5
    fall_damage_multiplier: 2.0

# Allowed Affixes
allowed_affixes:
  - "wind_walker"
  - "storm_caller"
  - "lightning_charged"
  - "aerial_superiority"
  - "cloud_step"
  - "thunder_strike"
  - "wind_barrier"
  - "sky_blessing"
  - "tempest_fury"

# Loot Configuration
loot_tables:
  common_chest: "spire_common_loot"
  rare_chest: "spire_rare_loot"
  boss_loot: "storm_lord_loot"
  completion_rewards: "spire_completion"
  sky_pirate_loot: "sky_pirate_treasure"

# Objectives
objectives:
  primary:
    - type: "kill_boss"
      target: "storm_lord"
      description: "Defeat the Storm Lord"
  secondary:
    - type: "collect_items"
      target: "wind_crystal"
      count: 7
      description: "Collect 7 Wind Crystals"
    - type: "survive_falls"
      max_falls: 3
      description: "Fall off platforms no more than 3 times"
    - type: "activate_mechanisms"
      target: "wind_generators"
      count: 4
      description: "Activate 4 Wind Generators"
    - type: "no_deaths"
      description: "Complete without any party member deaths"

# Environmental Effects
environment:
  lighting: "bright"
  weather: "clear"
  wind_effects: true
  fog_enabled: true
  fog_color: "#E6F3FF"
  fog_density: "light"
  ambient_sounds:
    - "ENTITY_PHANTOM_AMBIENT"
    - "WEATHER_RAIN"
    - "BLOCK_BEACON_AMBIENT"
  particle_effects:
    - type: "CLOUD"
      density: "medium"
      areas: ["platforms", "bridges"]
    - type: "END_ROD"
      density: "low"
      areas: ["crystal_chambers"]
    - type: "DRAGON_BREATH"
      density: "high"
      areas: ["wind_tunnels"]

# Environmental Hazards
hazards:
  - type: "wind_gust"
    knockback: 3.0
    interval: 80
    areas: ["exposed_platforms"]
    direction: "random"
  - type: "lightning_strike"
    damage: 12.0
    interval: 120
    areas: ["metal_structures"]
    effects:
      - type: "SLOWNESS"
        duration: 60
        amplifier: 1
  - type: "void_fall"
    damage: 999.0
    areas: ["platform_edges"]
    teleport_to_checkpoint: true
  - type: "storm_winds"
    damage: 3.0
    interval: 40
    areas: ["storm_chambers"]
    effects:
      - type: "LEVITATION"
        duration: 20
        amplifier: 0

# Unique Mechanics
mechanics:
  - type: "wind_currents"
    description: "Players can ride wind currents to reach higher platforms"
    activation: "step_on_wind_pad"
    duration: 100
    height_boost: 10
  - type: "storm_shields"
    description: "Activate storm shields to protect against lightning"
    required_items: ["redstone", "iron_ingot"]
    duration: 300
    protection_radius: 5
  - type: "cloud_walking"
    description: "Special boots allow walking on cloud blocks temporarily"
    item_required: "cloud_walker_boots"
    duration: 200
  - type: "aerial_combat"
    description: "Some fights take place on moving platforms"
    platform_speed: 0.5
    platform_patterns: ["circular", "figure_eight", "pendulum"]

# Checkpoints
checkpoints:
  - name: "first_platform"
    description: "First Platform Secured"
    position: "after_start_room"
    respawn_equipment:
      - "elytra"
      - "firework_rocket:10"
  - name: "mid_spire"
    description: "Mid-Spire Reached"
    position: "middle_rooms"
    respawn_equipment:
      - "elytra"
      - "firework_rocket:15"
  - name: "storm_chamber"
    description: "Storm Chamber Accessed"
    position: "before_boss"
    respawn_equipment:
      - "elytra"
      - "firework_rocket:20"

# Rewards
rewards:
  currency:
    base_amount: 200
    bonus_per_difficulty: 100
  experience:
    base_amount: 1000
    bonus_per_difficulty: 400
  titles:
    - condition: "first_completion"
      title: "Sky Walker"
    - condition: "nightmare_completion"
      title: "Storm Master"
    - condition: "no_falls"
      title: "Sure Footed"
    - condition: "speed_run_15min"
      title: "Wind Rider"
  achievements:
    - "spire_climber"
    - "wind_crystal_collector"
    - "storm_lord_vanquisher"
    - "aerial_ace"
    - "cloud_dancer"

# Flight System
flight_system:
  enabled: true
  elytra_provided: true
  firework_rockets_provided: true
  flight_zones:
    - "between_platforms"
    - "around_spire_exterior"
    - "storm_chamber_aerial_phase"
  no_fly_zones:
    - "boss_arena_ground_phase"
    - "puzzle_chambers"
  wind_boost_areas:
    - "updraft_columns"
    - "wind_tunnel_exits"

# Weather System
weather_system:
  enabled: true
  weather_patterns:
    - type: "clear"
      duration: 300
      visibility: "excellent"
    - type: "light_storm"
      duration: 200
      visibility: "good"
      lightning_chance: 0.1
    - type: "heavy_storm"
      duration: 100
      visibility: "poor"
      lightning_chance: 0.3
      wind_strength: 2.0

# Metadata
version: "1.0.0"
author: "UltimateDungeon Team"
created: "2024-01-01"
last_modified: "2024-01-01"

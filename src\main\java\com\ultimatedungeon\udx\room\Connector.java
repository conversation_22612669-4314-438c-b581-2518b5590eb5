package com.ultimatedungeon.udx.room;

import org.jetbrains.annotations.NotNull;

/**
 * Represents a connection point on a room template.
 * 
 * <p>Connectors define how rooms can be linked together during
 * procedural generation. They specify direction, size, and type.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public record Connector(
    @NotNull Direction direction,
    @NotNull ConnectorSize size,
    @NotNull ConnectorType type,
    int x,
    int y,
    int z
) {
    
    /**
     * Direction of the connector.
     */
    public enum Direction {
        NORTH(0, 0, -1),
        EAST(1, 0, 0),
        SOUTH(0, 0, 1),
        WEST(-1, 0, 0),
        UP(0, 1, 0),
        DOWN(0, -1, 0);
        
        private final int deltaX;
        private final int deltaY;
        private final int deltaZ;
        
        Direction(int deltaX, int deltaY, int deltaZ) {
            this.deltaX = deltaX;
            this.deltaY = deltaY;
            this.deltaZ = deltaZ;
        }
        
        public int getDeltaX() { return deltaX; }
        public int getDeltaY() { return deltaY; }
        public int getDeltaZ() { return deltaZ; }
        
        @NotNull
        public Direction getOpposite() {
            return switch (this) {
                case NORTH -> SOUTH;
                case EAST -> WEST;
                case SOUTH -> NORTH;
                case WEST -> EAST;
                case UP -> DOWN;
                case DOWN -> UP;
            };
        }
    }
    
    /**
     * Size of the connector opening.
     */
    public enum ConnectorSize {
        SMALL(1, 2),    // 1x2 opening
        MEDIUM(2, 3),   // 2x3 opening
        LARGE(3, 4),    // 3x4 opening
        HUGE(4, 5);     // 4x5 opening
        
        private final int width;
        private final int height;
        
        ConnectorSize(int width, int height) {
            this.width = width;
            this.height = height;
        }
        
        public int getWidth() { return width; }
        public int getHeight() { return height; }
    }
    
    /**
     * Type of connector for compatibility matching.
     */
    public enum ConnectorType {
        STANDARD,       // Normal room connections
        ENTRANCE,       // Dungeon entrance
        EXIT,           // Dungeon exit
        BOSS_ENTRANCE,  // Boss room entrance
        SECRET,         // Secret room connection
        VERTICAL        // Vertical connections (stairs, elevators)
    }
    
    /**
     * Checks if this connector is compatible with another.
     * 
     * @param other the other connector
     * @return true if compatible
     */
    public boolean isCompatibleWith(@NotNull Connector other) {
        // Must be opposite directions
        if (this.direction != other.direction.getOpposite()) {
            return false;
        }
        
        // Must be same size
        if (this.size != other.size) {
            return false;
        }
        
        // Type compatibility
        return isTypeCompatible(this.type, other.type);
    }
    
    /**
     * Checks if two connector types are compatible.
     */
    private boolean isTypeCompatible(@NotNull ConnectorType type1, @NotNull ConnectorType type2) {
        // Standard connectors are compatible with most types
        if (type1 == ConnectorType.STANDARD || type2 == ConnectorType.STANDARD) {
            return true;
        }
        
        // Specific type matching
        return switch (type1) {
            case ENTRANCE -> type2 == ConnectorType.ENTRANCE;
            case EXIT -> type2 == ConnectorType.EXIT;
            case BOSS_ENTRANCE -> type2 == ConnectorType.BOSS_ENTRANCE;
            case SECRET -> type2 == ConnectorType.SECRET;
            case VERTICAL -> type2 == ConnectorType.VERTICAL;
            default -> type1 == type2;
        };
    }
    
    /**
     * Gets the world position of this connector relative to a room position.
     * 
     * @param roomX room X coordinate
     * @param roomY room Y coordinate
     * @param roomZ room Z coordinate
     * @return world coordinates of the connector
     */
    @NotNull
    public int[] getWorldPosition(int roomX, int roomY, int roomZ) {
        return new int[]{
            roomX + x,
            roomY + y,
            roomZ + z
        };
    }
    
    @Override
    public String toString() {
        return String.format("Connector{%s %s %s at (%d,%d,%d)}", 
            direction, size, type, x, y, z);
    }
}

package com.ultimatedungeon.udx.gui.menus;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.gui.Menu;
import com.ultimatedungeon.udx.util.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

/**
 * Administrative hub menu for managing UltimateDungeonX features.
 * 
 * <p>This menu provides access to dungeon management, instance monitoring,
 * affix rotation control, and other administrative functions.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class AdminHubMenu extends Menu {
    
    private final UltimateDungeonX plugin;
    
    public AdminHubMenu(@NotNull UltimateDungeonX plugin, @NotNull Player player) {
        super(player, Component.text("Admin Hub").color(NamedTextColor.DARK_RED).decoration(TextDecoration.BOLD, true), 
              45, plugin.getMenuRegistry());
        this.plugin = plugin;
    }
    
    @Override
    protected void setupMenu() {
        // Clear inventory
        inventory.clear();
        clickHandlers.clear();
        
        // Create Dungeon
        ItemStack createDungeon = new ItemBuilder(Material.DIAMOND_PICKAXE)
            .name(Component.text("Create Dungeon").color(NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Create a new custom dungeon").color(NamedTextColor.GRAY),
                Component.text("Generates a superflat world for building").color(NamedTextColor.GRAY),
                Component.text("Use builder tools to add spawners & bosses").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to create new dungeon").color(NamedTextColor.YELLOW)
            )
            .glow()
            .build();
        
        setItem(10, createDungeon, clickType -> {
            if (clickType == ClickType.LEFT) {
                menuRegistry.openCreateDungeonMenu(player);
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
            }
        });
        
        // Instance Monitoring
        ItemStack instanceMon = new ItemBuilder(Material.COMPASS)
            .name(Component.text("Instance Monitor").color(NamedTextColor.AQUA).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("View active dungeon instances").color(NamedTextColor.GRAY),
                Component.text("Monitor performance and players").color(NamedTextColor.GRAY),
                Component.text("Force stop or teleport to instances").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to monitor instances").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(12, instanceMon, clickType -> {
            if (clickType == ClickType.LEFT) {
                // TODO: Open instance monitor when implemented
                player.sendMessage(Component.text("Instance monitor coming soon!").color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        // Affix Rotation
        ItemStack affixRotation = new ItemBuilder(Material.CLOCK)
            .name(Component.text("Affix Rotation").color(NamedTextColor.LIGHT_PURPLE).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Manage daily/weekly affix rotations").color(NamedTextColor.GRAY),
                Component.text("Force new rotations or preview upcoming").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to manage affixes").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(14, affixRotation, clickType -> {
            if (clickType == ClickType.LEFT) {
                // TODO: Open affix rotation menu when implemented
                player.sendMessage(Component.text("Affix rotation management coming soon!").color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        // Room Editor
        ItemStack roomEditor = new ItemBuilder(Material.GOLDEN_AXE)
            .name(Component.text("Room Editor").color(NamedTextColor.YELLOW).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Create and edit dungeon rooms").color(NamedTextColor.GRAY),
                Component.text("Place markers and configure connectors").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to open room editor").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(16, roomEditor, clickType -> {
            if (clickType == ClickType.LEFT) {
                plugin.getMenuRegistry().openMenu(player, new RoomEditorMenu(plugin, player));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
            }
        });
        
        // Spawner Editor
        ItemStack spawnerEditor = new ItemBuilder(Material.SPAWNER)
            .name(Component.text("Spawner Editor").color(NamedTextColor.RED).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Configure mob spawners and waves").color(NamedTextColor.GRAY),
                Component.text("Set behaviors and abilities").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to edit spawners").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(19, spawnerEditor, clickType -> {
            if (clickType == ClickType.LEFT) {
                plugin.getMenuRegistry().openMenu(player, new SpawnerEditorMenu(plugin, player));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
            }
        });
        
        // Boss Editor
        ItemStack bossEditor = new ItemBuilder(Material.WITHER_SKELETON_SKULL)
            .name(Component.text("Boss Editor").color(NamedTextColor.DARK_PURPLE).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Design boss encounters and phases").color(NamedTextColor.GRAY),
                Component.text("Configure abilities and mechanics").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to edit bosses").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(21, bossEditor, clickType -> {
            if (clickType == ClickType.LEFT) {
                plugin.getMenuRegistry().openMenu(player, new BossEditorMenu(plugin, player));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
            }
        });
        
        // Loot Editor
        ItemStack lootEditor = new ItemBuilder(Material.DIAMOND)
            .name(Component.text("Loot Editor").color(NamedTextColor.BLUE).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Configure loot tables and rewards").color(NamedTextColor.GRAY),
                Component.text("Set rarities and drop chances").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to edit loot").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(23, lootEditor, clickType -> {
            if (clickType == ClickType.LEFT) {
                plugin.getMenuRegistry().openMenu(player, new LootEditorMenu(plugin, player));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
            }
        });
        
        // Debug Tools
        ItemStack debugTools = new ItemBuilder(Material.REDSTONE_TORCH)
            .name(Component.text("Debug Tools").color(NamedTextColor.RED).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Performance monitoring and debugging").color(NamedTextColor.GRAY),
                Component.text("View tick budgets and entity counts").color(NamedTextColor.GRAY),
                Component.text("Run self-tests and diagnostics").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to open debug tools").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(25, debugTools, clickType -> {
            if (clickType == ClickType.LEFT) {
                // TODO: Open debug tools when implemented
                player.sendMessage(Component.text("Debug tools coming soon!").color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        // Global Settings
        ItemStack globalSettings = new ItemBuilder(Material.COMMAND_BLOCK)
            .name(Component.text("Global Settings").color(NamedTextColor.DARK_GREEN).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Configure plugin-wide settings").color(NamedTextColor.GRAY),
                Component.text("Performance limits and features").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to configure settings").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(31, globalSettings, clickType -> {
            if (clickType == ClickType.LEFT) {
                // TODO: Open global settings when implemented
                player.sendMessage(Component.text("Global settings coming soon!").color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        // Back to main menu
        ItemStack backButton = new ItemBuilder(Material.BARRIER)
            .name(Component.text("Back to Main Menu").color(NamedTextColor.RED))
            .lore(Component.text("Return to the main menu").color(NamedTextColor.GRAY))
            .build();
        
        setItem(40, backButton, clickType -> {
            if (clickType == ClickType.LEFT) {
                new PlayerMainMenu(plugin, player).open();
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 0.8f);
            }
        });
        
        // Fill empty slots with glass panes
        fillEmptySlots();
    }
}

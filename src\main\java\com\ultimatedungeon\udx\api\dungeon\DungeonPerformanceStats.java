package com.ultimatedungeon.udx.api.dungeon;

import org.jetbrains.annotations.NotNull;

/**
 * Performance statistics for the dungeon system.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public record DungeonPerformanceStats(
    int activeInstances,
    int totalInstancesCreated,
    int totalInstancesCompleted,
    int totalInstancesFailed,
    long totalPlayersServed,
    double averageTickTime,
    double peakTickTime,
    long memoryUsageBytes,
    long peakMemoryUsageBytes,
    int entitiesSpawned,
    int blocksPlaced,
    double averageGenerationTimeMs,
    double averageInstanceStartTimeMs
) {
    
    /**
     * Creates new performance statistics.
     */
    public DungeonPerformanceStats {
        if (activeInstances < 0) {
            throw new IllegalArgumentException("Active instances cannot be negative");
        }
        if (totalInstancesCreated < 0) {
            throw new IllegalArgumentException("Total instances created cannot be negative");
        }
        if (totalInstancesCompleted < 0) {
            throw new IllegalArgumentException("Total instances completed cannot be negative");
        }
        if (totalInstancesFailed < 0) {
            throw new IllegalArgumentException("Total instances failed cannot be negative");
        }
        if (totalPlayersServed < 0) {
            throw new IllegalArgumentException("Total players served cannot be negative");
        }
        if (averageTickTime < 0) {
            throw new IllegalArgumentException("Average tick time cannot be negative");
        }
        if (peakTickTime < 0) {
            throw new IllegalArgumentException("Peak tick time cannot be negative");
        }
        if (memoryUsageBytes < 0) {
            throw new IllegalArgumentException("Memory usage cannot be negative");
        }
        if (peakMemoryUsageBytes < 0) {
            throw new IllegalArgumentException("Peak memory usage cannot be negative");
        }
        if (entitiesSpawned < 0) {
            throw new IllegalArgumentException("Entities spawned cannot be negative");
        }
        if (blocksPlaced < 0) {
            throw new IllegalArgumentException("Blocks placed cannot be negative");
        }
        if (averageGenerationTimeMs < 0) {
            throw new IllegalArgumentException("Average generation time cannot be negative");
        }
        if (averageInstanceStartTimeMs < 0) {
            throw new IllegalArgumentException("Average instance start time cannot be negative");
        }
    }
    
    /**
     * Gets the success rate as a percentage (0-100).
     * 
     * @return The success rate percentage
     */
    public double getSuccessRate() {
        int totalCompleted = totalInstancesCompleted + totalInstancesFailed;
        if (totalCompleted == 0) return 0.0;
        return (double) totalInstancesCompleted / totalCompleted * 100.0;
    }
    
    /**
     * Gets the failure rate as a percentage (0-100).
     * 
     * @return The failure rate percentage
     */
    public double getFailureRate() {
        return 100.0 - getSuccessRate();
    }
    
    /**
     * Gets the memory usage in megabytes.
     * 
     * @return Memory usage in MB
     */
    public double getMemoryUsageMB() {
        return memoryUsageBytes / (1024.0 * 1024.0);
    }
    
    /**
     * Gets the peak memory usage in megabytes.
     * 
     * @return Peak memory usage in MB
     */
    public double getPeakMemoryUsageMB() {
        return peakMemoryUsageBytes / (1024.0 * 1024.0);
    }
    
    /**
     * Checks if the system is performing well based on thresholds.
     * 
     * @return True if performance is within acceptable limits
     */
    public boolean isPerformingWell() {
        // Performance is considered good if:
        // - Average tick time is under 6ms (as per spec)
        // - Peak tick time is under 20ms
        // - Memory usage is reasonable
        return averageTickTime < 6.0 && 
               peakTickTime < 20.0 && 
               getMemoryUsageMB() < 1024.0; // Under 1GB
    }
    
    /**
     * Gets a formatted summary of the performance statistics.
     * 
     * @return Formatted performance summary
     */
    @NotNull
    public String getFormattedSummary() {
        return String.format(
            "Performance Stats: %d active instances, %.2fms avg tick, %.2f%% success rate, %.1fMB memory",
            activeInstances, averageTickTime, getSuccessRate(), getMemoryUsageMB()
        );
    }
}

package com.ultimatedungeon.udx.api.dungeon;

import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.UUID;

/**
 * Service interface for managing dungeons and instances.
 * 
 * <p>This interface provides methods for creating, managing, and querying
 * dungeon instances and definitions. It serves as the main API for addon
 * developers to interact with the dungeon system.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public interface DungeonService {
    
    /**
     * Gets all available dungeon definitions.
     * 
     * @return List of all dungeon definitions
     */
    @NotNull
    List<DungeonDefinition> getAllDungeons();
    
    /**
     * Gets a dungeon definition by ID.
     * 
     * @param dungeonId The dungeon ID
     * @return The dungeon definition, or null if not found
     */
    @Nullable
    DungeonDefinition getDungeon(@NotNull String dungeonId);
    
    /**
     * Gets all active dungeon instances.
     * 
     * @return List of all active instances
     */
    @NotNull
    List<DungeonInstance> getActiveInstances();
    
    /**
     * Gets a dungeon instance by ID.
     * 
     * @param instanceId The instance ID
     * @return The dungeon instance, or null if not found
     */
    @Nullable
    DungeonInstance getInstance(@NotNull UUID instanceId);
    
    /**
     * Gets the dungeon instance a player is currently in.
     * 
     * @param player The player
     * @return The dungeon instance, or null if not in a dungeon
     */
    @Nullable
    DungeonInstance getPlayerInstance(@NotNull Player player);
    
    /**
     * Creates a new dungeon instance.
     * 
     * @param dungeonId The dungeon ID to create an instance of
     * @param difficulty The difficulty tier
     * @param partyLeader The party leader who initiated the instance
     * @return The created instance, or null if creation failed
     */
    @Nullable
    DungeonInstance createInstance(@NotNull String dungeonId, @NotNull String difficulty, @NotNull Player partyLeader);
    
    /**
     * Starts a dungeon instance.
     * 
     * @param instanceId The instance ID
     * @param players The players to add to the instance
     * @return True if started successfully
     */
    boolean startInstance(@NotNull UUID instanceId, @NotNull List<Player> players);
    
    /**
     * Stops a dungeon instance.
     * 
     * @param instanceId The instance ID
     * @param reason The reason for stopping
     * @return True if stopped successfully
     */
    boolean stopInstance(@NotNull UUID instanceId, @NotNull String reason);
    
    /**
     * Adds a player to a dungeon instance.
     * 
     * @param instanceId The instance ID
     * @param player The player to add
     * @return True if added successfully
     */
    boolean addPlayerToInstance(@NotNull UUID instanceId, @NotNull Player player);
    
    /**
     * Removes a player from a dungeon instance.
     * 
     * @param instanceId The instance ID
     * @param player The player to remove
     * @return True if removed successfully
     */
    boolean removePlayerFromInstance(@NotNull UUID instanceId, @NotNull Player player);
    
    /**
     * Checks if a player can join a specific dungeon.
     * 
     * @param player The player
     * @param dungeonId The dungeon ID
     * @return True if the player can join
     */
    boolean canPlayerJoinDungeon(@NotNull Player player, @NotNull String dungeonId);
    
    /**
     * Gets the number of active instances for a dungeon.
     * 
     * @param dungeonId The dungeon ID
     * @return The number of active instances
     */
    int getActiveInstanceCount(@NotNull String dungeonId);
    
    /**
     * Gets performance statistics for the dungeon system.
     * 
     * @return Performance statistics
     */
    @NotNull
    DungeonPerformanceStats getPerformanceStats();
}

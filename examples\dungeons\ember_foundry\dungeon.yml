# Ember Foundry - Sample Dungeon Configuration
# A blazing industrial complex filled with fire elementals and molten hazards

id: "ember_foundry"
name: "Ember Foundry"
description: "An ancient dwarven foundry that has been overrun by fire elementals. Molten lava flows through the corridors, and the air shimmers with intense heat."

# Basic Properties
theme: "fire"
environment: "industrial"
biome: "NETHER_WASTES"
difficulty_base: 5
length_min: 6
length_max: 10
branching_factor: 2

# Level Requirements
level_requirement:
  min: 25
  max: 60
  recommended: 35

# Party Configuration
party:
  min_size: 2
  max_size: 5
  recommended_size: 4
  scaling_enabled: true

# Room Configuration
rooms:
  start_room: "foundry_entrance"
  boss_room: "molten_core_chamber"
  available_rooms:
    - "smelting_hall"
    - "lava_forge"
    - "fire_elemental_pit"
    - "cooling_chamber"
    - "gear_assembly"
    - "molten_bridge"
    - "steam_vents"
    - "coal_storage"

# Difficulty Scaling
difficulty_tiers:
  normal:
    mob_health_multiplier: 1.0
    mob_damage_multiplier: 1.0
    loot_multiplier: 1.0
    experience_multiplier: 1.0
    environmental_damage_multiplier: 1.0
  hard:
    mob_health_multiplier: 1.6
    mob_damage_multiplier: 1.4
    loot_multiplier: 1.3
    experience_multiplier: 1.4
    environmental_damage_multiplier: 1.2
  mythic:
    mob_health_multiplier: 2.2
    mob_damage_multiplier: 1.7
    loot_multiplier: 1.6
    experience_multiplier: 1.7
    environmental_damage_multiplier: 1.4
  nightmare:
    mob_health_multiplier: 3.5
    mob_damage_multiplier: 2.2
    loot_multiplier: 2.2
    experience_multiplier: 2.2
    environmental_damage_multiplier: 1.6

# Allowed Affixes
allowed_affixes:
  - "blazing_aura"
  - "molten_armor"
  - "fire_immunity"
  - "explosive_death"
  - "lava_trails"
  - "heat_wave"
  - "ember_storm"
  - "forge_blessing"

# Loot Configuration
loot_tables:
  common_chest: "foundry_common_loot"
  rare_chest: "foundry_rare_loot"
  boss_loot: "molten_lord_loot"
  completion_rewards: "foundry_completion"

# Objectives
objectives:
  primary:
    - type: "kill_boss"
      target: "molten_lord"
      description: "Defeat the Molten Lord"
  secondary:
    - type: "collect_items"
      target: "ember_crystal"
      count: 5
      description: "Collect 5 Ember Crystals"
    - type: "avoid_damage"
      damage_type: "fire"
      max_damage: 100
      description: "Take less than 100 fire damage"
    - type: "speed_run"
      time_limit: 900 # 15 minutes
      description: "Complete within 15 minutes"

# Environmental Effects
environment:
  lighting: "bright"
  temperature: "hot"
  fog_enabled: false
  ambient_sounds:
    - "BLOCK_FIRE_AMBIENT"
    - "BLOCK_LAVA_POP"
    - "BLOCK_FURNACE_FIRE_CRACKLE"
  particle_effects:
    - type: "FLAME"
      density: "medium"
      areas: ["forges", "lava_flows"]
    - type: "LAVA"
      density: "low"
      areas: ["molten_areas"]
    - type: "SMOKE_LARGE"
      density: "high"
      areas: ["chimneys", "vents"]

# Environmental Hazards
hazards:
  - type: "lava_flow"
    damage: 8.0
    interval: 10
    areas: ["lava_channels"]
    effects:
      - type: "FIRE_RESISTANCE"
        duration: -100
        amplifier: 0
  - type: "steam_jet"
    damage: 4.0
    interval: 60
    areas: ["steam_vents"]
    effects:
      - type: "BLINDNESS"
        duration: 40
        amplifier: 0
  - type: "heat_wave"
    damage: 2.0
    interval: 20
    areas: ["entire_dungeon"]
    conditions:
      - "no_fire_resistance"

# Checkpoints
checkpoints:
  - name: "entrance_secured"
    description: "Foundry Entrance Secured"
    position: "after_start_room"
  - name: "main_forge"
    description: "Main Forge Reached"
    position: "middle_rooms"
  - name: "molten_core"
    description: "Molten Core Chamber"
    position: "before_boss"

# Rewards
rewards:
  currency:
    base_amount: 150
    bonus_per_difficulty: 75
  experience:
    base_amount: 750
    bonus_per_difficulty: 300
  titles:
    - condition: "first_completion"
      title: "Forge Walker"
    - condition: "nightmare_completion"
      title: "Master of Flames"
    - condition: "no_fire_damage"
      title: "Heat Resistant"
  achievements:
    - "foundry_explorer"
    - "ember_collector"
    - "molten_lord_slayer"
    - "fire_immunity_master"

# Special Mechanics
mechanics:
  - type: "temperature_system"
    description: "Players build up heat over time, causing damage without fire resistance"
    heat_buildup_rate: 1
    heat_damage_threshold: 100
    cooling_areas: ["cooling_chamber"]
  - type: "forge_activation"
    description: "Players can activate forges to craft temporary fire resistance potions"
    required_items: ["coal", "water_bucket"]
    cooldown: 300
  - type: "lava_bridge_puzzle"
    description: "Players must activate switches to create temporary lava bridges"
    switch_count: 3
    bridge_duration: 60

# Metadata
version: "1.0.0"
author: "UltimateDungeon Team"
created: "2024-01-01"
last_modified: "2024-01-01"

package com.ultimatedungeon.udx.progression;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Tracks comprehensive player statistics across all dungeon activities.
 * 
 * <p>This class maintains detailed statistics including completion counts,
 * combat metrics, time played, and various achievement-related counters.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class PlayerStatistics {
    
    // Dungeon Statistics
    private int dungeonsCompleted;
    private int dungeonsStarted;
    private int dungeonsFailed;
    private long totalPlayTime; // in milliseconds
    private int bestCompletionStreak;
    private int currentCompletionStreak;
    
    // Combat Statistics
    private long damageDealt;
    private long damageReceived;
    private long healingDone;
    private int mobsKilled;
    private int bossesKilled;
    private int deaths;
    private int revives;
    private int revivesGiven;
    
    // Score and Progression
    private long totalScore;
    private int highestSingleScore;
    private int levelsGained;
    private int experienceEarned;
    
    // Social Statistics
    private int partiesJoined;
    private int partiesLed;
    private int playersRevived;
    private int friendsAdded;
    
    // Item and Loot Statistics
    private int chestsOpened;
    private int itemsLooted;
    private int rareItemsFound;
    private int legendaryItemsFound;
    
    // Miscellaneous
    private int secretsFound;
    private int trapsTriggered;
    private int puzzlesSolved;
    private long distanceTraveled; // in blocks
    
    // Custom counters for achievements
    private final Map<String, Integer> customCounters;
    
    public PlayerStatistics() {
        this.customCounters = new HashMap<>();
    }
    
    // Dungeon Statistics Methods
    
    public void incrementDungeonsCompleted() {
        dungeonsCompleted++;
        currentCompletionStreak++;
        if (currentCompletionStreak > bestCompletionStreak) {
            bestCompletionStreak = currentCompletionStreak;
        }
    }
    
    public void incrementDungeonsStarted() {
        dungeonsStarted++;
    }
    
    public void incrementDungeonsFailed() {
        dungeonsFailed++;
        currentCompletionStreak = 0;
    }
    
    public void addTotalPlayTime(long milliseconds) {
        totalPlayTime += milliseconds;
    }
    
    // Combat Statistics Methods
    
    public void addDamageDealt(long damage) {
        damageDealt += damage;
    }
    
    public void addDamageReceived(long damage) {
        damageReceived += damage;
    }
    
    public void addHealingDone(long healing) {
        healingDone += healing;
    }
    
    public void incrementMobsKilled() {
        mobsKilled++;
    }
    
    public void incrementBossesKilled() {
        bossesKilled++;
    }
    
    public void addDeaths(int deaths) {
        this.deaths += deaths;
    }
    
    public void incrementRevives() {
        revives++;
    }
    
    public void incrementRevivesGiven() {
        revivesGiven++;
        playersRevived++;
    }
    
    // Score and Progression Methods
    
    public void addScore(int score) {
        totalScore += score;
        if (score > highestSingleScore) {
            highestSingleScore = score;
        }
    }
    
    public void addExperience(int experience) {
        experienceEarned += experience;
    }
    
    public void incrementLevelsGained() {
        levelsGained++;
    }
    
    // Social Statistics Methods
    
    public void incrementPartiesJoined() {
        partiesJoined++;
    }
    
    public void incrementPartiesLed() {
        partiesLed++;
    }
    
    public void incrementFriendsAdded() {
        friendsAdded++;
    }
    
    // Item and Loot Methods
    
    public void incrementChestsOpened() {
        chestsOpened++;
    }
    
    public void incrementItemsLooted() {
        itemsLooted++;
    }
    
    public void incrementRareItemsFound() {
        rareItemsFound++;
    }
    
    public void incrementLegendaryItemsFound() {
        legendaryItemsFound++;
    }
    
    // Miscellaneous Methods
    
    public void incrementSecretsFound() {
        secretsFound++;
    }
    
    public void incrementTrapsTriggered() {
        trapsTriggered++;
    }
    
    public void incrementPuzzlesSolved() {
        puzzlesSolved++;
    }
    
    public void addDistanceTraveled(long blocks) {
        distanceTraveled += blocks;
    }
    
    // Custom Counter Methods
    
    public void incrementCustomCounter(String key) {
        customCounters.put(key, customCounters.getOrDefault(key, 0) + 1);
    }
    
    public void setCustomCounter(String key, int value) {
        customCounters.put(key, value);
    }
    
    public int getCustomCounter(String key) {
        return customCounters.getOrDefault(key, 0);
    }
    
    // Calculated Statistics
    
    public double getSuccessRate() {
        if (dungeonsStarted == 0) return 0.0;
        return (double) dungeonsCompleted / dungeonsStarted;
    }
    
    public double getAverageDamagePerRun() {
        if (dungeonsCompleted == 0) return 0.0;
        return (double) damageDealt / dungeonsCompleted;
    }
    
    public double getAverageScorePerRun() {
        if (dungeonsCompleted == 0) return 0.0;
        return (double) totalScore / dungeonsCompleted;
    }
    
    public Duration getTotalPlayTimeDuration() {
        return Duration.ofMillis(totalPlayTime);
    }
    
    public double getKillDeathRatio() {
        if (deaths == 0) return mobsKilled > 0 ? Double.MAX_VALUE : 0.0;
        return (double) mobsKilled / deaths;
    }
    
    // Getters
    
    public int getDungeonsCompleted() {
        return dungeonsCompleted;
    }
    
    public int getDungeonsStarted() {
        return dungeonsStarted;
    }
    
    public int getDungeonsFailed() {
        return dungeonsFailed;
    }
    
    public long getTotalPlayTime() {
        return totalPlayTime;
    }
    
    public int getBestCompletionStreak() {
        return bestCompletionStreak;
    }
    
    public int getCurrentCompletionStreak() {
        return currentCompletionStreak;
    }
    
    public long getDamageDealt() {
        return damageDealt;
    }
    
    public long getDamageReceived() {
        return damageReceived;
    }
    
    public long getHealingDone() {
        return healingDone;
    }
    
    public int getMobsKilled() {
        return mobsKilled;
    }
    
    public int getBossesKilled() {
        return bossesKilled;
    }
    
    public int getDeaths() {
        return deaths;
    }
    
    public int getRevives() {
        return revives;
    }
    
    public int getRevivesGiven() {
        return revivesGiven;
    }
    
    public long getTotalScore() {
        return totalScore;
    }
    
    public int getHighestSingleScore() {
        return highestSingleScore;
    }
    
    public int getLevelsGained() {
        return levelsGained;
    }
    
    public int getExperienceEarned() {
        return experienceEarned;
    }
    
    public int getPartiesJoined() {
        return partiesJoined;
    }
    
    public int getPartiesLed() {
        return partiesLed;
    }
    
    public int getPlayersRevived() {
        return playersRevived;
    }
    
    public int getFriendsAdded() {
        return friendsAdded;
    }
    
    public int getChestsOpened() {
        return chestsOpened;
    }
    
    public int getItemsLooted() {
        return itemsLooted;
    }
    
    public int getRareItemsFound() {
        return rareItemsFound;
    }
    
    public int getLegendaryItemsFound() {
        return legendaryItemsFound;
    }
    
    public int getSecretsFound() {
        return secretsFound;
    }
    
    public int getTrapsTriggered() {
        return trapsTriggered;
    }
    
    public int getPuzzlesSolved() {
        return puzzlesSolved;
    }
    
    public long getDistanceTraveled() {
        return distanceTraveled;
    }
    
    public Map<String, Integer> getCustomCounters() {
        return new HashMap<>(customCounters);
    }
    
    // Setters for data loading
    
    public void setDungeonsCompleted(int dungeonsCompleted) {
        this.dungeonsCompleted = dungeonsCompleted;
    }
    
    public void setDungeonsStarted(int dungeonsStarted) {
        this.dungeonsStarted = dungeonsStarted;
    }
    
    public void setDungeonsFailed(int dungeonsFailed) {
        this.dungeonsFailed = dungeonsFailed;
    }
    
    public void setTotalPlayTime(long totalPlayTime) {
        this.totalPlayTime = totalPlayTime;
    }
    
    public void setBestCompletionStreak(int bestCompletionStreak) {
        this.bestCompletionStreak = bestCompletionStreak;
    }
    
    public void setCurrentCompletionStreak(int currentCompletionStreak) {
        this.currentCompletionStreak = currentCompletionStreak;
    }
    
    public void setDamageDealt(long damageDealt) {
        this.damageDealt = damageDealt;
    }
    
    public void setDamageReceived(long damageReceived) {
        this.damageReceived = damageReceived;
    }
    
    public void setHealingDone(long healingDone) {
        this.healingDone = healingDone;
    }
    
    public void setMobsKilled(int mobsKilled) {
        this.mobsKilled = mobsKilled;
    }
    
    public void setBossesKilled(int bossesKilled) {
        this.bossesKilled = bossesKilled;
    }
    
    public void setDeaths(int deaths) {
        this.deaths = deaths;
    }
    
    public void setRevives(int revives) {
        this.revives = revives;
    }
    
    public void setRevivesGiven(int revivesGiven) {
        this.revivesGiven = revivesGiven;
    }
    
    public void setTotalScore(long totalScore) {
        this.totalScore = totalScore;
    }
    
    public void setHighestSingleScore(int highestSingleScore) {
        this.highestSingleScore = highestSingleScore;
    }
    
    public void setLevelsGained(int levelsGained) {
        this.levelsGained = levelsGained;
    }
    
    public void setExperienceEarned(int experienceEarned) {
        this.experienceEarned = experienceEarned;
    }
    
    public void setPartiesJoined(int partiesJoined) {
        this.partiesJoined = partiesJoined;
    }
    
    public void setPartiesLed(int partiesLed) {
        this.partiesLed = partiesLed;
    }
    
    public void setPlayersRevived(int playersRevived) {
        this.playersRevived = playersRevived;
    }
    
    public void setFriendsAdded(int friendsAdded) {
        this.friendsAdded = friendsAdded;
    }
    
    public void setChestsOpened(int chestsOpened) {
        this.chestsOpened = chestsOpened;
    }
    
    public void setItemsLooted(int itemsLooted) {
        this.itemsLooted = itemsLooted;
    }
    
    public void setRareItemsFound(int rareItemsFound) {
        this.rareItemsFound = rareItemsFound;
    }
    
    public void setLegendaryItemsFound(int legendaryItemsFound) {
        this.legendaryItemsFound = legendaryItemsFound;
    }
    
    public void setSecretsFound(int secretsFound) {
        this.secretsFound = secretsFound;
    }
    
    public void setTrapsTriggered(int trapsTriggered) {
        this.trapsTriggered = trapsTriggered;
    }
    
    public void setPuzzlesSolved(int puzzlesSolved) {
        this.puzzlesSolved = puzzlesSolved;
    }
    
    public void setDistanceTraveled(long distanceTraveled) {
        this.distanceTraveled = distanceTraveled;
    }
    
    @Override
    public String toString() {
        return "PlayerStatistics{" +
            "dungeonsCompleted=" + dungeonsCompleted +
            ", totalScore=" + totalScore +
            ", mobsKilled=" + mobsKilled +
            ", totalPlayTime=" + getTotalPlayTimeDuration() +
            '}';
    }
}

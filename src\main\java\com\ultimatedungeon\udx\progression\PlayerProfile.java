package com.ultimatedungeon.udx.progression;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.Instant;
import java.util.*;

/**
 * Represents a player's progression data and statistics.
 * 
 * <p>This class tracks all player progression including dungeon unlocks,
 * completion statistics, achievements, seasonal progress, and personal bests.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class PlayerProfile {
    
    private final UUID playerId;
    private final String playerName;
    private Instant firstJoin;
    private Instant lastSeen;
    
    // Dungeon Progress
    private final Set<String> unlockedDungeons;
    private final Map<String, DungeonProgress> dungeonProgress;
    
    // Achievements
    private final Set<String> unlockedAchievements;
    private final Map<String, AchievementProgress> achievementProgress;
    
    // Season Data
    private int currentSeasonScore;
    private final Map<Integer, SeasonData> seasonHistory;
    
    // Statistics
    private final PlayerStatistics statistics;
    
    // Settings
    private final Map<String, Object> settings;
    
    public PlayerProfile(@NotNull UUID playerId, @NotNull String playerName) {
        this.playerId = playerId;
        this.playerName = playerName;
        this.firstJoin = Instant.now();
        this.lastSeen = Instant.now();
        
        this.unlockedDungeons = new HashSet<>();
        this.dungeonProgress = new HashMap<>();
        this.unlockedAchievements = new HashSet<>();
        this.achievementProgress = new HashMap<>();
        this.currentSeasonScore = 0;
        this.seasonHistory = new HashMap<>();
        this.statistics = new PlayerStatistics();
        this.settings = new HashMap<>();
        
        // Initialize default settings
        initializeDefaultSettings();
    }
    
    /**
     * Initializes default player settings.
     */
    private void initializeDefaultSettings() {
        settings.put("showRunHUD", true);
        settings.put("showDamageNumbers", true);
        settings.put("showParticleEffects", true);
        settings.put("playSounds", true);
        settings.put("autoRejoin", true);
        settings.put("friendlyFire", false);
        settings.put("spectateMode", "FOLLOW");
    }
    
    // Getters
    
    @NotNull
    public UUID getPlayerId() {
        return playerId;
    }
    
    @NotNull
    public String getPlayerName() {
        return playerName;
    }
    
    @NotNull
    public Instant getFirstJoin() {
        return firstJoin;
    }
    
    @NotNull
    public Instant getLastSeen() {
        return lastSeen;
    }
    
    @NotNull
    public Set<String> getUnlockedDungeons() {
        return Collections.unmodifiableSet(unlockedDungeons);
    }
    
    @NotNull
    public Map<String, DungeonProgress> getDungeonProgress() {
        return Collections.unmodifiableMap(dungeonProgress);
    }
    
    @NotNull
    public Set<String> getUnlockedAchievements() {
        return Collections.unmodifiableSet(unlockedAchievements);
    }
    
    @NotNull
    public Map<String, AchievementProgress> getAchievementProgress() {
        return Collections.unmodifiableMap(achievementProgress);
    }
    
    public int getCurrentSeasonScore() {
        return currentSeasonScore;
    }
    
    @NotNull
    public Map<Integer, SeasonData> getSeasonHistory() {
        return Collections.unmodifiableMap(seasonHistory);
    }
    
    @NotNull
    public PlayerStatistics getStatistics() {
        return statistics;
    }
    
    @NotNull
    public Map<String, Object> getSettings() {
        return Collections.unmodifiableMap(settings);
    }
    
    // Dungeon Progress Methods
    
    /**
     * Checks if a dungeon is unlocked for this player.
     * 
     * @param dungeonId the dungeon ID
     * @return true if unlocked, false otherwise
     */
    public boolean isDungeonUnlocked(@NotNull String dungeonId) {
        return unlockedDungeons.contains(dungeonId);
    }
    
    /**
     * Unlocks a dungeon for this player.
     * 
     * @param dungeonId the dungeon ID to unlock
     * @return true if newly unlocked, false if already unlocked
     */
    public boolean unlockDungeon(@NotNull String dungeonId) {
        return unlockedDungeons.add(dungeonId);
    }
    
    /**
     * Gets the progress for a specific dungeon.
     * 
     * @param dungeonId the dungeon ID
     * @return the dungeon progress, or null if not found
     */
    @Nullable
    public DungeonProgress getDungeonProgress(@NotNull String dungeonId) {
        return dungeonProgress.get(dungeonId);
    }
    
    /**
     * Updates progress for a dungeon completion.
     * 
     * @param dungeonId the dungeon ID
     * @param difficulty the difficulty tier
     * @param completionTime the completion time in milliseconds
     * @param deaths the number of deaths
     * @param score the score achieved
     */
    public void updateDungeonProgress(@NotNull String dungeonId, @NotNull String difficulty, 
                                    long completionTime, int deaths, int score) {
        DungeonProgress progress = dungeonProgress.computeIfAbsent(dungeonId, 
            k -> new DungeonProgress(dungeonId));
        
        progress.recordCompletion(difficulty, completionTime, deaths, score);
        
        // Update statistics
        statistics.incrementDungeonsCompleted();
        statistics.addTotalPlayTime(completionTime);
        statistics.addDeaths(deaths);
        statistics.addScore(score);
    }
    
    // Achievement Methods
    
    /**
     * Checks if an achievement is unlocked.
     * 
     * @param achievementId the achievement ID
     * @return true if unlocked, false otherwise
     */
    public boolean isAchievementUnlocked(@NotNull String achievementId) {
        return unlockedAchievements.contains(achievementId);
    }
    
    /**
     * Unlocks an achievement.
     * 
     * @param achievementId the achievement ID
     * @return true if newly unlocked, false if already unlocked
     */
    public boolean unlockAchievement(@NotNull String achievementId) {
        return unlockedAchievements.add(achievementId);
    }
    
    /**
     * Gets achievement progress.
     * 
     * @param achievementId the achievement ID
     * @return the achievement progress, or null if not found
     */
    @Nullable
    public AchievementProgress getAchievementProgress(@NotNull String achievementId) {
        return achievementProgress.get(achievementId);
    }
    
    /**
     * Updates achievement progress.
     * 
     * @param achievementId the achievement ID
     * @param progress the current progress value
     * @param target the target value for completion
     */
    public void updateAchievementProgress(@NotNull String achievementId, int progress, int target) {
        AchievementProgress achProgress = achievementProgress.computeIfAbsent(achievementId,
            k -> new AchievementProgress(achievementId, target));
        
        achProgress.setProgress(progress);
        
        // Auto-unlock if completed
        if (progress >= target && !isAchievementUnlocked(achievementId)) {
            unlockAchievement(achievementId);
        }
    }
    
    // Season Methods
    
    /**
     * Adds score to the current season.
     * 
     * @param score the score to add
     */
    public void addSeasonScore(int score) {
        this.currentSeasonScore += score;
    }
    
    /**
     * Gets season data for a specific season.
     * 
     * @param seasonId the season ID
     * @return the season data, or null if not found
     */
    @Nullable
    public SeasonData getSeasonData(int seasonId) {
        return seasonHistory.get(seasonId);
    }
    
    /**
     * Ends the current season and archives the data.
     * 
     * @param seasonId the season ID to archive
     * @param rank the final rank achieved
     */
    public void endSeason(int seasonId, int rank) {
        SeasonData seasonData = new SeasonData(seasonId, currentSeasonScore, rank, Instant.now());
        seasonHistory.put(seasonId, seasonData);
        currentSeasonScore = 0;
    }
    
    // Settings Methods
    
    /**
     * Gets a setting value.
     * 
     * @param key the setting key
     * @param defaultValue the default value if not found
     * @param <T> the value type
     * @return the setting value or default
     */
    @SuppressWarnings("unchecked")
    public <T> T getSetting(@NotNull String key, @NotNull T defaultValue) {
        Object value = settings.get(key);
        if (value != null && defaultValue.getClass().isInstance(value)) {
            return (T) value;
        }
        return defaultValue;
    }
    
    /**
     * Sets a setting value.
     * 
     * @param key the setting key
     * @param value the setting value
     */
    public void setSetting(@NotNull String key, @NotNull Object value) {
        settings.put(key, value);
    }
    
    // Update Methods
    
    /**
     * Updates the last seen timestamp.
     */
    public void updateLastSeen() {
        this.lastSeen = Instant.now();
    }
    
    /**
     * Sets the first join timestamp.
     * 
     * @param firstJoin the first join timestamp
     */
    public void setFirstJoin(@NotNull Instant firstJoin) {
        this.firstJoin = firstJoin;
    }
    
    /**
     * Sets the last seen timestamp.
     * 
     * @param lastSeen the last seen timestamp
     */
    public void setLastSeen(@NotNull Instant lastSeen) {
        this.lastSeen = lastSeen;
    }
    
    /**
     * Sets the current season score.
     * 
     * @param score the season score
     */
    public void setCurrentSeasonScore(int score) {
        this.currentSeasonScore = score;
    }
    
    // Utility Methods
    
    /**
     * Gets the total number of dungeons completed across all difficulties.
     * 
     * @return the total completion count
     */
    public int getTotalDungeonsCompleted() {
        return dungeonProgress.values().stream()
            .mapToInt(DungeonProgress::getTotalCompletions)
            .sum();
    }
    
    /**
     * Gets the highest difficulty completed for any dungeon.
     * 
     * @return the highest difficulty, or null if none completed
     */
    @Nullable
    public String getHighestDifficultyCompleted() {
        return dungeonProgress.values().stream()
            .flatMap(progress -> progress.getCompletedDifficulties().stream())
            .max(this::compareDifficulties)
            .orElse(null);
    }
    
    /**
     * Compares two difficulty strings for ordering.
     * 
     * @param d1 first difficulty
     * @param d2 second difficulty
     * @return comparison result
     */
    private int compareDifficulties(String d1, String d2) {
        List<String> order = Arrays.asList("NORMAL", "HARD", "MYTHIC", "NIGHTMARE");
        return Integer.compare(order.indexOf(d1), order.indexOf(d2));
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        PlayerProfile that = (PlayerProfile) obj;
        return Objects.equals(playerId, that.playerId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(playerId);
    }
    
    @Override
    public String toString() {
        return "PlayerProfile{" +
            "playerId=" + playerId +
            ", playerName='" + playerName + '\'' +
            ", unlockedDungeons=" + unlockedDungeons.size() +
            ", achievements=" + unlockedAchievements.size() +
            ", seasonScore=" + currentSeasonScore +
            '}';
    }
}

package com.ultimatedungeon.udx.affix;

/**
 * Represents the type of affix effect.
 * 
 * <p>Affix types categorize the kind of modification an affix provides,
 * helping with organization and application logic.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public enum AffixType {
    
    /**
     * Affects mob behavior and stats.
     */
    MOB("Mob", "§c"),
    
    /**
     * Affects environmental conditions.
     */
    ENVIRONMENT("Environment", "§a"),
    
    /**
     * Affects player abilities and stats.
     */
    PLAYER("Player", "§9"),
    
    /**
     * Affects loot and rewards.
     */
    LOOT("Loot", "§6"),
    
    /**
     * Affects dungeon mechanics.
     */
    MECHANIC("Mechanic", "§5"),
    
    /**
     * Mixed effects across multiple categories.
     */
    MIXED("Mixed", "§d");
    
    private final String displayName;
    private final String colorCode;
    
    AffixType(String displayName, String colorCode) {
        this.displayName = displayName;
        this.colorCode = colorCode;
    }
    
    /**
     * Gets the display name.
     * 
     * @return the display name
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * Gets the color code.
     * 
     * @return the color code
     */
    public String getColorCode() {
        return colorCode;
    }
    
    /**
     * Gets the formatted display name with color.
     * 
     * @return the formatted display name
     */
    public String getFormattedName() {
        return colorCode + displayName;
    }
}

package com.ultimatedungeon.udx.gen;

import com.ultimatedungeon.udx.config.ConfigService;
import com.ultimatedungeon.udx.room.Connector;
import com.ultimatedungeon.udx.room.RoomService;
import com.ultimatedungeon.udx.room.RoomTemplate;
import com.ultimatedungeon.udx.util.SchedulerUtil;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Level;
import java.util.stream.Collectors;

/**
 * Service for procedural dungeon generation.
 * 
 * <p>This service handles the algorithmic generation of dungeon layouts
 * using room templates and constraint-based placement.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class GenerationService {
    
    private final Plugin plugin;
    private final RoomService roomService;
    private final ConfigService configService;
    private final SchedulerUtil schedulerUtil;
    
    // Generation statistics
    private int totalGenerations = 0;
    private long totalGenerationTime = 0;
    private int successfulGenerations = 0;
    
    public GenerationService(@NotNull Plugin plugin, @NotNull RoomService roomService, @NotNull ConfigService configService) {
        this.plugin = plugin;
        this.roomService = roomService;
        this.configService = configService;
        this.schedulerUtil = new SchedulerUtil(plugin);
    }
    
    /**
     * Initializes the generation service.
     */
    public void initialize() {
        plugin.getLogger().info("Generation service initialized");
    }
    
    /**
     * Generates a dungeon layout asynchronously.
     * 
     * @param dungeonId the dungeon ID
     * @param constraints the generation constraints
     * @return future that completes with the generated layout
     */
    @NotNull
    public CompletableFuture<DungeonLayout> generateDungeonAsync(@NotNull String dungeonId, @NotNull GenerationConstraints constraints) {
        return CompletableFuture.supplyAsync(() -> generateDungeon(dungeonId, constraints));
    }
    
    /**
     * Generates a dungeon layout synchronously.
     * 
     * @param dungeonId the dungeon ID
     * @param constraints the generation constraints
     * @return the generated layout
     * @throws GenerationException if generation fails
     */
    @NotNull
    public DungeonLayout generateDungeon(@NotNull String dungeonId, @NotNull GenerationConstraints constraints) throws GenerationException {
        long startTime = System.currentTimeMillis();
        totalGenerations++;
        
        try {
            // Validate constraints
            List<String> errors = constraints.validate();
            if (!errors.isEmpty()) {
                throw new GenerationException("Invalid constraints: " + String.join(", ", errors));
            }
            
            plugin.getLogger().info("Generating dungeon '" + dungeonId + "' with constraints: " + constraints);
            
            // Initialize random with seed
            Random random = new Random(constraints.getSeed());
            
            // Generate layout
            DungeonLayout layout = generateLayoutWithRetries(dungeonId, constraints, random);
            
            long generationTime = System.currentTimeMillis() - startTime;
            totalGenerationTime += generationTime;
            successfulGenerations++;
            
            plugin.getLogger().info("Successfully generated dungeon '" + dungeonId + "' in " + generationTime + "ms (" + 
                                  layout.getRoomCount() + " rooms, depth " + layout.getMaxDepth() + ")");
            
            return layout;
            
        } catch (Exception e) {
            long generationTime = System.currentTimeMillis() - startTime;
            plugin.getLogger().log(Level.SEVERE, "Failed to generate dungeon '" + dungeonId + "' after " + generationTime + "ms", e);
            
            if (e instanceof GenerationException) {
                throw e;
            } else {
                throw new GenerationException("Unexpected error during generation", e);
            }
        }
    }
    
    /**
     * Generates a layout with retry logic.
     */
    @NotNull
    private DungeonLayout generateLayoutWithRetries(@NotNull String dungeonId, @NotNull GenerationConstraints constraints, @NotNull Random random) throws GenerationException {
        GenerationException lastException = null;
        
        for (int attempt = 1; attempt <= constraints.getMaxRetries(); attempt++) {
            try {
                return generateLayoutAttempt(dungeonId, constraints, random, attempt);
            } catch (GenerationException e) {
                lastException = e;
                
                if (attempt < constraints.getMaxRetries()) {
                    plugin.getLogger().warning("Generation attempt " + attempt + " failed: " + e.getMessage() + " (retrying...)");
                    
                    // Modify seed slightly for retry
                    random = new Random(constraints.getSeed() + attempt);
                } else {
                    plugin.getLogger().severe("All " + constraints.getMaxRetries() + " generation attempts failed");
                }
            }
        }
        
        throw new GenerationException("Failed to generate dungeon after " + constraints.getMaxRetries() + " attempts", lastException);
    }
    
    /**
     * Performs a single generation attempt.
     */
    @NotNull
    private DungeonLayout generateLayoutAttempt(@NotNull String dungeonId, @NotNull GenerationConstraints constraints, @NotNull Random random, int attempt) throws GenerationException {
        // Get available room templates
        List<RoomTemplate> availableTemplates = getAvailableTemplates(constraints);
        if (availableTemplates.isEmpty()) {
            throw new GenerationException("No room templates available for constraints");
        }
        
        // Create generation context
        GenerationContext context = new GenerationContext(constraints, availableTemplates, random);
        
        // Step 1: Place entrance room
        RoomLayout entranceRoom = placeEntranceRoom(context);
        context.addRoom(entranceRoom);
        
        // Step 2: Generate main path to target depth
        generateMainPath(context, entranceRoom);
        
        // Step 3: Add branches based on branching factor
        addBranches(context);
        
        // Step 4: Place exit rooms
        Set<RoomLayout> exitRooms = placeExitRooms(context);
        
        // Step 5: Validate and create layout
        Map<Integer, RoomLayout> roomMap = context.getRooms().stream()
            .collect(Collectors.toMap(RoomLayout::getId, room -> room));
        
        long generationTime = System.currentTimeMillis() - context.getStartTime();
        
        DungeonLayout layout = new DungeonLayout(
            dungeonId,
            constraints,
            roomMap,
            entranceRoom,
            exitRooms,
            generationTime
        );
        
        // Validate layout
        List<String> validationErrors = layout.validate();
        if (!validationErrors.isEmpty()) {
            throw new GenerationException("Generated layout is invalid: " + String.join(", ", validationErrors));
        }
        
        return layout;
    }
    
    /**
     * Gets available room templates based on constraints.
     */
    @NotNull
    private List<RoomTemplate> getAvailableTemplates(@NotNull GenerationConstraints constraints) {
        List<RoomTemplate> templates = new ArrayList<>(roomService.getAllRoomTemplates());
        
        // Filter by required tags
        if (!constraints.getRequiredTags().isEmpty()) {
            templates = templates.stream()
                .filter(template -> template.getTags().containsAll(constraints.getRequiredTags()))
                .collect(Collectors.toList());
        }
        
        // Filter by forbidden tags
        if (!constraints.getForbiddenTags().isEmpty()) {
            templates = templates.stream()
                .filter(template -> Collections.disjoint(template.getTags(), constraints.getForbiddenTags()))
                .collect(Collectors.toList());
        }
        
        // Filter by difficulty
        templates = templates.stream()
            .filter(template -> template.isSuitableForDepth(constraints.getTargetDifficulty()))
            .collect(Collectors.toList());
        
        return templates;
    }
    
    /**
     * Places the entrance room.
     */
    @NotNull
    private RoomLayout placeEntranceRoom(@NotNull GenerationContext context) throws GenerationException {
        List<RoomTemplate> entranceTemplates = context.getAvailableTemplates().stream()
            .filter(template -> template.getRoomType() == RoomTemplate.RoomType.ENTRANCE)
            .collect(Collectors.toList());
        
        if (entranceTemplates.isEmpty()) {
            // Use any template as entrance if no dedicated entrance templates
            entranceTemplates = context.getAvailableTemplates().stream()
                .filter(template -> template.getRoomType() == RoomTemplate.RoomType.CHAMBER)
                .collect(Collectors.toList());
        }
        
        if (entranceTemplates.isEmpty()) {
            throw new GenerationException("No suitable entrance room templates available");
        }
        
        RoomTemplate template = entranceTemplates.get(context.getRandom().nextInt(entranceTemplates.size()));
        
        // Place at origin
        return new RoomLayout(context.getNextRoomId(), template, 0, 0, 0, 0);
    }
    
    /**
     * Generates the main path from entrance to target depth.
     */
    private void generateMainPath(@NotNull GenerationContext context, @NotNull RoomLayout startRoom) throws GenerationException {
        RoomLayout currentRoom = startRoom;
        int targetDepth = Math.min(context.getConstraints().getMaxDepth(), 
                                  context.getConstraints().getTargetRooms() / 2);
        
        for (int depth = 1; depth <= targetDepth && context.getRooms().size() < context.getConstraints().getMaxRooms(); depth++) {
            RoomLayout nextRoom = placeConnectedRoom(context, currentRoom, depth);
            if (nextRoom != null) {
                context.addRoom(nextRoom);
                currentRoom = nextRoom;
            } else {
                // Can't extend main path further
                break;
            }
        }
    }
    
    /**
     * Adds branch rooms based on branching factor.
     */
    private void addBranches(@NotNull GenerationContext context) throws GenerationException {
        double branchingFactor = context.getConstraints().getBranchingFactor();
        int targetRooms = context.getConstraints().getTargetRooms();
        
        List<RoomLayout> roomsToExpand = new ArrayList<>(context.getRooms());
        Collections.shuffle(roomsToExpand, context.getRandom());
        
        for (RoomLayout room : roomsToExpand) {
            if (context.getRooms().size() >= targetRooms) {
                break;
            }
            
            // Calculate branch probability based on current connections and branching factor
            double branchProbability = Math.max(0.0, branchingFactor - room.getConnectionCount());
            
            if (context.getRandom().nextDouble() < branchProbability) {
                // Try to add a branch
                RoomLayout branchRoom = placeConnectedRoom(context, room, room.getDepth() + 1);
                if (branchRoom != null) {
                    context.addRoom(branchRoom);
                    
                    // Possibly extend the branch
                    if (context.getRandom().nextDouble() < 0.3 && context.getRooms().size() < targetRooms) {
                        RoomLayout extendedRoom = placeConnectedRoom(context, branchRoom, branchRoom.getDepth() + 1);
                        if (extendedRoom != null) {
                            context.addRoom(extendedRoom);
                        }
                    }
                }
            }
        }
    }
    
    /**
     * Places a room connected to an existing room.
     */
    @Nullable
    private RoomLayout placeConnectedRoom(@NotNull GenerationContext context, @NotNull RoomLayout parentRoom, int depth) {
        List<Connector> availableConnectors = parentRoom.getAllAvailableConnectors();
        if (availableConnectors.isEmpty()) {
            return null;
        }
        
        Collections.shuffle(availableConnectors, context.getRandom());
        
        for (Connector connector : availableConnectors) {
            RoomLayout newRoom = tryPlaceRoomAtConnector(context, parentRoom, connector, depth);
            if (newRoom != null) {
                return newRoom;
            }
        }
        
        return null;
    }
    
    /**
     * Tries to place a room at a specific connector.
     */
    @Nullable
    private RoomLayout tryPlaceRoomAtConnector(@NotNull GenerationContext context, @NotNull RoomLayout parentRoom, @NotNull Connector connector, int depth) {
        // Get compatible templates
        List<RoomTemplate> compatibleTemplates = getCompatibleTemplates(context, connector.direction().getOpposite(), depth);
        if (compatibleTemplates.isEmpty()) {
            return null;
        }
        
        Collections.shuffle(compatibleTemplates, context.getRandom());
        
        for (RoomTemplate template : compatibleTemplates) {
            // Find compatible connectors in the template
            List<Connector> templateConnectors = template.getConnectors().stream()
                .filter(c -> c.direction() == connector.direction().getOpposite())
                .filter(c -> c.isCompatibleWith(connector))
                .collect(Collectors.toList());
            
            if (templateConnectors.isEmpty()) {
                continue;
            }
            
            Connector templateConnector = templateConnectors.get(context.getRandom().nextInt(templateConnectors.size()));
            
            // Calculate position
            int[] parentConnectorPos = parentRoom.getConnectorWorldPosition(connector);
            int[] templateConnectorPos = {templateConnector.x(), templateConnector.y(), templateConnector.z()};
            
            int roomX = parentConnectorPos[0] - templateConnectorPos[0];
            int roomY = parentConnectorPos[1] - templateConnectorPos[1];
            int roomZ = parentConnectorPos[2] - templateConnectorPos[2];
            
            // Apply direction offset
            Connector.Direction direction = connector.direction();
            switch (direction) {
                case NORTH -> roomZ -= context.getConstraints().getRoomSpacing();
                case SOUTH -> roomZ += context.getConstraints().getRoomSpacing();
                case EAST -> roomX += context.getConstraints().getRoomSpacing();
                case WEST -> roomX -= context.getConstraints().getRoomSpacing();
                case UP -> roomY += context.getConstraints().getRoomSpacing();
                case DOWN -> roomY -= context.getConstraints().getRoomSpacing();
            }
            
            RoomLayout newRoom = new RoomLayout(context.getNextRoomId(), template, roomX, roomY, roomZ, depth);
            
            // Check for overlaps
            if (!context.getConstraints().isAllowOverlaps()) {
                boolean overlaps = context.getRooms().stream().anyMatch(existing -> existing.overlaps(newRoom));
                if (overlaps) {
                    continue;
                }
            }
            
            // Connect the rooms
            parentRoom.connect(connector.direction(), newRoom, connector);
            
            return newRoom;
        }
        
        return null;
    }
    
    /**
     * Gets templates compatible with a specific direction and depth.
     */
    @NotNull
    private List<RoomTemplate> getCompatibleTemplates(@NotNull GenerationContext context, @NotNull Connector.Direction requiredDirection, int depth) {
        return context.getAvailableTemplates().stream()
            .filter(template -> template.isSuitableForDepth(depth))
            .filter(template -> template.getConnectors().stream()
                .anyMatch(connector -> connector.direction() == requiredDirection))
            .collect(Collectors.toList());
    }
    
    /**
     * Places exit rooms at dead ends.
     */
    @NotNull
    private Set<RoomLayout> placeExitRooms(@NotNull GenerationContext context) {
        Set<RoomLayout> exitRooms = new HashSet<>();
        
        // Find dead-end rooms at maximum depth
        int maxDepth = context.getRooms().stream()
            .mapToInt(RoomLayout::getDepth)
            .max()
            .orElse(0);
        
        List<RoomLayout> candidates = context.getRooms().stream()
            .filter(room -> room.getDepth() >= maxDepth - 1)
            .filter(room -> room.getConnectionCount() <= 1)
            .collect(Collectors.toList());
        
        if (candidates.isEmpty()) {
            // Use any room at max depth as exit
            candidates = context.getRooms().stream()
                .filter(room -> room.getDepth() == maxDepth)
                .collect(Collectors.toList());
        }
        
        // Select at least one exit room
        if (!candidates.isEmpty()) {
            exitRooms.add(candidates.get(context.getRandom().nextInt(candidates.size())));
        }
        
        return exitRooms;
    }
    
    /**
     * Gets generation statistics.
     * 
     * @return map of statistics
     */
    @NotNull
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        stats.put("totalGenerations", totalGenerations);
        stats.put("successfulGenerations", successfulGenerations);
        stats.put("failureRate", totalGenerations > 0 ? (double)(totalGenerations - successfulGenerations) / totalGenerations : 0.0);
        stats.put("totalGenerationTime", totalGenerationTime);
        stats.put("averageGenerationTime", successfulGenerations > 0 ? (double)totalGenerationTime / successfulGenerations : 0.0);
        
        return stats;
    }
    
    /**
     * Shuts down the generation service.
     */
    public void shutdown() {
        plugin.getLogger().info("Generation service shut down (generated " + successfulGenerations + "/" + totalGenerations + " dungeons)");
    }
    
    /**
     * Internal generation context.
     */
    private static class GenerationContext {
        private final GenerationConstraints constraints;
        private final List<RoomTemplate> availableTemplates;
        private final Random random;
        private final List<RoomLayout> rooms;
        private final long startTime;
        private int nextRoomId;
        
        public GenerationContext(@NotNull GenerationConstraints constraints, @NotNull List<RoomTemplate> availableTemplates, @NotNull Random random) {
            this.constraints = constraints;
            this.availableTemplates = availableTemplates;
            this.random = random;
            this.rooms = new ArrayList<>();
            this.startTime = System.currentTimeMillis();
            this.nextRoomId = 1;
        }
        
        public GenerationConstraints getConstraints() { return constraints; }
        public List<RoomTemplate> getAvailableTemplates() { return availableTemplates; }
        public Random getRandom() { return random; }
        public List<RoomLayout> getRooms() { return rooms; }
        public long getStartTime() { return startTime; }
        
        public int getNextRoomId() { return nextRoomId++; }
        
        public void addRoom(@NotNull RoomLayout room) {
            rooms.add(room);
        }
    }
    
    /**
     * Exception thrown when generation fails.
     */
    public static class GenerationException extends RuntimeException {
        public GenerationException(@NotNull String message) {
            super(message);
        }
        
        public GenerationException(@NotNull String message, @Nullable Throwable cause) {
            super(message, cause);
        }
    }
}

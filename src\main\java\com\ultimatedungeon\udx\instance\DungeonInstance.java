package com.ultimatedungeon.udx.instance;

import com.ultimatedungeon.udx.party.Party;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Represents a single dungeon instance with its own isolated world.
 * 
 * <p>Each instance has a unique ID, manages its own world, tracks players,
 * and follows a strict lifecycle state machine.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class DungeonInstance {
    
    private final UUID instanceId;
    private final String dungeonId;
    private final String worldName;
    private final AtomicReference<InstanceState> state;
    private final Set<UUID> players;
    private final Map<String, Object> metadata;
    
    private Party party;
    private World world;
    private Location spawnLocation;
    private Instant createdAt;
    private Instant startedAt;
    private Instant completedAt;
    private int maxPlayers;
    private int lives;
    private long timeoutTicks;
    
    // Statistics
    private int mobsKilled;
    private int chestsOpened;
    private int deathCount;
    private long elapsedTicks;
    
    public DungeonInstance(@NotNull String dungeonId) {
        this.instanceId = UUID.randomUUID();
        this.dungeonId = dungeonId;
        this.worldName = "udx_inst_" + instanceId.toString().replace("-", "");
        this.state = new AtomicReference<>(InstanceState.CREATING);
        this.players = ConcurrentHashMap.newKeySet();
        this.metadata = new ConcurrentHashMap<>();
        this.createdAt = Instant.now();
        this.maxPlayers = 4; // Default
        this.lives = 3; // Default
        this.timeoutTicks = 20L * 60L * 30L; // 30 minutes default
    }
    
    /**
     * Transitions the instance to a new state.
     * 
     * @param newState the target state
     * @return true if transition was successful
     */
    public boolean transitionTo(@NotNull InstanceState newState) {
        InstanceState currentState = state.get();
        
        if (!currentState.canTransitionTo(newState)) {
            return false;
        }
        
        if (state.compareAndSet(currentState, newState)) {
            onStateChanged(currentState, newState);
            return true;
        }
        
        return false;
    }
    
    /**
     * Called when the instance state changes.
     */
    private void onStateChanged(@NotNull InstanceState from, @NotNull InstanceState to) {
        switch (to) {
            case READY -> {
                // Instance is ready for players
                broadcastMessage(Component.text("Dungeon instance is ready! Players may now join.")
                    .color(NamedTextColor.GREEN));
            }
            case RUNNING -> {
                this.startedAt = Instant.now();
                broadcastMessage(Component.text("Dungeon run has started! Good luck!")
                    .color(NamedTextColor.YELLOW));
            }
            case COMPLETING -> {
                this.completedAt = Instant.now();
                this.elapsedTicks = System.currentTimeMillis() / 50L - (startedAt != null ? startedAt.toEpochMilli() / 50L : 0L);
            }
            case DISPOSING -> {
                broadcastMessage(Component.text("Instance is shutting down. You will be teleported out.")
                    .color(NamedTextColor.RED));
            }
        }
    }
    
    /**
     * Adds a player to the instance.
     * 
     * @param player the player to add
     * @return true if player was added successfully
     */
    public boolean addPlayer(@NotNull Player player) {
        if (!state.get().canJoin()) {
            return false;
        }
        
        if (players.size() >= maxPlayers) {
            return false;
        }
        
        if (players.add(player.getUniqueId())) {
            // Teleport player to spawn location
            if (spawnLocation != null) {
                player.teleport(spawnLocation);
            }
            
            // Send welcome message
            player.sendMessage(Component.text("Welcome to " + dungeonId + "!")
                .color(NamedTextColor.GREEN));
            
            // Notify other players
            broadcastMessage(Component.text(player.getName() + " has joined the dungeon.")
                .color(NamedTextColor.GRAY), player);
            
            // Start instance if this is the first player
            if (players.size() == 1 && state.get() == InstanceState.READY) {
                transitionTo(InstanceState.RUNNING);
            }
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Removes a player from the instance.
     * 
     * @param player the player to remove
     * @return true if player was removed
     */
    public boolean removePlayer(@NotNull Player player) {
        if (players.remove(player.getUniqueId())) {
            // Teleport player to spawn world
            Location spawnWorld = Bukkit.getWorlds().get(0).getSpawnLocation();
            player.teleport(spawnWorld);
            
            // Notify remaining players
            broadcastMessage(Component.text(player.getName() + " has left the dungeon.")
                .color(NamedTextColor.GRAY));
            
            // Check if instance should be disposed
            if (players.isEmpty() && state.get().isActive()) {
                transitionTo(InstanceState.DISPOSING);
            }
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Broadcasts a message to all players in the instance.
     * 
     * @param message the message to broadcast
     */
    public void broadcastMessage(@NotNull Component message) {
        broadcastMessage(message, null);
    }
    
    /**
     * Broadcasts a message to all players except the excluded one.
     * 
     * @param message the message to broadcast
     * @param exclude the player to exclude (can be null)
     */
    public void broadcastMessage(@NotNull Component message, @Nullable Player exclude) {
        for (UUID playerId : players) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline() && !player.equals(exclude)) {
                player.sendMessage(message);
            }
        }
    }
    
    /**
     * Gets all online players in the instance.
     * 
     * @return list of online players
     */
    @NotNull
    public List<Player> getOnlinePlayers() {
        List<Player> onlinePlayers = new ArrayList<>();
        for (UUID playerId : players) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                onlinePlayers.add(player);
            }
        }
        return onlinePlayers;
    }
    
    /**
     * Checks if the instance has timed out.
     * 
     * @return true if timed out
     */
    public boolean isTimedOut() {
        if (startedAt == null) {
            return false;
        }
        
        long currentTicks = System.currentTimeMillis() / 50L;
        long startTicks = startedAt.toEpochMilli() / 50L;
        return (currentTicks - startTicks) > timeoutTicks;
    }
    
    /**
     * Gets the remaining time in ticks.
     * 
     * @return remaining ticks, or -1 if no timeout
     */
    public long getRemainingTicks() {
        if (startedAt == null || timeoutTicks <= 0) {
            return -1;
        }
        
        long currentTicks = System.currentTimeMillis() / 50L;
        long startTicks = startedAt.toEpochMilli() / 50L;
        long elapsed = currentTicks - startTicks;
        return Math.max(0, timeoutTicks - elapsed);
    }
    
    // Getters and setters
    
    @NotNull
    public UUID getInstanceId() {
        return instanceId;
    }
    
    @NotNull
    public String getDungeonId() {
        return dungeonId;
    }
    
    @NotNull
    public String getWorldName() {
        return worldName;
    }
    
    @NotNull
    public InstanceState getState() {
        return state.get();
    }
    
    @NotNull
    public Set<UUID> getPlayerIds() {
        return new HashSet<>(players);
    }
    
    public int getPlayerCount() {
        return players.size();
    }
    
    @Nullable
    public Party getParty() {
        return party;
    }
    
    public void setParty(@Nullable Party party) {
        this.party = party;
    }
    
    @Nullable
    public World getWorld() {
        return world;
    }
    
    public void setWorld(@Nullable World world) {
        this.world = world;
    }
    
    @Nullable
    public Location getSpawnLocation() {
        return spawnLocation;
    }
    
    public void setSpawnLocation(@Nullable Location spawnLocation) {
        this.spawnLocation = spawnLocation;
    }
    
    @NotNull
    public Instant getCreatedAt() {
        return createdAt;
    }
    
    @Nullable
    public Instant getStartedAt() {
        return startedAt;
    }
    
    @Nullable
    public Instant getCompletedAt() {
        return completedAt;
    }
    
    public int getMaxPlayers() {
        return maxPlayers;
    }
    
    public void setMaxPlayers(int maxPlayers) {
        this.maxPlayers = Math.max(1, maxPlayers);
    }
    
    public int getLives() {
        return lives;
    }
    
    public void setLives(int lives) {
        this.lives = Math.max(0, lives);
    }
    
    public long getTimeoutTicks() {
        return timeoutTicks;
    }
    
    public void setTimeoutTicks(long timeoutTicks) {
        this.timeoutTicks = Math.max(0, timeoutTicks);
    }
    
    // Statistics
    
    public int getMobsKilled() {
        return mobsKilled;
    }
    
    public void incrementMobsKilled() {
        this.mobsKilled++;
    }
    
    public int getChestsOpened() {
        return chestsOpened;
    }
    
    public void incrementChestsOpened() {
        this.chestsOpened++;
    }
    
    public int getDeathCount() {
        return deathCount;
    }
    
    public void incrementDeathCount() {
        this.deathCount++;
    }
    
    public long getElapsedTicks() {
        if (startedAt == null) {
            return 0;
        }
        
        long endTicks = completedAt != null ? 
            completedAt.toEpochMilli() / 50L : 
            System.currentTimeMillis() / 50L;
        long startTicks = startedAt.toEpochMilli() / 50L;
        
        return endTicks - startTicks;
    }
    
    // Metadata
    
    @Nullable
    public Object getMetadata(@NotNull String key) {
        return metadata.get(key);
    }
    
    public void setMetadata(@NotNull String key, @Nullable Object value) {
        if (value == null) {
            metadata.remove(key);
        } else {
            metadata.put(key, value);
        }
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (!(obj instanceof DungeonInstance other)) return false;
        return instanceId.equals(other.instanceId);
    }
    
    @Override
    public int hashCode() {
        return instanceId.hashCode();
    }
    
    @Override
    public String toString() {
        return "DungeonInstance{" +
            "id=" + instanceId +
            ", dungeon=" + dungeonId +
            ", state=" + state.get() +
            ", players=" + players.size() +
            '}';
    }
}

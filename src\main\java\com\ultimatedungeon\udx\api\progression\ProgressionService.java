package com.ultimatedungeon.udx.api.progression;

import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.UUID;

/**
 * Service interface for managing player progression and achievements.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public interface ProgressionService {
    
    /**
     * Gets a player's profile data.
     * 
     * @param playerId The player UUID
     * @return The player profile, or null if not found
     */
    @Nullable
    PlayerProfileData getPlayerProfile(@NotNull UUID playerId);
    
    /**
     * Gets a player's profile data.
     * 
     * @param player The player
     * @return The player profile, or null if not found
     */
    @Nullable
    PlayerProfileData getPlayerProfile(@NotNull Player player);
    
    /**
     * Gets a player's progress for a specific dungeon.
     * 
     * @param playerId The player UUID
     * @param dungeonId The dungeon ID
     * @return The dungeon progress, or null if not found
     */
    @Nullable
    DungeonProgressData getDungeonProgress(@NotNull UUID playerId, @NotNull String dungeonId);
    
    /**
     * Gets all achievements available in the system.
     * 
     * @return List of all achievements
     */
    @NotNull
    List<AchievementData> getAllAchievements();
    
    /**
     * Gets a player's achievement progress.
     * 
     * @param playerId The player UUID
     * @param achievementId The achievement ID
     * @return The achievement progress, or null if not found
     */
    @Nullable
    AchievementProgressData getAchievementProgress(@NotNull UUID playerId, @NotNull String achievementId);
    
    /**
     * Gets all achievement progress for a player.
     * 
     * @param playerId The player UUID
     * @return List of achievement progress
     */
    @NotNull
    List<AchievementProgressData> getPlayerAchievements(@NotNull UUID playerId);
    
    /**
     * Gets the current season data.
     * 
     * @return The current season data
     */
    @NotNull
    SeasonData getCurrentSeason();
    
    /**
     * Gets a player's statistics for the current season.
     * 
     * @param playerId The player UUID
     * @return The season statistics, or null if not found
     */
    @Nullable
    SeasonStatsData getSeasonStats(@NotNull UUID playerId);
    
    /**
     * Gets the leaderboard for a specific dungeon and difficulty.
     * 
     * @param dungeonId The dungeon ID
     * @param difficulty The difficulty tier
     * @param limit The maximum number of entries to return
     * @return List of leaderboard entries
     */
    @NotNull
    List<LeaderboardEntry> getLeaderboard(@NotNull String dungeonId, @NotNull String difficulty, int limit);
    
    /**
     * Gets the global leaderboard for all dungeons.
     * 
     * @param limit The maximum number of entries to return
     * @return List of global leaderboard entries
     */
    @NotNull
    List<LeaderboardEntry> getGlobalLeaderboard(int limit);
    
    /**
     * Checks if a player has unlocked a specific dungeon.
     * 
     * @param playerId The player UUID
     * @param dungeonId The dungeon ID
     * @return True if the dungeon is unlocked
     */
    boolean isDungeonUnlocked(@NotNull UUID playerId, @NotNull String dungeonId);
    
    /**
     * Checks if a player has unlocked a specific difficulty tier.
     * 
     * @param playerId The player UUID
     * @param dungeonId The dungeon ID
     * @param difficulty The difficulty tier
     * @return True if the difficulty is unlocked
     */
    boolean isDifficultyUnlocked(@NotNull UUID playerId, @NotNull String dungeonId, @NotNull String difficulty);
    
    /**
     * Awards an achievement to a player.
     * 
     * @param playerId The player UUID
     * @param achievementId The achievement ID
     * @return True if the achievement was awarded successfully
     */
    boolean awardAchievement(@NotNull UUID playerId, @NotNull String achievementId);
    
    /**
     * Updates a player's statistics after completing a dungeon.
     * 
     * @param playerId The player UUID
     * @param dungeonId The dungeon ID
     * @param difficulty The difficulty tier
     * @param completionTimeMs The completion time in milliseconds
     * @param deaths The number of deaths
     * @param damageDealt The total damage dealt
     * @param healingDone The total healing done
     * @return True if the statistics were updated successfully
     */
    boolean updateDungeonStats(@NotNull UUID playerId, @NotNull String dungeonId, @NotNull String difficulty, 
                              long completionTimeMs, int deaths, double damageDealt, double healingDone);
    
    /**
     * Gets a player's rank for a specific dungeon and difficulty.
     * 
     * @param playerId The player UUID
     * @param dungeonId The dungeon ID
     * @param difficulty The difficulty tier
     * @return The player's rank (1-based), or -1 if not ranked
     */
    int getPlayerRank(@NotNull UUID playerId, @NotNull String dungeonId, @NotNull String difficulty);
    
    /**
     * Gets a player's global rank across all dungeons.
     * 
     * @param playerId The player UUID
     * @return The player's global rank (1-based), or -1 if not ranked
     */
    int getPlayerGlobalRank(@NotNull UUID playerId);
}

package com.ultimatedungeon.udx.gui.menus;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.gui.Menu;
import com.ultimatedungeon.udx.util.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

/**
 * Boss editor for designing epic boss encounters.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class BossEditorMenu extends Menu {
    
    private final UltimateDungeonX plugin;
    private String selectedBossType = "WITHER_SKELETON";
    private int currentPhase = 1;
    private int totalPhases = 3;
    private String selectedAbility = "CHARGE_ATTACK";
    
    public BossEditorMenu(@NotNull UltimateDungeonX plugin, @NotNull Player player) {
        super(player, Component.text("Boss Editor").color(NamedTextColor.DARK_RED).decoration(TextDecoration.BOLD, true), 
              54, plugin.getMenuRegistry());
        this.plugin = plugin;
    }
    
    @Override
    protected void setupMenu() {
        inventory.clear();
        clickHandlers.clear();
        
        // Header
        ItemStack headerItem = new ItemBuilder(Material.WITHER_SKELETON_SKULL)
            .name(Component.text("Epic Boss Designer").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Design legendary boss encounters").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Create multi-phase bosses with").color(NamedTextColor.YELLOW),
                Component.text("unique abilities and mechanics").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(4, headerItem, clickType -> {
            // No action for header
        });
        
        // Boss Configuration
        setupBossConfiguration();
        
        // Phase Management
        setupPhaseManagement();
        
        // Abilities & Mechanics
        setupAbilitiesAndMechanics();
        
        // Arena Configuration
        setupArenaConfiguration();
        
        // Testing & Actions
        setupTestingAndActions();
        
        // Back button
        ItemStack backItem = new ItemBuilder(Material.ARROW)
            .name(Component.text("Back to Editor Hub").color(NamedTextColor.GRAY))
            .lore(Component.text("Click to return").color(NamedTextColor.DARK_GRAY))
            .build();
        
        setItem(49, backItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                plugin.getMenuRegistry().openEditorHub(player);
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        fillEmptySlots();
    }
    
    private void setupBossConfiguration() {
        // Boss Type
        Material bossMaterial = switch (selectedBossType) {
            case "WITHER_SKELETON" -> Material.WITHER_SKELETON_SKULL;
            case "ZOMBIE_GIANT" -> Material.ZOMBIE_HEAD;
            case "ELDER_GUARDIAN" -> Material.PRISMARINE_CRYSTALS;
            case "ENDER_DRAGON" -> Material.DRAGON_HEAD;
            case "WITHER" -> Material.NETHER_STAR;
            case "CUSTOM_GOLEM" -> Material.IRON_BLOCK;
            default -> Material.WITHER_SKELETON_SKULL;
        };
        
        ItemStack bossTypeItem = new ItemBuilder(bossMaterial)
            .name(Component.text("Boss Type: " + selectedBossType).color(NamedTextColor.DARK_RED).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Select the boss entity type").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Available boss types:").color(NamedTextColor.YELLOW),
                Component.text("• WITHER_SKELETON - Undead warrior").color(selectedBossType.equals("WITHER_SKELETON") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• ZOMBIE_GIANT - Massive zombie").color(selectedBossType.equals("ZOMBIE_GIANT") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• ELDER_GUARDIAN - Ocean tyrant").color(selectedBossType.equals("ELDER_GUARDIAN") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• ENDER_DRAGON - Void lord").color(selectedBossType.equals("ENDER_DRAGON") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• WITHER - Destruction incarnate").color(selectedBossType.equals("WITHER") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• CUSTOM_GOLEM - Iron guardian").color(selectedBossType.equals("CUSTOM_GOLEM") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to cycle boss types").color(NamedTextColor.AQUA)
            )
            .glow()
            .build();
        
        setItem(10, bossTypeItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                selectedBossType = switch (selectedBossType) {
                    case "WITHER_SKELETON" -> "ZOMBIE_GIANT";
                    case "ZOMBIE_GIANT" -> "ELDER_GUARDIAN";
                    case "ELDER_GUARDIAN" -> "ENDER_DRAGON";
                    case "ENDER_DRAGON" -> "WITHER";
                    case "WITHER" -> "CUSTOM_GOLEM";
                    default -> "WITHER_SKELETON";
                };
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
        
        // Boss Stats
        ItemStack statsItem = new ItemBuilder(Material.NETHERITE_SWORD)
            .name(Component.text("Boss Statistics").color(NamedTextColor.RED))
            .lore(
                Component.text("Configure boss attributes").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Health: 500.0 ❤❤❤").color(NamedTextColor.RED),
                Component.text("Attack Damage: 15.0 ⚔⚔").color(NamedTextColor.DARK_RED),
                Component.text("Movement Speed: 0.3 ➤").color(NamedTextColor.BLUE),
                Component.text("Armor: 8.0 🛡🛡").color(NamedTextColor.GRAY),
                Component.text("Knockback Resistance: 0.8").color(NamedTextColor.DARK_GRAY),
                Component.text("Follow Range: 32.0 👁").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Left-click: Increase values").color(NamedTextColor.GREEN),
                Component.text("Right-click: Decrease values").color(NamedTextColor.RED),
                Component.text("Shift-click: Reset to defaults").color(NamedTextColor.YELLOW)
            )
            .glow()
            .build();
        
        setItem(11, statsItem, clickType -> {
            switch (clickType) {
                case LEFT -> {
                    player.sendMessage(Component.text("Boss stats increased! Now even more challenging!")
                        .color(NamedTextColor.GREEN));
                    player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);
                }
                case RIGHT -> {
                    player.sendMessage(Component.text("Boss stats decreased! Slightly more manageable.")
                        .color(NamedTextColor.RED));
                    player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 0.8f);
                }
                case SHIFT_LEFT -> {
                    player.sendMessage(Component.text("Boss stats reset to balanced defaults!")
                        .color(NamedTextColor.YELLOW));
                    player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                }
            }
            refresh();
        });
        
        // Boss Name & Appearance
        ItemStack appearanceItem = new ItemBuilder(Material.NAME_TAG)
            .name(Component.text("Boss Identity").color(NamedTextColor.GOLD))
            .lore(
                Component.text("Configure boss name and appearance").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Name: ").color(NamedTextColor.YELLOW)
                    .append(Component.text("The Bone Lord").color(NamedTextColor.DARK_RED).decoration(TextDecoration.BOLD, true)),
                Component.text("Title: ").color(NamedTextColor.YELLOW)
                    .append(Component.text("Harbinger of Doom").color(NamedTextColor.DARK_GRAY)),
                Component.text("Size Scale: 1.5x").color(NamedTextColor.YELLOW),
                Component.text("Glow Effect: Dark Red").color(NamedTextColor.YELLOW),
                Component.text("Custom Model: Enabled").color(NamedTextColor.GREEN),
                Component.empty(),
                Component.text("Click to customize appearance").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(12, appearanceItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Opening appearance customizer...")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                // TODO: Open appearance customizer submenu
            }
        });
    }
    
    private void setupPhaseManagement() {
        // Current Phase Display
        ItemStack phaseDisplayItem = new ItemBuilder(Material.CLOCK)
            .name(Component.text("Phase " + currentPhase + " of " + totalPhases).color(NamedTextColor.AQUA).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Currently editing phase " + currentPhase).color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Phase Trigger: 75% Health").color(NamedTextColor.YELLOW),
                Component.text("Duration: Until next phase").color(NamedTextColor.YELLOW),
                Component.text("Active Abilities: 3").color(NamedTextColor.YELLOW),
                Component.text("Minion Spawns: 2 waves").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Left-click: Next phase").color(NamedTextColor.GREEN),
                Component.text("Right-click: Previous phase").color(NamedTextColor.RED)
            )
            .amount(currentPhase)
            .glow()
            .build();
        
        setItem(19, phaseDisplayItem, clickType -> {
            switch (clickType) {
                case LEFT -> {
                    currentPhase = Math.min(currentPhase + 1, totalPhases);
                    player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                    refresh();
                }
                case RIGHT -> {
                    currentPhase = Math.max(currentPhase - 1, 1);
                    player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 0.8f);
                    refresh();
                }
            }
        });
        
        // Add Phase
        ItemStack addPhaseItem = new ItemBuilder(Material.LIME_DYE)
            .name(Component.text("Add Phase").color(NamedTextColor.GREEN))
            .lore(
                Component.text("Add a new boss phase").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Current phases: " + totalPhases).color(NamedTextColor.YELLOW),
                Component.text("Maximum phases: 5").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Each phase can have unique:").color(NamedTextColor.AQUA),
                Component.text("• Health threshold trigger").color(NamedTextColor.WHITE),
                Component.text("• Special abilities").color(NamedTextColor.WHITE),
                Component.text("• Arena modifications").color(NamedTextColor.WHITE),
                Component.text("• Minion spawns").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to add phase").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(20, addPhaseItem, clickType -> {
            if (clickType == ClickType.LEFT && totalPhases < 5) {
                totalPhases++;
                currentPhase = totalPhases;
                player.sendMessage(Component.text("New phase " + totalPhases + " added!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);
                refresh();
            } else if (totalPhases >= 5) {
                player.sendMessage(Component.text("Maximum of 5 phases allowed!")
                    .color(NamedTextColor.RED));
                player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
            }
        });
        
        // Remove Phase
        ItemStack removePhaseItem = new ItemBuilder(Material.RED_DYE)
            .name(Component.text("Remove Phase").color(NamedTextColor.RED))
            .lore(
                Component.text("Remove the current phase").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("This will delete phase " + currentPhase).color(NamedTextColor.YELLOW),
                Component.text("and all its configurations").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Minimum phases: 1").color(NamedTextColor.RED),
                Component.empty(),
                Component.text("Click to remove phase").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(21, removePhaseItem, clickType -> {
            if (clickType == ClickType.LEFT && totalPhases > 1) {
                totalPhases--;
                currentPhase = Math.min(currentPhase, totalPhases);
                player.sendMessage(Component.text("Phase removed!")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 0.8f);
                refresh();
            } else if (totalPhases <= 1) {
                player.sendMessage(Component.text("Cannot remove the last phase!")
                    .color(NamedTextColor.RED));
                player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
            }
        });
    }
    
    private void setupAbilitiesAndMechanics() {
        // Ability Selection
        Material abilityMaterial = switch (selectedAbility) {
            case "CHARGE_ATTACK" -> Material.IRON_SWORD;
            case "METEOR_SHOWER" -> Material.FIRE_CHARGE;
            case "LIGHTNING_STORM" -> Material.TRIDENT;
            case "SUMMON_MINIONS" -> Material.ZOMBIE_HEAD;
            case "TELEPORT_STRIKE" -> Material.ENDER_PEARL;
            case "GROUND_SLAM" -> Material.MACE;
            case "SHIELD_BARRIER" -> Material.SHIELD;
            default -> Material.IRON_SWORD;
        };
        
        ItemStack abilityItem = new ItemBuilder(abilityMaterial)
            .name(Component.text("Ability: " + selectedAbility).color(NamedTextColor.LIGHT_PURPLE).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Configure boss abilities").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Available abilities:").color(NamedTextColor.YELLOW),
                Component.text("• CHARGE_ATTACK - Rush at players").color(selectedAbility.equals("CHARGE_ATTACK") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• METEOR_SHOWER - Rain fire from sky").color(selectedAbility.equals("METEOR_SHOWER") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• LIGHTNING_STORM - Electric chaos").color(selectedAbility.equals("LIGHTNING_STORM") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• SUMMON_MINIONS - Call reinforcements").color(selectedAbility.equals("SUMMON_MINIONS") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• TELEPORT_STRIKE - Instant attacks").color(selectedAbility.equals("TELEPORT_STRIKE") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• GROUND_SLAM - AoE knockback").color(selectedAbility.equals("GROUND_SLAM") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• SHIELD_BARRIER - Damage immunity").color(selectedAbility.equals("SHIELD_BARRIER") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to cycle abilities").color(NamedTextColor.AQUA)
            )
            .glow()
            .build();
        
        setItem(28, abilityItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                selectedAbility = switch (selectedAbility) {
                    case "CHARGE_ATTACK" -> "METEOR_SHOWER";
                    case "METEOR_SHOWER" -> "LIGHTNING_STORM";
                    case "LIGHTNING_STORM" -> "SUMMON_MINIONS";
                    case "SUMMON_MINIONS" -> "TELEPORT_STRIKE";
                    case "TELEPORT_STRIKE" -> "GROUND_SLAM";
                    case "GROUND_SLAM" -> "SHIELD_BARRIER";
                    default -> "CHARGE_ATTACK";
                };
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
        
        // Ability Configuration
        ItemStack configItem = new ItemBuilder(Material.ENCHANTED_BOOK)
            .name(Component.text("Ability Settings").color(NamedTextColor.AQUA))
            .lore(
                Component.text("Configure ability parameters").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Cooldown: 15 seconds").color(NamedTextColor.YELLOW),
                Component.text("Damage: 12.0 ⚔").color(NamedTextColor.RED),
                Component.text("Range: 8.0 blocks").color(NamedTextColor.BLUE),
                Component.text("Telegraph Time: 2.0s").color(NamedTextColor.GOLD),
                Component.text("Particle Effect: Enabled").color(NamedTextColor.GREEN),
                Component.text("Sound Effect: Epic").color(NamedTextColor.GREEN),
                Component.empty(),
                Component.text("Click to configure").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(29, configItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Opening ability configuration...")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                // TODO: Open ability config submenu
            }
        });
        
        // Telegraph System
        ItemStack telegraphItem = new ItemBuilder(Material.REDSTONE_TORCH)
            .name(Component.text("Telegraph System").color(NamedTextColor.RED))
            .lore(
                Component.text("Configure ability warnings").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Telegraph methods:").color(NamedTextColor.YELLOW),
                Component.text("✓ Particle circles").color(NamedTextColor.GREEN),
                Component.text("✓ Title announcements").color(NamedTextColor.GREEN),
                Component.text("✓ Sound warnings").color(NamedTextColor.GREEN),
                Component.text("✓ Boss bar messages").color(NamedTextColor.GREEN),
                Component.text("✗ Ground markers").color(NamedTextColor.RED),
                Component.empty(),
                Component.text("Warning time: 2.0 seconds").color(NamedTextColor.AQUA),
                Component.empty(),
                Component.text("Click to configure telegraphs").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(30, telegraphItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Telegraph system configured!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                refresh();
            }
        });
    }
    
    private void setupArenaConfiguration() {
        // Arena Effects
        ItemStack arenaItem = new ItemBuilder(Material.BEACON)
            .name(Component.text("Arena Mechanics").color(NamedTextColor.GOLD))
            .lore(
                Component.text("Configure arena-wide effects").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Active mechanics:").color(NamedTextColor.YELLOW),
                Component.text("• Barrier walls during fight").color(NamedTextColor.WHITE),
                Component.text("• Damage zones in corners").color(NamedTextColor.WHITE),
                Component.text("• Healing fountains (phase 3)").color(NamedTextColor.WHITE),
                Component.text("• Falling debris hazards").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Environmental effects:").color(NamedTextColor.AQUA),
                Component.text("• Darkness during phase 2").color(NamedTextColor.WHITE),
                Component.text("• Lightning strikes").color(NamedTextColor.WHITE),
                Component.text("• Particle storms").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to configure arena").color(NamedTextColor.AQUA)
            )
            .glow()
            .build();
        
        setItem(37, arenaItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Opening arena configurator...")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                // TODO: Open arena config submenu
            }
        });
        
        // Minion Spawns
        ItemStack minionsItem = new ItemBuilder(Material.SKELETON_SKULL)
            .name(Component.text("Minion Spawns").color(NamedTextColor.GRAY))
            .lore(
                Component.text("Configure boss minions").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Phase 1 minions:").color(NamedTextColor.YELLOW),
                Component.text("• 3x Skeleton Archers").color(NamedTextColor.WHITE),
                Component.text("• Spawn at 50% health").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Phase 2 minions:").color(NamedTextColor.YELLOW),
                Component.text("• 2x Wither Skeletons").color(NamedTextColor.WHITE),
                Component.text("• Spawn every 30 seconds").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Phase 3 minions:").color(NamedTextColor.YELLOW),
                Component.text("• 1x Elite Guardian").color(NamedTextColor.WHITE),
                Component.text("• Spawn at phase start").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to configure minions").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(38, minionsItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Opening minion configurator...")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                // TODO: Open minion config submenu
            }
        });
        
        // Boss Bar & UI
        ItemStack bossBarItem = new ItemBuilder(Material.EXPERIENCE_BOTTLE)
            .name(Component.text("Boss Bar & UI").color(NamedTextColor.GREEN))
            .lore(
                Component.text("Configure boss UI elements").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Boss bar settings:").color(NamedTextColor.YELLOW),
                Component.text("• Color: Red → Purple → Gold").color(NamedTextColor.WHITE),
                Component.text("• Style: Segmented").color(NamedTextColor.WHITE),
                Component.text("• Phase indicators: Enabled").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Announcements:").color(NamedTextColor.AQUA),
                Component.text("• Phase transitions").color(NamedTextColor.WHITE),
                Component.text("• Ability warnings").color(NamedTextColor.WHITE),
                Component.text("• Victory/defeat messages").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to configure UI").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(39, bossBarItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Boss UI configured!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                refresh();
            }
        });
    }
    
    private void setupTestingAndActions() {
        // Test Boss
        ItemStack testItem = new ItemBuilder(Material.DRAGON_EGG)
            .name(Component.text("Test Boss Fight").color(NamedTextColor.LIGHT_PURPLE).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Spawn the boss for testing").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("This will create a test instance").color(NamedTextColor.YELLOW),
                Component.text("with the configured boss and").color(NamedTextColor.YELLOW),
                Component.text("all its phases and abilities").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Test features:").color(NamedTextColor.AQUA),
                Component.text("• Full boss encounter").color(NamedTextColor.WHITE),
                Component.text("• All phases and abilities").color(NamedTextColor.WHITE),
                Component.text("• Arena mechanics").color(NamedTextColor.WHITE),
                Component.text("• Minion spawns").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to start test fight").color(NamedTextColor.AQUA)
            )
            .glow()
            .build();
        
        setItem(46, testItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Preparing epic boss encounter...")
                    .color(NamedTextColor.LIGHT_PURPLE));
                player.sendMessage(Component.text("Teleporting to boss arena...")
                    .color(NamedTextColor.GOLD));
                player.playSound(player.getLocation(), Sound.ENTITY_WITHER_SPAWN, 0.5f, 1.2f);
                close();
            }
        });
        
        // Save Boss
        ItemStack saveItem = new ItemBuilder(Material.WRITABLE_BOOK)
            .name(Component.text("Save Boss Config").color(NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Save boss configuration").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("This will save:").color(NamedTextColor.YELLOW),
                Component.text("• Boss type and statistics").color(NamedTextColor.WHITE),
                Component.text("• All phases and triggers").color(NamedTextColor.WHITE),
                Component.text("• Abilities and mechanics").color(NamedTextColor.WHITE),
                Component.text("• Arena configuration").color(NamedTextColor.WHITE),
                Component.text("• Minion spawns and UI").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to save boss").color(NamedTextColor.AQUA)
            )
            .glow()
            .build();
        
        setItem(47, saveItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Saving legendary boss configuration...")
                    .color(NamedTextColor.YELLOW));
                player.sendMessage(Component.text("✓ Boss saved as 'the_bone_lord.json'")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);
            }
        });
        
        // Load Boss
        ItemStack loadItem = new ItemBuilder(Material.BOOK)
            .name(Component.text("Load Boss Config").color(NamedTextColor.BLUE))
            .lore(
                Component.text("Load existing boss configuration").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Available bosses: 5").color(NamedTextColor.YELLOW),
                Component.text("• the_bone_lord.json").color(NamedTextColor.WHITE),
                Component.text("• flame_tyrant.json").color(NamedTextColor.WHITE),
                Component.text("• void_guardian.json").color(NamedTextColor.WHITE),
                Component.text("• storm_bringer.json").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to browse bosses").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(48, loadItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Opening boss library...")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                // TODO: Open boss browser menu
            }
        });
    }
}

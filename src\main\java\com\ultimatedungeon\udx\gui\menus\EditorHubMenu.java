package com.ultimatedungeon.udx.gui.menus;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.gui.Menu;
import com.ultimatedungeon.udx.util.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

/**
 * Central hub for all content editing tools.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class EditorHubMenu extends Menu {
    
    private final UltimateDungeonX plugin;
    
    public EditorHubMenu(@NotNull UltimateDungeonX plugin, @NotNull Player player) {
        super(player, Component.text("Content Editor Hub").color(NamedTextColor.LIGHT_PURPLE).decoration(TextDecoration.BOLD, true), 
              45, plugin.getMenuRegistry());
        this.plugin = plugin;
    }
    
    @Override
    protected void setupMenu() {
        inventory.clear();
        clickHandlers.clear();
        
        // Header
        ItemStack headerItem = new ItemBuilder(Material.CRAFTING_TABLE)
            .name(Component.text("UltimateDungeonX Editor Hub").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Create and edit dungeon content").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Build amazing dungeons with our").color(NamedTextColor.YELLOW),
                Component.text("powerful in-game editing tools").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(4, headerItem, clickType -> {
            // No action for header
        });
        
        // Room Editor
        ItemStack roomEditorItem = new ItemBuilder(Material.BRICKS)
            .name(Component.text("Room Editor").color(NamedTextColor.AQUA).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Create and edit room templates").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Features:").color(NamedTextColor.YELLOW),
                Component.text("• Select regions with WorldEdit-style tools").color(NamedTextColor.WHITE),
                Component.text("• Place connector markers").color(NamedTextColor.WHITE),
                Component.text("• Add spawn points and triggers").color(NamedTextColor.WHITE),
                Component.text("• Set room metadata and tags").color(NamedTextColor.WHITE),
                Component.text("• Export to UDX format").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to open Room Editor").color(NamedTextColor.AQUA)
            )
            .glow()
            .build();
        
        setItem(19, roomEditorItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                plugin.getMenuRegistry().openRoomEditor(player);
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        // Spawner Editor
        ItemStack spawnerEditorItem = new ItemBuilder(Material.SPAWNER)
            .name(Component.text("Spawner Editor").color(NamedTextColor.RED).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Configure mob spawners and waves").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Features:").color(NamedTextColor.YELLOW),
                Component.text("• Design custom mob definitions").color(NamedTextColor.WHITE),
                Component.text("• Configure spawn waves and triggers").color(NamedTextColor.WHITE),
                Component.text("• Set mob behaviors and AI").color(NamedTextColor.WHITE),
                Component.text("• Test spawners in sandbox mode").color(NamedTextColor.WHITE),
                Component.text("• Export spawner configurations").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to open Spawner Editor").color(NamedTextColor.AQUA)
            )
            .glow()
            .build();
        
        setItem(21, spawnerEditorItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                plugin.getMenuRegistry().openSpawnerEditor(player);
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        // Boss Editor
        ItemStack bossEditorItem = new ItemBuilder(Material.WITHER_SKELETON_SKULL)
            .name(Component.text("Boss Editor").color(NamedTextColor.DARK_RED).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Design epic boss encounters").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Features:").color(NamedTextColor.YELLOW),
                Component.text("• Create multi-phase boss fights").color(NamedTextColor.WHITE),
                Component.text("• Design custom abilities and telegraphs").color(NamedTextColor.WHITE),
                Component.text("• Configure arena mechanics").color(NamedTextColor.WHITE),
                Component.text("• Set boss AI and behaviors").color(NamedTextColor.WHITE),
                Component.text("• Test boss encounters").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to open Boss Editor").color(NamedTextColor.AQUA)
            )
            .glow()
            .build();
        
        setItem(23, bossEditorItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                plugin.getMenuRegistry().openBossEditor(player);
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        // Loot Editor
        ItemStack lootEditorItem = new ItemBuilder(Material.CHEST)
            .name(Component.text("Loot Editor").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Create rewarding loot systems").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Features:").color(NamedTextColor.YELLOW),
                Component.text("• Design weighted loot tables").color(NamedTextColor.WHITE),
                Component.text("• Configure rarity tiers and jackpots").color(NamedTextColor.WHITE),
                Component.text("• Set chest regeneration rules").color(NamedTextColor.WHITE),
                Component.text("• Create completion rewards").color(NamedTextColor.WHITE),
                Component.text("• Test loot generation").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to open Loot Editor").color(NamedTextColor.AQUA)
            )
            .glow()
            .build();
        
        setItem(25, lootEditorItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                plugin.getMenuRegistry().openLootEditor(player);
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        // Quick Actions
        setupQuickActions();
        
        // Back button
        ItemStack backItem = new ItemBuilder(Material.ARROW)
            .name(Component.text("Back to Admin Hub").color(NamedTextColor.GRAY))
            .lore(Component.text("Click to return").color(NamedTextColor.DARK_GRAY))
            .build();
        
        setItem(40, backItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                plugin.getMenuRegistry().openAdminHub(player);
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        fillEmptySlots();
    }
    
    private void setupQuickActions() {
        // Build World Access
        ItemStack buildWorldItem = new ItemBuilder(Material.GRASS_BLOCK)
            .name(Component.text("Build World").color(NamedTextColor.GREEN))
            .lore(
                Component.text("Teleport to the build world").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Use the build world to create").color(NamedTextColor.YELLOW),
                Component.text("room templates and test designs").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Click to teleport").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(37, buildWorldItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Teleporting to build world...")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);
                close();
                // TODO: Implement teleport to build world
            }
        });
        
        // Import/Export Tools
        ItemStack importExportItem = new ItemBuilder(Material.BOOK)
            .name(Component.text("Import/Export").color(NamedTextColor.BLUE))
            .lore(
                Component.text("Import and export content").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("• Import rooms from schematics").color(NamedTextColor.WHITE),
                Component.text("• Export UDX content packs").color(NamedTextColor.WHITE),
                Component.text("• Share content with other servers").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to open tools").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(39, importExportItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Import/Export tools coming soon!")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        // Documentation
        ItemStack docsItem = new ItemBuilder(Material.WRITABLE_BOOK)
            .name(Component.text("Documentation").color(NamedTextColor.YELLOW))
            .lore(
                Component.text("Editor guides and tutorials").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("• Room creation guide").color(NamedTextColor.WHITE),
                Component.text("• Spawner configuration tutorial").color(NamedTextColor.WHITE),
                Component.text("• Boss design best practices").color(NamedTextColor.WHITE),
                Component.text("• Loot balancing tips").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to view guides").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(41, docsItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Opening documentation...")
                    .color(NamedTextColor.YELLOW));
                player.sendMessage(Component.text("Visit our wiki for detailed guides!")
                    .color(NamedTextColor.AQUA));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        // Validation Tools
        ItemStack validationItem = new ItemBuilder(Material.REDSTONE_TORCH)
            .name(Component.text("Validation Tools").color(NamedTextColor.RED))
            .lore(
                Component.text("Test and validate your content").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("• Validate room connectivity").color(NamedTextColor.WHITE),
                Component.text("• Test spawner configurations").color(NamedTextColor.WHITE),
                Component.text("• Simulate boss encounters").color(NamedTextColor.WHITE),
                Component.text("• Check loot table balance").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to run validation").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(43, validationItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Running content validation...")
                    .color(NamedTextColor.YELLOW));
                player.sendMessage(Component.text("✓ All content appears valid!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);
            }
        });
    }
}

package com.ultimatedungeon.udx.api.progression;

import org.jetbrains.annotations.NotNull;
import java.time.Instant;
import java.util.UUID;

public record AchievementProgressData(
    @NotNull UUID playerId,
    @NotNull String achievementId,
    double progress,
    boolean completed,
    @NotNull Instant startedAt,
    @NotNull Instant completedAt
) {
    public AchievementProgressData {
        if (playerId == null) throw new IllegalArgumentException("Player ID cannot be null");
        if (achievementId == null || achievementId.isBlank()) throw new IllegalArgumentException("Achievement ID cannot be null or blank");
        if (progress < 0 || progress > 100) throw new IllegalArgumentException("Progress must be between 0 and 100");
        if (startedAt == null) throw new IllegalArgumentException("Started at cannot be null");
        if (completedAt == null) throw new IllegalArgumentException("Completed at cannot be null");
    }
}

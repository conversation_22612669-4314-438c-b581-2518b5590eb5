package com.ultimatedungeon.udx.affix;

/**
 * Represents difficulty tiers for dungeons.
 * 
 * <p>Difficulty tiers scale mob stats, rewards, and requirements
 * to provide appropriate challenges for different player skill levels.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public enum DifficultyTier {
    
    /**
     * Normal difficulty - baseline experience.
     */
    NORMAL("Normal", "§f", 1.0, 1.0, 1.0, 1.0, 0),
    
    /**
     * Hard difficulty - increased challenge.
     */
    HARD("Hard", "§e", 1.3, 1.2, 1.5, 1.3, 5),
    
    /**
     * Mythic difficulty - significant challenge.
     */
    MYTHIC("Mythic", "§c", 1.8, 1.5, 2.0, 1.8, 15),
    
    /**
     * Nightmare difficulty - extreme challenge.
     */
    NIGHTMARE("Nightmare", "§4", 2.5, 2.0, 3.0, 2.5, 30);
    
    private final String displayName;
    private final String colorCode;
    private final double healthMultiplier;
    private final double damageMultiplier;
    private final double rewardMultiplier;
    private final double experienceMultiplier;
    private final int levelRequirement;
    
    DifficultyTier(String displayName, String colorCode, double healthMultiplier, 
                   double damageMultiplier, double rewardMultiplier, 
                   double experienceMultiplier, int levelRequirement) {
        this.displayName = displayName;
        this.colorCode = colorCode;
        this.healthMultiplier = healthMultiplier;
        this.damageMultiplier = damageMultiplier;
        this.rewardMultiplier = rewardMultiplier;
        this.experienceMultiplier = experienceMultiplier;
        this.levelRequirement = levelRequirement;
    }
    
    /**
     * Gets the display name.
     * 
     * @return the display name
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * Gets the color code.
     * 
     * @return the color code
     */
    public String getColorCode() {
        return colorCode;
    }
    
    /**
     * Gets the health multiplier for mobs.
     * 
     * @return the health multiplier
     */
    public double getHealthMultiplier() {
        return healthMultiplier;
    }
    
    /**
     * Gets the damage multiplier for mobs.
     * 
     * @return the damage multiplier
     */
    public double getDamageMultiplier() {
        return damageMultiplier;
    }
    
    /**
     * Gets the reward multiplier.
     * 
     * @return the reward multiplier
     */
    public double getRewardMultiplier() {
        return rewardMultiplier;
    }
    
    /**
     * Gets the experience multiplier.
     * 
     * @return the experience multiplier
     */
    public double getExperienceMultiplier() {
        return experienceMultiplier;
    }
    
    /**
     * Gets the minimum level requirement.
     * 
     * @return the level requirement
     */
    public int getLevelRequirement() {
        return levelRequirement;
    }
    
    /**
     * Gets the formatted display name with color.
     * 
     * @return the formatted display name
     */
    public String getFormattedName() {
        return colorCode + displayName;
    }
    
    /**
     * Gets the difficulty tier by name (case-insensitive).
     * 
     * @param name the difficulty name
     * @return the difficulty tier, or NORMAL if not found
     */
    public static DifficultyTier fromString(String name) {
        for (DifficultyTier tier : values()) {
            if (tier.name().equalsIgnoreCase(name) || 
                tier.displayName.equalsIgnoreCase(name)) {
                return tier;
            }
        }
        return NORMAL;
    }
    
    /**
     * Checks if this difficulty is harder than another.
     * 
     * @param other the other difficulty
     * @return true if this is harder
     */
    public boolean isHarderThan(DifficultyTier other) {
        return this.ordinal() > other.ordinal();
    }
    
    /**
     * Gets the next difficulty tier.
     * 
     * @return the next tier, or this tier if already at maximum
     */
    public DifficultyTier getNext() {
        DifficultyTier[] values = values();
        int nextIndex = this.ordinal() + 1;
        return nextIndex < values.length ? values[nextIndex] : this;
    }
    
    /**
     * Gets the previous difficulty tier.
     * 
     * @return the previous tier, or this tier if already at minimum
     */
    public DifficultyTier getPrevious() {
        int prevIndex = this.ordinal() - 1;
        return prevIndex >= 0 ? values()[prevIndex] : this;
    }
    
    /**
     * Scales a value by the party size.
     * 
     * @param baseValue the base value
     * @param partySize the party size
     * @param maxPartySize the maximum party size
     * @return the scaled value
     */
    public double scaleForPartySize(double baseValue, int partySize, int maxPartySize) {
        if (partySize <= 1 || maxPartySize <= 1) {
            return baseValue;
        }
        
        // Scale difficulty based on party size (more players = slightly harder)
        double partyScale = 1.0 + (partySize - 1) * 0.15; // 15% per additional player
        return baseValue * partyScale;
    }
}

package com.ultimatedungeon.udx.combat;

import org.bukkit.entity.LivingEntity;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Map;

/**
 * Represents a status effect that can be applied to entities.
 * 
 * <p>Status effects can modify entity behavior, stats, or apply
 * periodic effects like damage or healing over time.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public record StatusEffect(
    @NotNull String id,
    @NotNull String name,
    @NotNull StatusEffectType type,
    int duration,
    int amplifier,
    @NotNull Map<String, Object> parameters,
    @Nullable String source
) {
    
    /**
     * Types of status effects.
     */
    public enum StatusEffectType {
        /**
         * Beneficial effects that help the target.
         */
        BUFF,
        
        /**
         * Harmful effects that hinder the target.
         */
        DEBUFF,
        
        /**
         * Neutral effects that don't help or harm.
         */
        NEUTRAL,
        
        /**
         * Damage over time effects.
         */
        DOT,
        
        /**
         * Healing over time effects.
         */
        HOT,
        
        /**
         * Crowd control effects.
         */
        CC
    }
    
    /**
     * Applies this status effect to an entity.
     * 
     * @param entity the entity to apply the effect to
     */
    public void apply(@NotNull LivingEntity entity) {
        // Implementation would depend on the specific effect
        // This is handled by the CombatService
    }
    
    /**
     * Removes this status effect from an entity.
     * 
     * @param entity the entity to remove the effect from
     */
    public void remove(@NotNull LivingEntity entity) {
        // Implementation would depend on the specific effect
        // This is handled by the CombatService
    }
    
    /**
     * Ticks this status effect (called periodically).
     * 
     * @param entity the entity with this effect
     * @return true if the effect should continue, false if it should be removed
     */
    public boolean tick(@NotNull LivingEntity entity) {
        // Implementation would depend on the specific effect
        // This is handled by the CombatService
        return duration > 0;
    }
    
    /**
     * Gets the display name with color coding.
     * 
     * @return the colored display name
     */
    @NotNull
    public String getColoredName() {
        String color = switch (type) {
            case BUFF -> "§a";
            case DEBUFF -> "§c";
            case DOT -> "§4";
            case HOT -> "§2";
            case CC -> "§6";
            case NEUTRAL -> "§7";
        };
        return color + name;
    }
    
    /**
     * Checks if this effect is beneficial.
     * 
     * @return true if beneficial, false otherwise
     */
    public boolean isBeneficial() {
        return type == StatusEffectType.BUFF || type == StatusEffectType.HOT;
    }
    
    /**
     * Checks if this effect is harmful.
     * 
     * @return true if harmful, false otherwise
     */
    public boolean isHarmful() {
        return type == StatusEffectType.DEBUFF || type == StatusEffectType.DOT || type == StatusEffectType.CC;
    }
    
    /**
     * Gets the remaining duration in ticks.
     * 
     * @return the remaining duration
     */
    public int getRemainingDuration() {
        return duration;
    }
    
    /**
     * Creates a new status effect with reduced duration.
     * 
     * @param ticks the number of ticks to reduce
     * @return a new status effect with reduced duration
     */
    @NotNull
    public StatusEffect withReducedDuration(int ticks) {
        return new StatusEffect(
            id,
            name,
            type,
            Math.max(0, duration - ticks),
            amplifier,
            parameters,
            source
        );
    }
    
    /**
     * Creates a new status effect with modified amplifier.
     * 
     * @param newAmplifier the new amplifier value
     * @return a new status effect with the modified amplifier
     */
    @NotNull
    public StatusEffect withAmplifier(int newAmplifier) {
        return new StatusEffect(
            id,
            name,
            type,
            duration,
            newAmplifier,
            parameters,
            source
        );
    }
    
    /**
     * Checks if this effect can stack with another effect.
     * 
     * @param other the other effect to check
     * @return true if they can stack, false otherwise
     */
    public boolean canStackWith(@NotNull StatusEffect other) {
        // Same effect ID but different sources can stack
        return id.equals(other.id) && !java.util.Objects.equals(source, other.source);
    }
    
    /**
     * Gets a parameter value as a specific type.
     * 
     * @param key the parameter key
     * @param defaultValue the default value if not found
     * @param <T> the type of the parameter
     * @return the parameter value or default
     */
    @SuppressWarnings("unchecked")
    public <T> T getParameter(@NotNull String key, @NotNull T defaultValue) {
        Object value = parameters.get(key);
        if (value != null && defaultValue.getClass().isInstance(value)) {
            return (T) value;
        }
        return defaultValue;
    }
    
    /**
     * Validates the status effect configuration.
     * 
     * @throws IllegalArgumentException if the configuration is invalid
     */
    public void validate() {
        if (id.isBlank()) {
            throw new IllegalArgumentException("Status effect ID cannot be blank");
        }
        
        if (name.isBlank()) {
            throw new IllegalArgumentException("Status effect name cannot be blank");
        }
        
        if (duration < 0) {
            throw new IllegalArgumentException("Status effect duration cannot be negative");
        }
        
        if (amplifier < 0) {
            throw new IllegalArgumentException("Status effect amplifier cannot be negative");
        }
    }
}

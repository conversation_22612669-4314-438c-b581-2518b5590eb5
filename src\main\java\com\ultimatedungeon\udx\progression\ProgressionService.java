package com.ultimatedungeon.udx.progression;

import com.ultimatedungeon.udx.data.DataService;
import com.ultimatedungeon.udx.util.SchedulerUtil;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitTask;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;

/**
 * Manages player progression, achievements, and seasonal systems.
 * 
 * <p>This service handles loading/saving player profiles, tracking achievements,
 * managing seasonal progression, and providing leaderboard functionality.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class ProgressionService implements Listener {
    
    private final Plugin plugin;
    private final DataService dataService;
    private final SchedulerUtil schedulerUtil;
    
    // In-memory cache of player profiles
    private final Map<UUID, PlayerProfile> profileCache;
    
    // Achievement definitions
    private final Map<String, Achievement> achievements;
    
    // Season management
    private int currentSeason;
    private final Map<String, List<LeaderboardEntry>> leaderboards;
    
    // Background tasks
    private BukkitTask saveTask;
    private BukkitTask leaderboardUpdateTask;
    
    public ProgressionService(@NotNull Plugin plugin, @NotNull DataService dataService, 
                            @NotNull SchedulerUtil schedulerUtil) {
        this.plugin = plugin;
        this.dataService = dataService;
        this.schedulerUtil = schedulerUtil;
        this.profileCache = new ConcurrentHashMap<>();
        this.achievements = new HashMap<>();
        this.leaderboards = new ConcurrentHashMap<>();
        this.currentSeason = 1;
        
        initialize();
    }
    
    /**
     * Initializes the progression service.
     */
    private void initialize() {
        plugin.getLogger().info("Initializing ProgressionService...");
        
        // Load achievements
        loadAchievements();
        
        // Load current season
        loadCurrentSeason();
        
        // Start background tasks
        startBackgroundTasks();
        
        // Register event listeners
        Bukkit.getPluginManager().registerEvents(this, plugin);
        
        plugin.getLogger().info("ProgressionService initialized");
    }
    
    /**
     * Loads achievement definitions.
     */
    private void loadAchievements() {
        // Define core achievements
        achievements.put("first_dungeon", new Achievement(
            "first_dungeon", "First Steps", "Complete your first dungeon",
            1, "STONE_SWORD", Arrays.asList("Complete any dungeon on any difficulty")
        ));
        
        achievements.put("speed_runner", new Achievement(
            "speed_runner", "Speed Runner", "Complete a dungeon in under 5 minutes",
            1, "GOLDEN_BOOTS", Arrays.asList("Complete any dungeon in under 5 minutes")
        ));
        
        achievements.put("dungeon_master", new Achievement(
            "dungeon_master", "Dungeon Master", "Complete 100 dungeons",
            100, "DIAMOND_SWORD", Arrays.asList("Complete 100 dungeons total")
        ));
        
        achievements.put("nightmare_conqueror", new Achievement(
            "nightmare_conqueror", "Nightmare Conqueror", "Complete a dungeon on Nightmare difficulty",
            1, "NETHERITE_SWORD", Arrays.asList("Complete any dungeon on Nightmare difficulty")
        ));
        
        achievements.put("treasure_hunter", new Achievement(
            "treasure_hunter", "Treasure Hunter", "Open 500 chests",
            500, "CHEST", Arrays.asList("Open 500 chests in dungeons")
        ));
        
        achievements.put("team_player", new Achievement(
            "team_player", "Team Player", "Revive 50 teammates",
            50, "GOLDEN_APPLE", Arrays.asList("Revive 50 teammates in dungeons")
        ));
        
        achievements.put("boss_slayer", new Achievement(
            "boss_slayer", "Boss Slayer", "Defeat 25 bosses",
            25, "WITHER_SKELETON_SKULL", Arrays.asList("Defeat 25 dungeon bosses")
        ));
        
        achievements.put("perfectionist", new Achievement(
            "perfectionist", "Perfectionist", "Complete a dungeon without dying",
            1, "TOTEM_OF_UNDYING", Arrays.asList("Complete any dungeon without dying")
        ));
        
        plugin.getLogger().info("Loaded " + achievements.size() + " achievements");
    }
    
    /**
     * Loads the current season information.
     */
    private void loadCurrentSeason() {
        // TODO: Load from database or config
        currentSeason = 1;
        plugin.getLogger().info("Current season: " + currentSeason);
    }
    
    /**
     * Starts background tasks for saving and leaderboard updates.
     */
    private void startBackgroundTasks() {
        // Auto-save profiles every 5 minutes
        saveTask = schedulerUtil.runTaskTimerAsynchronously(() -> {
            saveAllProfiles();
        }, 20L * 60L * 5L, 20L * 60L * 5L);
        
        // Update leaderboards every 10 minutes
        leaderboardUpdateTask = schedulerUtil.runTaskTimerAsynchronously(() -> {
            updateLeaderboards();
        }, 20L * 60L * 10L, 20L * 60L * 10L);
    }
    
    /**
     * Gets or loads a player's profile.
     * 
     * @param playerId the player UUID
     * @return the player profile, or null if not found
     */
    @Nullable
    public PlayerProfile getPlayerProfile(@NotNull UUID playerId) {
        return profileCache.get(playerId);
    }
    
    /**
     * Gets or creates a player's profile.
     * 
     * @param playerId the player UUID
     * @param playerName the player name
     * @return the player profile
     */
    @NotNull
    public PlayerProfile getOrCreatePlayerProfile(@NotNull UUID playerId, @NotNull String playerName) {
        return profileCache.computeIfAbsent(playerId, k -> {
            PlayerProfile profile = loadPlayerProfile(playerId);
            if (profile == null) {
                profile = new PlayerProfile(playerId, playerName);
                plugin.getLogger().info("Created new profile for player: " + playerName);
            }
            return profile;
        });
    }
    
    /**
     * Loads a player profile from the database.
     * 
     * @param playerId the player UUID
     * @return the loaded profile, or null if not found
     */
    @Nullable
    private PlayerProfile loadPlayerProfile(@NotNull UUID playerId) {
        try {
            // TODO: Implement conversion between data.PlayerProfile and progression.PlayerProfile
            // For now, return null to create new profiles
            return null;
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to load player profile: " + playerId, e);
            return null;
        }
    }
    
    /**
     * Saves a player profile to the database.
     * 
     * @param profile the profile to save
     */
    public void savePlayerProfile(@NotNull PlayerProfile profile) {
        schedulerUtil.runTaskAsynchronously(() -> {
            try {
                // TODO: Implement conversion between progression.PlayerProfile and data.PlayerProfile
                // dataService.savePlayerProfile(profile);
                plugin.getLogger().info("Saved profile for player: " + profile.getPlayerName());
            } catch (Exception e) {
                plugin.getLogger().log(Level.SEVERE, "Failed to save player profile: " + profile.getPlayerId(), e);
            }
        });
    }
    
    /**
     * Saves all cached player profiles.
     */
    private void saveAllProfiles() {
        if (profileCache.isEmpty()) return;
        
        plugin.getLogger().info("Saving " + profileCache.size() + " player profiles...");
        
        for (PlayerProfile profile : profileCache.values()) {
            try {
                // TODO: Implement conversion between progression.PlayerProfile and data.PlayerProfile
                // dataService.savePlayerProfile(profile);
                plugin.getLogger().info("Saved profile for player: " + profile.getPlayerName());
            } catch (Exception e) {
                plugin.getLogger().log(Level.SEVERE, "Failed to save profile: " + profile.getPlayerId(), e);
            }
        }
        
        plugin.getLogger().info("Profile save complete");
    }
    
    /**
     * Records a dungeon completion for a player.
     * 
     * @param playerId the player UUID
     * @param dungeonId the dungeon ID
     * @param difficulty the difficulty tier
     * @param completionTime the completion time in milliseconds
     * @param deaths the number of deaths
     * @param score the score achieved
     */
    public void recordDungeonCompletion(@NotNull UUID playerId, @NotNull String dungeonId,
                                      @NotNull String difficulty, long completionTime, 
                                      int deaths, int score) {
        Player player = Bukkit.getPlayer(playerId);
        if (player == null) return;
        
        PlayerProfile profile = getOrCreatePlayerProfile(playerId, player.getName());
        profile.updateDungeonProgress(dungeonId, difficulty, completionTime, deaths, score);
        profile.addSeasonScore(score);
        
        // Check achievements
        checkAchievements(profile);
        
        // Save profile
        savePlayerProfile(profile);
        
        plugin.getLogger().info("Recorded completion for " + player.getName() + 
            ": " + dungeonId + " (" + difficulty + ") - Score: " + score);
    }
    
    /**
     * Checks and unlocks achievements for a player.
     * 
     * @param profile the player profile
     */
    private void checkAchievements(@NotNull PlayerProfile profile) {
        PlayerStatistics stats = profile.getStatistics();
        
        // First dungeon
        if (stats.getDungeonsCompleted() >= 1) {
            unlockAchievement(profile, "first_dungeon");
        }
        
        // Dungeon master
        if (stats.getDungeonsCompleted() >= 100) {
            unlockAchievement(profile, "dungeon_master");
        }
        
        // Nightmare conqueror
        if ("NIGHTMARE".equals(profile.getHighestDifficultyCompleted())) {
            unlockAchievement(profile, "nightmare_conqueror");
        }
        
        // Treasure hunter
        if (stats.getChestsOpened() >= 500) {
            unlockAchievement(profile, "treasure_hunter");
        }
        
        // Team player
        if (stats.getRevivesGiven() >= 50) {
            unlockAchievement(profile, "team_player");
        }
        
        // Boss slayer
        if (stats.getBossesKilled() >= 25) {
            unlockAchievement(profile, "boss_slayer");
        }
    }
    
    /**
     * Unlocks an achievement for a player.
     * 
     * @param profile the player profile
     * @param achievementId the achievement ID
     */
    private void unlockAchievement(@NotNull PlayerProfile profile, @NotNull String achievementId) {
        if (profile.unlockAchievement(achievementId)) {
            Achievement achievement = achievements.get(achievementId);
            if (achievement != null) {
                Player player = Bukkit.getPlayer(profile.getPlayerId());
                if (player != null) {
                    player.sendMessage("§6§lACHIEVEMENT UNLOCKED: §e" + achievement.getName());
                    player.sendMessage("§7" + achievement.getDescription());
                    
                    // Play sound and effects
                    player.playSound(player.getLocation(), "entity.player.levelup", 1.0f, 1.0f);
                }
                
                plugin.getLogger().info("Player " + profile.getPlayerName() + 
                    " unlocked achievement: " + achievement.getName());
            }
        }
    }
    
    /**
     * Gets the leaderboard for a specific category.
     * 
     * @param category the leaderboard category
     * @param limit the maximum number of entries
     * @return the leaderboard entries
     */
    @NotNull
    public List<LeaderboardEntry> getLeaderboard(@NotNull String category, int limit) {
        List<LeaderboardEntry> entries = leaderboards.getOrDefault(category, new ArrayList<>());
        return entries.stream().limit(limit).toList();
    }
    
    /**
     * Updates all leaderboards.
     */
    private void updateLeaderboards() {
        plugin.getLogger().info("Updating leaderboards...");
        
        // Season score leaderboard
        List<LeaderboardEntry> seasonEntries = profileCache.values().stream()
            .map(profile -> new LeaderboardEntry(
                profile.getPlayerId(),
                profile.getPlayerName(),
                profile.getCurrentSeasonScore()
            ))
            .sorted((a, b) -> Integer.compare(b.getValue(), a.getValue()))
            .toList();
        leaderboards.put("season_score", seasonEntries);
        
        // Total dungeons completed
        List<LeaderboardEntry> dungeonEntries = profileCache.values().stream()
            .map(profile -> new LeaderboardEntry(
                profile.getPlayerId(),
                profile.getPlayerName(),
                profile.getStatistics().getDungeonsCompleted()
            ))
            .sorted((a, b) -> Integer.compare(b.getValue(), a.getValue()))
            .toList();
        leaderboards.put("dungeons_completed", dungeonEntries);
        
        // Total score
        List<LeaderboardEntry> scoreEntries = profileCache.values().stream()
            .map(profile -> new LeaderboardEntry(
                profile.getPlayerId(),
                profile.getPlayerName(),
                (int) profile.getStatistics().getTotalScore()
            ))
            .sorted((a, b) -> Integer.compare(b.getValue(), a.getValue()))
            .toList();
        leaderboards.put("total_score", scoreEntries);
        
        plugin.getLogger().info("Leaderboards updated");
    }
    
    /**
     * Gets all available achievements.
     * 
     * @return map of achievement ID to achievement
     */
    @NotNull
    public Map<String, Achievement> getAchievements() {
        return Collections.unmodifiableMap(achievements);
    }
    
    /**
     * Gets the current season number.
     * 
     * @return the current season
     */
    public int getCurrentSeason() {
        return currentSeason;
    }
    
    /**
     * Starts a new season.
     * 
     * @param newSeasonId the new season ID
     */
    public void startNewSeason(int newSeasonId) {
        plugin.getLogger().info("Starting new season: " + newSeasonId);
        
        // Archive current season data for all players
        for (PlayerProfile profile : profileCache.values()) {
            int rank = getPlayerSeasonRank(profile.getPlayerId());
            profile.endSeason(currentSeason, rank);
        }
        
        // Update current season
        currentSeason = newSeasonId;
        
        // Clear leaderboards
        leaderboards.clear();
        
        // Save all profiles
        saveAllProfiles();
        
        // Broadcast season change
        Bukkit.broadcastMessage("§6§lNEW SEASON STARTED: §e" + newSeasonId);
        
        plugin.getLogger().info("New season started: " + newSeasonId);
    }
    
    /**
     * Gets a player's rank in the current season.
     * 
     * @param playerId the player UUID
     * @return the player's rank (1-based), or -1 if not found
     */
    public int getPlayerSeasonRank(@NotNull UUID playerId) {
        List<LeaderboardEntry> seasonBoard = getLeaderboard("season_score", Integer.MAX_VALUE);
        for (int i = 0; i < seasonBoard.size(); i++) {
            if (seasonBoard.get(i).getPlayerId().equals(playerId)) {
                return i + 1;
            }
        }
        return -1;
    }
    
    // Event Handlers
    
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();
        
        // Load or create player profile
        schedulerUtil.runTaskAsynchronously(() -> {
            PlayerProfile profile = getOrCreatePlayerProfile(playerId, player.getName());
            profile.updateLastSeen();
            
            plugin.getLogger().info("Loaded profile for player: " + player.getName());
        });
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();
        
        PlayerProfile profile = profileCache.get(playerId);
        if (profile != null) {
            profile.updateLastSeen();
            savePlayerProfile(profile);
        }
    }
    
    /**
     * Shuts down the progression service.
     */
    public void shutdown() {
        plugin.getLogger().info("Shutting down ProgressionService...");
        
        // Cancel background tasks
        if (saveTask != null) {
            saveTask.cancel();
        }
        if (leaderboardUpdateTask != null) {
            leaderboardUpdateTask.cancel();
        }
        
        // Save all profiles
        saveAllProfiles();
        
        // Clear cache
        profileCache.clear();
        
        plugin.getLogger().info("ProgressionService shutdown complete");
    }
    
    /**
     * Represents a leaderboard entry.
     */
    public static class LeaderboardEntry {
        private final UUID playerId;
        private final String playerName;
        private final int value;
        
        public LeaderboardEntry(@NotNull UUID playerId, @NotNull String playerName, int value) {
            this.playerId = playerId;
            this.playerName = playerName;
            this.value = value;
        }
        
        @NotNull
        public UUID getPlayerId() {
            return playerId;
        }
        
        @NotNull
        public String getPlayerName() {
            return playerName;
        }
        
        public int getValue() {
            return value;
        }
    }
}

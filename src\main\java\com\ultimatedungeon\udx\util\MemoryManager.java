package com.ultimatedungeon.udx.util;

import org.bukkit.entity.Entity;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.lang.ref.WeakReference;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Supplier;
import java.util.logging.Logger;

/**
 * Manages memory usage and provides object pooling for performance optimization.
 * 
 * <p>This class implements object pools for frequently created/destroyed objects,
 * tracks entity counts, and provides cleanup utilities to prevent memory leaks.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class MemoryManager {
    
    private final Plugin plugin;
    private final Logger logger;
    
    // Object pools
    private final ConcurrentMap<Class<?>, ObjectPool<?>> objectPools;
    
    // Entity tracking
    private final ConcurrentMap<String, AtomicInteger> entityCounts;
    private final ConcurrentMap<String, Integer> entityLimits;
    
    // Cleanup tracking
    private final ConcurrentLinkedQueue<WeakReference<Object>> cleanupQueue;
    private BukkitTask cleanupTask;
    
    // Statistics
    private final AtomicLong totalObjectsPooled;
    private final AtomicLong totalObjectsReused;
    private final AtomicLong totalEntitiesTracked;
    
    // Configuration
    private static final int DEFAULT_POOL_SIZE = 100;
    private static final int MAX_POOL_SIZE = 1000;
    private static final long CLEANUP_INTERVAL_TICKS = 1200; // 1 minute
    
    public MemoryManager(@NotNull Plugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        
        this.objectPools = new ConcurrentHashMap<>();
        this.entityCounts = new ConcurrentHashMap<>();
        this.entityLimits = new ConcurrentHashMap<>();
        this.cleanupQueue = new ConcurrentLinkedQueue<>();
        
        this.totalObjectsPooled = new AtomicLong(0);
        this.totalObjectsReused = new AtomicLong(0);
        this.totalEntitiesTracked = new AtomicLong(0);
        
        startCleanupTask();
        setupDefaultEntityLimits();
    }
    
    /**
     * Creates or gets an object pool for the specified class.
     * 
     * @param clazz The class to pool
     * @param factory Factory function to create new instances
     * @param <T> The type to pool
     * @return The object pool
     */
    @NotNull
    public <T> ObjectPool<T> getOrCreatePool(@NotNull Class<T> clazz, @NotNull Supplier<T> factory) {
        return getOrCreatePool(clazz, factory, DEFAULT_POOL_SIZE);
    }
    
    /**
     * Creates or gets an object pool for the specified class with custom size.
     * 
     * @param clazz The class to pool
     * @param factory Factory function to create new instances
     * @param maxSize Maximum pool size
     * @param <T> The type to pool
     * @return The object pool
     */
    @NotNull
    @SuppressWarnings("unchecked")
    public <T> ObjectPool<T> getOrCreatePool(@NotNull Class<T> clazz, @NotNull Supplier<T> factory, int maxSize) {
        return (ObjectPool<T>) objectPools.computeIfAbsent(clazz, 
            k -> new ObjectPool<>(clazz, factory, Math.min(maxSize, MAX_POOL_SIZE)));
    }
    
    /**
     * Borrows an object from the pool.
     * 
     * @param clazz The class to borrow
     * @param <T> The type to borrow
     * @return An object instance, or null if no pool exists
     */
    @Nullable
    @SuppressWarnings("unchecked")
    public <T> T borrowObject(@NotNull Class<T> clazz) {
        ObjectPool<T> pool = (ObjectPool<T>) objectPools.get(clazz);
        if (pool != null) {
            T object = pool.borrow();
            if (object != null) {
                totalObjectsReused.incrementAndGet();
            }
            return object;
        }
        return null;
    }
    
    /**
     * Returns an object to the pool.
     * 
     * @param object The object to return
     * @param <T> The type of the object
     */
    public <T> void returnObject(@NotNull T object) {
        @SuppressWarnings("unchecked")
        Class<T> clazz = (Class<T>) object.getClass();
        
        @SuppressWarnings("unchecked")
        ObjectPool<T> pool = (ObjectPool<T>) objectPools.get(clazz);
        
        if (pool != null) {
            pool.returnObject(object);
        }
    }
    
    /**
     * Tracks an entity for counting and limits.
     * 
     * @param entity The entity to track
     * @param category Category for grouping (e.g., "dungeon_mob", "boss")
     */
    public void trackEntity(@NotNull Entity entity, @NotNull String category) {
        AtomicInteger count = entityCounts.computeIfAbsent(category, k -> new AtomicInteger(0));
        int newCount = count.incrementAndGet();
        totalEntitiesTracked.incrementAndGet();
        
        // Check limits
        Integer limit = entityLimits.get(category);
        if (limit != null && newCount > limit) {
            logger.warning(String.format("Entity limit exceeded for category '%s': %d/%d", 
                category, newCount, limit));
        }
        
        // Add to cleanup queue for automatic tracking
        cleanupQueue.offer(new WeakReference<>(entity));
    }
    
    /**
     * Untracks an entity.
     * 
     * @param category The category the entity was tracked under
     */
    public void untrackEntity(@NotNull String category) {
        AtomicInteger count = entityCounts.get(category);
        if (count != null) {
            count.decrementAndGet();
        }
    }
    
    /**
     * Sets the entity limit for a category.
     * 
     * @param category The entity category
     * @param limit The maximum number of entities allowed
     */
    public void setEntityLimit(@NotNull String category, int limit) {
        entityLimits.put(category, limit);
        logger.info(String.format("Set entity limit for '%s': %d", category, limit));
    }
    
    /**
     * Gets the current entity count for a category.
     * 
     * @param category The entity category
     * @return The current count
     */
    public int getEntityCount(@NotNull String category) {
        AtomicInteger count = entityCounts.get(category);
        return count != null ? count.get() : 0;
    }
    
    /**
     * Gets the entity limit for a category.
     * 
     * @param category The entity category
     * @return The limit, or -1 if no limit is set
     */
    public int getEntityLimit(@NotNull String category) {
        return entityLimits.getOrDefault(category, -1);
    }
    
    /**
     * Checks if an entity category is at or over its limit.
     * 
     * @param category The entity category
     * @return True if at or over limit
     */
    public boolean isEntityLimitReached(@NotNull String category) {
        Integer limit = entityLimits.get(category);
        if (limit == null) return false;
        
        return getEntityCount(category) >= limit;
    }
    
    /**
     * Adds an object to the cleanup queue for automatic memory management.
     * 
     * @param object The object to track for cleanup
     */
    public void addToCleanupQueue(@NotNull Object object) {
        cleanupQueue.offer(new WeakReference<>(object));
    }
    
    /**
     * Forces cleanup of all object pools and tracked entities.
     */
    public void forceCleanup() {
        logger.info("Forcing memory cleanup...");
        
        // Clean object pools
        int poolsCleaned = 0;
        int objectsFreed = 0;
        
        for (ObjectPool<?> pool : objectPools.values()) {
            int freed = pool.cleanup();
            if (freed > 0) {
                poolsCleaned++;
                objectsFreed += freed;
            }
        }
        
        // Clean weak references
        int referencesCleared = 0;
        WeakReference<Object> ref;
        while ((ref = cleanupQueue.poll()) != null) {
            if (ref.get() == null) {
                referencesCleared++;
            } else {
                // Put back if still alive
                cleanupQueue.offer(ref);
                break;
            }
        }
        
        logger.info(String.format("Cleanup completed: %d pools cleaned, %d objects freed, %d references cleared",
            poolsCleaned, objectsFreed, referencesCleared));
    }
    
    /**
     * Gets memory usage statistics.
     */
    @NotNull
    public MemoryStats getMemoryStats() {
        int totalPooledObjects = 0;
        int totalActiveObjects = 0;
        
        for (ObjectPool<?> pool : objectPools.values()) {
            totalPooledObjects += pool.getPoolSize();
            totalActiveObjects += pool.getActiveCount();
        }
        
        int totalEntityCount = entityCounts.values().stream()
            .mapToInt(AtomicInteger::get)
            .sum();
        
        return new MemoryStats(
            objectPools.size(),
            totalPooledObjects,
            totalActiveObjects,
            totalObjectsPooled.get(),
            totalObjectsReused.get(),
            totalEntityCount,
            totalEntitiesTracked.get(),
            cleanupQueue.size()
        );
    }
    
    /**
     * Starts the automatic cleanup task.
     */
    private void startCleanupTask() {
        cleanupTask = new BukkitRunnable() {
            @Override
            public void run() {
                performPeriodicCleanup();
            }
        }.runTaskTimer(plugin, CLEANUP_INTERVAL_TICKS, CLEANUP_INTERVAL_TICKS);
    }
    
    /**
     * Performs periodic cleanup of pools and references.
     */
    private void performPeriodicCleanup() {
        // Clean weak references
        int cleaned = 0;
        WeakReference<Object> ref;
        int checked = 0;
        
        while ((ref = cleanupQueue.poll()) != null && checked < 1000) { // Limit to prevent lag
            checked++;
            if (ref.get() == null) {
                cleaned++;
            } else {
                cleanupQueue.offer(ref); // Put back if still alive
            }
        }
        
        if (cleaned > 0) {
            logger.fine(String.format("Periodic cleanup: cleared %d weak references", cleaned));
        }
    }
    
    /**
     * Sets up default entity limits.
     */
    private void setupDefaultEntityLimits() {
        setEntityLimit("dungeon_mob", 200);
        setEntityLimit("boss", 10);
        setEntityLimit("projectile", 500);
        setEntityLimit("particle_effect", 1000);
        setEntityLimit("temporary_entity", 100);
    }
    
    /**
     * Shuts down the memory manager.
     */
    public void shutdown() {
        logger.info("Shutting down MemoryManager...");
        
        // Cancel cleanup task
        if (cleanupTask != null) {
            cleanupTask.cancel();
        }
        
        // Log final statistics
        MemoryStats stats = getMemoryStats();
        logger.info("Final memory statistics:");
        logger.info(String.format("  Object Pools: %d", stats.poolCount()));
        logger.info(String.format("  Pooled Objects: %d", stats.totalPooledObjects()));
        logger.info(String.format("  Active Objects: %d", stats.totalActiveObjects()));
        logger.info(String.format("  Objects Reused: %d", stats.totalObjectsReused()));
        logger.info(String.format("  Entities Tracked: %d", stats.totalEntitiesTracked()));
        
        // Force final cleanup
        forceCleanup();
        
        // Clear all data
        objectPools.clear();
        entityCounts.clear();
        entityLimits.clear();
        cleanupQueue.clear();
        
        logger.info("MemoryManager shutdown complete");
    }
    
    /**
     * Object pool implementation for reusing objects.
     */
    public static class ObjectPool<T> {
        private final Class<T> clazz;
        private final Supplier<T> factory;
        private final ConcurrentLinkedQueue<T> pool;
        private final AtomicInteger poolSize;
        private final AtomicInteger activeCount;
        private final int maxSize;
        
        public ObjectPool(@NotNull Class<T> clazz, @NotNull Supplier<T> factory, int maxSize) {
            this.clazz = clazz;
            this.factory = factory;
            this.pool = new ConcurrentLinkedQueue<>();
            this.poolSize = new AtomicInteger(0);
            this.activeCount = new AtomicInteger(0);
            this.maxSize = maxSize;
        }
        
        @Nullable
        public T borrow() {
            T object = pool.poll();
            if (object != null) {
                poolSize.decrementAndGet();
                activeCount.incrementAndGet();
                return object;
            }
            
            // Create new object if pool is empty
            try {
                object = factory.get();
                activeCount.incrementAndGet();
                return object;
            } catch (Exception e) {
                return null;
            }
        }
        
        public void returnObject(@NotNull T object) {
            if (poolSize.get() < maxSize) {
                pool.offer(object);
                poolSize.incrementAndGet();
            }
            activeCount.decrementAndGet();
        }
        
        public int getPoolSize() {
            return poolSize.get();
        }
        
        public int getActiveCount() {
            return activeCount.get();
        }
        
        public int cleanup() {
            int freed = 0;
            // Clear half the pool to free memory
            int toRemove = poolSize.get() / 2;
            
            for (int i = 0; i < toRemove; i++) {
                if (pool.poll() != null) {
                    poolSize.decrementAndGet();
                    freed++;
                }
            }
            
            return freed;
        }
    }
    
    /**
     * Memory statistics record.
     */
    public record MemoryStats(
        int poolCount,
        int totalPooledObjects,
        int totalActiveObjects,
        long totalObjectsPooled,
        long totalObjectsReused,
        int currentEntityCount,
        long totalEntitiesTracked,
        int cleanupQueueSize
    ) {}
}

package com.ultimatedungeon.udx.affix;

import com.ultimatedungeon.udx.config.ConfigService;
import com.ultimatedungeon.udx.util.SchedulerUtil;
import org.bukkit.Bukkit;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntitySpawnEvent;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitTask;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;

/**
 * Manages affix system and difficulty scaling.
 * 
 * <p>This service handles affix definitions, rotations, difficulty scaling,
 * and runtime application of affix effects to dungeons and entities.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class AffixService implements Listener {
    
    private final Plugin plugin;
    private final ConfigService configService;
    private final SchedulerUtil schedulerUtil;
    
    // Affix definitions
    private final Map<String, Affix> affixes;
    
    // Active rotations
    private final Map<String, AffixRotation> rotations;
    private AffixRotation currentRotation;
    
    // Runtime tracking
    private final Map<UUID, Set<String>> instanceAffixes;
    private final Map<UUID, DifficultyTier> instanceDifficulty;
    private final Map<UUID, Integer> instancePartySize;
    
    // Background tasks
    private BukkitTask rotationUpdateTask;
    
    public AffixService(@NotNull Plugin plugin, @NotNull ConfigService configService,
                       @NotNull SchedulerUtil schedulerUtil) {
        this.plugin = plugin;
        this.configService = configService;
        this.schedulerUtil = schedulerUtil;
        this.affixes = new ConcurrentHashMap<>();
        this.rotations = new ConcurrentHashMap<>();
        this.instanceAffixes = new ConcurrentHashMap<>();
        this.instanceDifficulty = new ConcurrentHashMap<>();
        this.instancePartySize = new ConcurrentHashMap<>();
        
        initialize();
    }
    
    /**
     * Initializes the affix service.
     */
    private void initialize() {
        plugin.getLogger().info("Initializing AffixService...");
        
        // Load affix definitions
        loadAffixes();
        
        // Load rotations
        loadRotations();
        
        // Update current rotation
        updateCurrentRotation();
        
        // Start background tasks
        startBackgroundTasks();
        
        // Register event listeners
        Bukkit.getPluginManager().registerEvents(this, plugin);
        
        plugin.getLogger().info("AffixService initialized with " + affixes.size() + " affixes");
    }
    
    /**
     * Loads affix definitions.
     */
    private void loadAffixes() {
        // Define core affixes
        
        // Mob affixes
        addAffix(new Affix(
            "berserk_mobs", "Berserk Mobs", "Mobs deal more damage but have less health",
            AffixType.MOB, AffixRarity.COMMON, "IRON_SWORD",
            Arrays.asList("Increased mob damage", "Reduced mob health"),
            Map.of("damage_multiplier", 1.5, "health_multiplier", 0.7),
            1.2, 1.1, 2
        ));
        
        addAffix(new Affix(
            "armored_enemies", "Armored Enemies", "Mobs have increased armor and resistance",
            AffixType.MOB, AffixRarity.UNCOMMON, "IRON_CHESTPLATE",
            Arrays.asList("Increased mob armor", "Damage resistance"),
            Map.of("armor_multiplier", 2.0, "resistance", 0.2),
            1.3, 1.2, 3
        ));
        
        addAffix(new Affix(
            "swift_strikes", "Swift Strikes", "Mobs attack faster and move quicker",
            AffixType.MOB, AffixRarity.RARE, "GOLDEN_BOOTS",
            Arrays.asList("Increased attack speed", "Increased movement speed"),
            Map.of("attack_speed_multiplier", 1.4, "movement_speed_multiplier", 1.3),
            1.5, 1.3, 5
        ));
        
        // Environment affixes
        addAffix(new Affix(
            "frosted_floors", "Frosted Floors", "Floors are slippery and movement is impaired",
            AffixType.ENVIRONMENT, AffixRarity.COMMON, "ICE",
            Arrays.asList("Slippery surfaces", "Reduced traction"),
            Map.of("slip_chance", 0.3, "movement_reduction", 0.2),
            1.1, 1.0, 1
        ));
        
        addAffix(new Affix(
            "thick_air", "Thick Air", "Projectiles move slower and abilities have longer cooldowns",
            AffixType.ENVIRONMENT, AffixRarity.UNCOMMON, "COBWEB",
            Arrays.asList("Slower projectiles", "Increased cooldowns"),
            Map.of("projectile_speed_multiplier", 0.7, "cooldown_multiplier", 1.3),
            1.2, 1.1, 2
        ));
        
        addAffix(new Affix(
            "unstable_magic", "Unstable Magic", "Magic effects are unpredictable and chaotic",
            AffixType.ENVIRONMENT, AffixRarity.EPIC, "NETHER_STAR",
            Arrays.asList("Random spell effects", "Magical instability"),
            Map.of("chaos_chance", 0.15, "effect_variance", 0.5),
            2.0, 1.8, 8
        ));
        
        // Player affixes
        addAffix(new Affix(
            "mana_burn", "Mana Burn", "Taking damage drains mana and slows regeneration",
            AffixType.PLAYER, AffixRarity.RARE, "LAPIS_LAZULI",
            Arrays.asList("Mana drain on damage", "Slower regeneration"),
            Map.of("mana_drain_per_damage", 0.1, "regen_multiplier", 0.5),
            1.4, 1.2, 4
        ));
        
        addAffix(new Affix(
            "fragile_bones", "Fragile Bones", "Players take more fall damage and knockback",
            AffixType.PLAYER, AffixRarity.UNCOMMON, "BONE",
            Arrays.asList("Increased fall damage", "Increased knockback"),
            Map.of("fall_damage_multiplier", 1.5, "knockback_multiplier", 1.3),
            1.2, 1.1, 2
        ));
        
        // Loot affixes
        addAffix(new Affix(
            "treasure_hunter", "Treasure Hunter", "Increased loot quality but fewer drops",
            AffixType.LOOT, AffixRarity.RARE, "CHEST",
            Arrays.asList("Higher quality loot", "Reduced drop rates"),
            Map.of("quality_bonus", 2, "quantity_multiplier", 0.7),
            1.8, 1.0, 0
        ));
        
        addAffix(new Affix(
            "cursed_gold", "Cursed Gold", "More currency drops but items are more expensive",
            AffixType.LOOT, AffixRarity.EPIC, "GOLD_INGOT",
            Arrays.asList("Increased currency", "Higher shop prices"),
            Map.of("currency_multiplier", 2.0, "price_multiplier", 1.5),
            1.5, 1.0, 0
        ));
        
        // Mechanic affixes
        addAffix(new Affix(
            "volatile_deaths", "Volatile Deaths", "Mobs explode when killed",
            AffixType.MECHANIC, AffixRarity.EPIC, "TNT",
            Arrays.asList("Explosive deaths", "Area damage"),
            Map.of("explosion_power", 2.0, "damage_radius", 3.0),
            1.8, 1.5, 6
        ));
        
        addAffix(new Affix(
            "regenerating_foes", "Regenerating Foes", "Mobs slowly regenerate health over time",
            AffixType.MECHANIC, AffixRarity.LEGENDARY, "GOLDEN_APPLE",
            Arrays.asList("Health regeneration", "Extended fights"),
            Map.of("regen_rate", 0.02, "regen_interval", 20),
            2.5, 2.0, 10
        ));
        
        plugin.getLogger().info("Loaded " + affixes.size() + " affix definitions");
    }
    
    /**
     * Adds an affix to the registry.
     * 
     * @param affix the affix to add
     */
    private void addAffix(@NotNull Affix affix) {
        affixes.put(affix.getId(), affix);
    }
    
    /**
     * Loads rotation configurations.
     */
    private void loadRotations() {
        // Create default weekly rotation
        LocalDate now = LocalDate.now();
        LocalDate weekStart = now.minusDays(now.getDayOfWeek().getValue() - 1);
        LocalDate weekEnd = weekStart.plusDays(6);
        
        List<String> weeklyAffixes = generateRandomAffixes(3);
        
        AffixRotation weeklyRotation = new AffixRotation(
            "weekly_" + weekStart.toString(),
            AffixRotation.RotationType.WEEKLY,
            weekStart,
            weekEnd,
            weeklyAffixes,
            new HashMap<>(),
            true
        );
        
        rotations.put(weeklyRotation.getId(), weeklyRotation);
        
        plugin.getLogger().info("Loaded " + rotations.size() + " affix rotations");
    }
    
    /**
     * Generates random affixes for rotation.
     * 
     * @param count the number of affixes to generate
     * @return the list of affix IDs
     */
    @NotNull
    private List<String> generateRandomAffixes(int count) {
        List<String> available = new ArrayList<>(affixes.keySet());
        Collections.shuffle(available);
        
        List<String> selected = new ArrayList<>();
        Set<AffixType> usedTypes = new HashSet<>();
        
        for (String affixId : available) {
            if (selected.size() >= count) break;
            
            Affix affix = affixes.get(affixId);
            if (affix != null && !usedTypes.contains(affix.getType())) {
                selected.add(affixId);
                usedTypes.add(affix.getType());
            }
        }
        
        return selected;
    }
    
    /**
     * Updates the current rotation based on date.
     */
    private void updateCurrentRotation() {
        LocalDate now = LocalDate.now();
        
        for (AffixRotation rotation : rotations.values()) {
            if (rotation.isActiveOn(now)) {
                currentRotation = rotation;
                plugin.getLogger().info("Updated current rotation: " + rotation.getId());
                return;
            }
        }
        
        // No active rotation found, create a new one
        createNewRotation();
    }
    
    /**
     * Creates a new rotation for the current period.
     */
    private void createNewRotation() {
        LocalDate now = LocalDate.now();
        LocalDate weekStart = now.minusDays(now.getDayOfWeek().getValue() - 1);
        LocalDate weekEnd = weekStart.plusDays(6);
        
        List<String> newAffixes = generateRandomAffixes(3);
        
        AffixRotation newRotation = new AffixRotation(
            "weekly_" + weekStart.toString(),
            AffixRotation.RotationType.WEEKLY,
            weekStart,
            weekEnd,
            newAffixes,
            new HashMap<>(),
            true
        );
        
        rotations.put(newRotation.getId(), newRotation);
        currentRotation = newRotation;
        
        plugin.getLogger().info("Created new rotation: " + newRotation.getId());
        
        // Broadcast rotation change
        Bukkit.broadcastMessage("§6§lNEW AFFIX ROTATION: §e" + String.join("§7, §e", newAffixes));
    }
    
    /**
     * Starts background tasks.
     */
    private void startBackgroundTasks() {
        // Check for rotation updates every hour
        rotationUpdateTask = schedulerUtil.runTaskTimerAsynchronously(() -> {
            updateCurrentRotation();
        }, 20L * 60L * 60L, 20L * 60L * 60L); // Every hour
    }
    
    /**
     * Gets an affix by ID.
     * 
     * @param affixId the affix ID
     * @return the affix, or null if not found
     */
    @Nullable
    public Affix getAffix(@NotNull String affixId) {
        return affixes.get(affixId);
    }
    
    /**
     * Gets all available affixes.
     * 
     * @return the affixes map
     */
    @NotNull
    public Map<String, Affix> getAffixes() {
        return Collections.unmodifiableMap(affixes);
    }
    
    /**
     * Gets the current rotation.
     * 
     * @return the current rotation, or null if none active
     */
    @Nullable
    public AffixRotation getCurrentRotation() {
        return currentRotation;
    }
    
    /**
     * Gets active affixes for a dungeon.
     * 
     * @param dungeonId the dungeon ID
     * @return the list of active affixes
     */
    @NotNull
    public List<Affix> getActiveAffixes(@NotNull String dungeonId) {
        if (currentRotation == null) {
            return Collections.emptyList();
        }
        
        List<String> affixIds = currentRotation.getAffixesForDungeon(dungeonId);
        List<Affix> activeAffixes = new ArrayList<>();
        
        for (String affixId : affixIds) {
            Affix affix = affixes.get(affixId);
            if (affix != null) {
                activeAffixes.add(affix);
            }
        }
        
        return activeAffixes;
    }
    
    /**
     * Sets affixes for a dungeon instance.
     * 
     * @param instanceId the instance ID
     * @param affixIds the affix IDs
     */
    public void setInstanceAffixes(@NotNull UUID instanceId, @NotNull Set<String> affixIds) {
        instanceAffixes.put(instanceId, new HashSet<>(affixIds));
    }
    
    /**
     * Sets difficulty for a dungeon instance.
     * 
     * @param instanceId the instance ID
     * @param difficulty the difficulty tier
     * @param partySize the party size
     */
    public void setInstanceDifficulty(@NotNull UUID instanceId, @NotNull DifficultyTier difficulty, int partySize) {
        instanceDifficulty.put(instanceId, difficulty);
        instancePartySize.put(instanceId, partySize);
    }
    
    /**
     * Gets the difficulty for an instance.
     * 
     * @param instanceId the instance ID
     * @return the difficulty tier, or NORMAL if not set
     */
    @NotNull
    public DifficultyTier getInstanceDifficulty(@NotNull UUID instanceId) {
        return instanceDifficulty.getOrDefault(instanceId, DifficultyTier.NORMAL);
    }
    
    /**
     * Applies difficulty scaling to an entity.
     * 
     * @param entity the entity
     * @param instanceId the instance ID
     */
    public void applyDifficultyScaling(@NotNull LivingEntity entity, @NotNull UUID instanceId) {
        DifficultyTier difficulty = getInstanceDifficulty(instanceId);
        int partySize = instancePartySize.getOrDefault(instanceId, 1);
        
        // Scale health
        double maxHealth = entity.getMaxHealth();
        double scaledHealth = difficulty.scaleForPartySize(
            maxHealth * difficulty.getHealthMultiplier(), 
            partySize, 
            4
        );
        entity.setMaxHealth(scaledHealth);
        entity.setHealth(scaledHealth);
        
        // Apply affix effects
        Set<String> affixIds = instanceAffixes.get(instanceId);
        if (affixIds != null) {
            for (String affixId : affixIds) {
                Affix affix = affixes.get(affixId);
                if (affix != null) {
                    applyAffixToEntity(affix, entity);
                }
            }
        }
    }
    
    /**
     * Applies an affix effect to an entity.
     * 
     * @param affix the affix
     * @param entity the entity
     */
    private void applyAffixToEntity(@NotNull Affix affix, @NotNull LivingEntity entity) {
        // Apply health modifications
        if (affix.hasEffect("Reduced mob health")) {
            double healthMult = affix.getParameterAsDouble("health_multiplier", 1.0);
            double newHealth = entity.getMaxHealth() * healthMult;
            entity.setMaxHealth(newHealth);
            entity.setHealth(newHealth);
        }
        
        // Apply speed modifications
        if (affix.hasEffect("Increased movement speed")) {
            double speedMult = affix.getParameterAsDouble("movement_speed_multiplier", 1.0);
            // Apply speed effect instead of attribute modification
            int amplifier = Math.max(0, (int) ((speedMult - 1.0) * 10));
            entity.addPotionEffect(new org.bukkit.potion.PotionEffect(
                org.bukkit.potion.PotionEffectType.SPEED, 
                Integer.MAX_VALUE, 
                amplifier, 
                false, 
                false
            ));
        }
        
        // Apply armor modifications
        if (affix.hasEffect("Increased mob armor")) {
            double armorMult = affix.getParameterAsDouble("armor_multiplier", 1.0);
            // Apply resistance effect instead of attribute modification
            int amplifier = Math.max(0, (int) ((armorMult - 1.0) * 5));
            entity.addPotionEffect(new org.bukkit.potion.PotionEffect(
                org.bukkit.potion.PotionEffectType.RESISTANCE, 
                Integer.MAX_VALUE, 
                amplifier, 
                false, 
                false
            ));
        }
    }
    
    /**
     * Calculates the total reward multiplier for an instance.
     * 
     * @param instanceId the instance ID
     * @return the total reward multiplier
     */
    public double calculateRewardMultiplier(@NotNull UUID instanceId) {
        DifficultyTier difficulty = getInstanceDifficulty(instanceId);
        double multiplier = difficulty.getRewardMultiplier();
        
        Set<String> affixIds = instanceAffixes.get(instanceId);
        if (affixIds != null) {
            for (String affixId : affixIds) {
                Affix affix = affixes.get(affixId);
                if (affix != null) {
                    multiplier *= affix.getLootMultiplier();
                }
            }
        }
        
        return multiplier;
    }
    
    /**
     * Removes instance data when instance is disposed.
     * 
     * @param instanceId the instance ID
     */
    public void removeInstanceData(@NotNull UUID instanceId) {
        instanceAffixes.remove(instanceId);
        instanceDifficulty.remove(instanceId);
        instancePartySize.remove(instanceId);
    }
    
    // Event Handlers
    
    @EventHandler
    public void onEntitySpawn(EntitySpawnEvent event) {
        if (!(event.getEntity() instanceof LivingEntity)) return;
        
        LivingEntity entity = (LivingEntity) event.getEntity();
        
        // Check if entity is in a dungeon instance
        // This would need integration with InstanceManager to get instance ID
        // For now, we'll skip this implementation detail
    }
    
    @EventHandler
    public void onEntityDamage(EntityDamageByEntityEvent event) {
        if (!(event.getDamager() instanceof LivingEntity)) return;
        if (!(event.getEntity() instanceof Player)) return;
        
        LivingEntity damager = (LivingEntity) event.getDamager();
        Player player = (Player) event.getEntity();
        
        // Apply affix damage modifications
        // This would need instance context to apply proper scaling
    }
    
    /**
     * Shuts down the affix service.
     */
    public void shutdown() {
        plugin.getLogger().info("Shutting down AffixService...");
        
        // Cancel background tasks
        if (rotationUpdateTask != null) {
            rotationUpdateTask.cancel();
        }
        
        // Clear data
        instanceAffixes.clear();
        instanceDifficulty.clear();
        instancePartySize.clear();
        
        plugin.getLogger().info("AffixService shutdown complete");
    }
}

package com.ultimatedungeon.udx.api.party;

import org.jetbrains.annotations.NotNull;

import java.time.Instant;
import java.util.UUID;

/**
 * Immutable representation of queue data for API consumers.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public record QueueData(
    @NotNull UUID partyId,
    @NotNull String dungeonId,
    @NotNull String difficulty,
    @NotNull Instant queuedAt,
    int estimatedWaitTimeMs,
    int position,
    boolean readyCheckActive,
    @NotNull Instant readyCheckStartedAt
) {
    
    /**
     * Creates new queue data.
     */
    public QueueData {
        if (partyId == null) {
            throw new IllegalArgumentException("Party ID cannot be null");
        }
        if (dungeonId == null || dungeonId.isBlank()) {
            throw new IllegalArgumentException("Dungeon ID cannot be null or blank");
        }
        if (difficulty == null || difficulty.isBlank()) {
            throw new IllegalArgumentException("Difficulty cannot be null or blank");
        }
        if (queuedAt == null) {
            throw new IllegalArgumentException("Queued at cannot be null");
        }
        if (estimatedWaitTimeMs < 0) {
            throw new IllegalArgumentException("Estimated wait time cannot be negative");
        }
        if (position < 0) {
            throw new IllegalArgumentException("Position cannot be negative");
        }
        if (readyCheckStartedAt == null) {
            throw new IllegalArgumentException("Ready check started at cannot be null");
        }
    }
    
    /**
     * Gets the time spent in queue in milliseconds.
     * 
     * @return Time in queue in milliseconds
     */
    public long getTimeInQueueMs() {
        return Instant.now().toEpochMilli() - queuedAt.toEpochMilli();
    }
    
    /**
     * Gets the remaining estimated wait time in milliseconds.
     * 
     * @return Remaining wait time in milliseconds
     */
    public long getRemainingWaitTimeMs() {
        long timeInQueue = getTimeInQueueMs();
        return Math.max(0, estimatedWaitTimeMs - timeInQueue);
    }
    
    /**
     * Gets the progress through the estimated wait time as a percentage (0-100).
     * 
     * @return Progress percentage
     */
    public double getWaitProgress() {
        if (estimatedWaitTimeMs <= 0) return 100.0;
        long timeInQueue = getTimeInQueueMs();
        return Math.min(100.0, (double) timeInQueue / estimatedWaitTimeMs * 100.0);
    }
    
    /**
     * Checks if the ready check is currently active.
     * 
     * @return True if ready check is active
     */
    public boolean isReadyCheckActive() {
        return readyCheckActive;
    }
    
    /**
     * Gets the time since ready check started in milliseconds.
     * 
     * @return Time since ready check started in milliseconds
     */
    public long getReadyCheckElapsedMs() {
        if (!readyCheckActive) return 0L;
        return Instant.now().toEpochMilli() - readyCheckStartedAt.toEpochMilli();
    }
    
    /**
     * Checks if the queue entry has exceeded the estimated wait time.
     * 
     * @return True if wait time has been exceeded
     */
    public boolean hasExceededEstimate() {
        return getTimeInQueueMs() > estimatedWaitTimeMs;
    }
    
    /**
     * Gets a formatted string representation of the queue position.
     * 
     * @return Formatted position string
     */
    @NotNull
    public String getFormattedPosition() {
        if (position == 0) {
            return "Next in line";
        } else if (position == 1) {
            return "2nd in queue";
        } else if (position == 2) {
            return "3rd in queue";
        } else {
            return (position + 1) + "th in queue";
        }
    }
    
    /**
     * Gets a formatted string representation of the remaining wait time.
     * 
     * @return Formatted wait time string
     */
    @NotNull
    public String getFormattedWaitTime() {
        long remainingMs = getRemainingWaitTimeMs();
        if (remainingMs <= 0) {
            return "Any moment now";
        }
        
        long seconds = remainingMs / 1000;
        if (seconds < 60) {
            return seconds + "s";
        }
        
        long minutes = seconds / 60;
        seconds = seconds % 60;
        if (minutes < 60) {
            return minutes + "m " + seconds + "s";
        }
        
        long hours = minutes / 60;
        minutes = minutes % 60;
        return hours + "h " + minutes + "m";
    }
}

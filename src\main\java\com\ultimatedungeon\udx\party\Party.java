package com.ultimatedungeon.udx.party;

import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Represents a party of players for dungeon runs.
 * 
 * <p>This is a basic implementation that will be expanded in Phase 10.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class Party {
    
    private final UUID partyId;
    private final Set<UUID> members;
    private UUID leader;
    private final Map<String, Object> metadata;
    
    public Party(@NotNull Player leader) {
        this.partyId = UUID.randomUUID();
        this.members = ConcurrentHashMap.newKeySet();
        this.leader = leader.getUniqueId();
        this.metadata = new ConcurrentHashMap<>();
        
        // Add leader as first member
        this.members.add(leader.getUniqueId());
    }
    
    @NotNull
    public UUID getPartyId() {
        return partyId;
    }
    
    @NotNull
    public Set<UUID> getMembers() {
        return new HashSet<>(members);
    }
    
    @NotNull
    public UUID getLeader() {
        return leader;
    }
    
    public void setLeader(@NotNull UUID leader) {
        if (members.contains(leader)) {
            this.leader = leader;
        }
    }
    
    public boolean addMember(@NotNull UUID playerId) {
        return members.add(playerId);
    }
    
    public boolean removeMember(@NotNull UUID playerId) {
        boolean removed = members.remove(playerId);
        
        // If leader was removed, promote someone else
        if (playerId.equals(leader) && !members.isEmpty()) {
            leader = members.iterator().next();
        }
        
        return removed;
    }
    
    public int getSize() {
        return members.size();
    }
    
    public boolean isEmpty() {
        return members.isEmpty();
    }
    
    @Nullable
    public Object getMetadata(@NotNull String key) {
        return metadata.get(key);
    }
    
    public void setMetadata(@NotNull String key, @Nullable Object value) {
        if (value == null) {
            metadata.remove(key);
        } else {
            metadata.put(key, value);
        }
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (!(obj instanceof Party other)) return false;
        return partyId.equals(other.partyId);
    }
    
    @Override
    public int hashCode() {
        return partyId.hashCode();
    }
    
    @Override
    public String toString() {
        return "Party{" +
            "id=" + partyId +
            ", members=" + members.size() +
            ", leader=" + leader +
            '}';
    }
}

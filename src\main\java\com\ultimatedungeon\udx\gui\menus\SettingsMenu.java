package com.ultimatedungeon.udx.gui.menus;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.gui.Menu;
import com.ultimatedungeon.udx.util.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

/**
 * Settings menu for player preferences and configuration.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class SettingsMenu extends Menu {
    
    private final UltimateDungeonX plugin;
    
    public SettingsMenu(@NotNull UltimateDungeonX plugin, @NotNull Player player) {
        super(player, Component.text("Settings").color(NamedTextColor.BLUE).decoration(TextDecoration.BOLD, true), 
              45, plugin.getMenuRegistry());
        this.plugin = plugin;
    }
    
    @Override
    protected void setupMenu() {
        inventory.clear();
        clickHandlers.clear();
        
        // HUD Settings
        setupHudSettings();
        
        // Sound Settings
        setupSoundSettings();
        
        // Notification Settings
        setupNotificationSettings();
        
        // Privacy Settings
        setupPrivacySettings();
        
        // Performance Settings
        setupPerformanceSettings();
        
        // Back button
        ItemStack backItem = new ItemBuilder(Material.ARROW)
            .name(Component.text("Back to Main Menu").color(NamedTextColor.GRAY))
            .lore(Component.text("Click to return").color(NamedTextColor.DARK_GRAY))
            .build();
        
        setItem(40, backItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                plugin.getMenuRegistry().openMainMenu(player);
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        fillEmptySlots();
    }
    
    private void setupHudSettings() {
        // Scoreboard HUD toggle
        ItemStack scoreboardItem = new ItemBuilder(Material.ITEM_FRAME)
            .name(Component.text("Scoreboard HUD").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Show dungeon progress on scoreboard").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Status: ").color(NamedTextColor.YELLOW)
                    .append(Component.text("Enabled").color(NamedTextColor.GREEN)),
                Component.empty(),
                Component.text("Click to toggle").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(10, scoreboardItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                // TODO: Toggle scoreboard HUD setting
                player.sendMessage(Component.text("Scoreboard HUD toggled!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
        
        // Bossbar HUD toggle
        ItemStack bossbarItem = new ItemBuilder(Material.DRAGON_HEAD)
            .name(Component.text("Boss Health Bar").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Show boss health and abilities").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Status: ").color(NamedTextColor.YELLOW)
                    .append(Component.text("Enabled").color(NamedTextColor.GREEN)),
                Component.empty(),
                Component.text("Click to toggle").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(11, bossbarItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                // TODO: Toggle bossbar setting
                player.sendMessage(Component.text("Boss health bar toggled!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
        
        // Action bar messages toggle
        ItemStack actionBarItem = new ItemBuilder(Material.PAPER)
            .name(Component.text("Action Bar Messages").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Show damage and healing numbers").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Status: ").color(NamedTextColor.YELLOW)
                    .append(Component.text("Enabled").color(NamedTextColor.GREEN)),
                Component.empty(),
                Component.text("Click to toggle").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(12, actionBarItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                // TODO: Toggle action bar messages
                player.sendMessage(Component.text("Action bar messages toggled!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
    }
    
    private void setupSoundSettings() {
        // Master volume
        ItemStack masterVolumeItem = new ItemBuilder(Material.NOTE_BLOCK)
            .name(Component.text("Master Volume").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Adjust overall sound volume").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Volume: ").color(NamedTextColor.YELLOW)
                    .append(Component.text("100%").color(NamedTextColor.GREEN)),
                Component.empty(),
                Component.text("Left-click: Decrease").color(NamedTextColor.AQUA),
                Component.text("Right-click: Increase").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(19, masterVolumeItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                // TODO: Decrease volume
                player.sendMessage(Component.text("Volume decreased!")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 0.8f, 0.8f);
                refresh();
            } else if (clickType == ClickType.RIGHT) {
                // TODO: Increase volume
                player.sendMessage(Component.text("Volume increased!")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
        
        // Combat sounds toggle
        ItemStack combatSoundsItem = new ItemBuilder(Material.IRON_SWORD)
            .name(Component.text("Combat Sounds").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Play sounds for attacks and abilities").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Status: ").color(NamedTextColor.YELLOW)
                    .append(Component.text("Enabled").color(NamedTextColor.GREEN)),
                Component.empty(),
                Component.text("Click to toggle").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(20, combatSoundsItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                // TODO: Toggle combat sounds
                player.sendMessage(Component.text("Combat sounds toggled!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_ATTACK_SWEEP, 1.0f, 1.0f);
                refresh();
            }
        });
    }
    
    private void setupNotificationSettings() {
        // Party invites
        ItemStack partyInvitesItem = new ItemBuilder(Material.WRITABLE_BOOK)
            .name(Component.text("Party Invitations").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Receive party invitations").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Status: ").color(NamedTextColor.YELLOW)
                    .append(Component.text("Enabled").color(NamedTextColor.GREEN)),
                Component.empty(),
                Component.text("Click to toggle").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(28, partyInvitesItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                // TODO: Toggle party invites
                player.sendMessage(Component.text("Party invitations toggled!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
        
        // Achievement notifications
        ItemStack achievementItem = new ItemBuilder(Material.GOLDEN_APPLE)
            .name(Component.text("Achievement Notifications").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Show achievement unlock messages").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Status: ").color(NamedTextColor.YELLOW)
                    .append(Component.text("Enabled").color(NamedTextColor.GREEN)),
                Component.empty(),
                Component.text("Click to toggle").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(29, achievementItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                // TODO: Toggle achievement notifications
                player.sendMessage(Component.text("Achievement notifications toggled!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.UI_TOAST_CHALLENGE_COMPLETE, 1.0f, 1.0f);
                refresh();
            }
        });
    }
    
    private void setupPrivacySettings() {
        // Spectate mode
        ItemStack spectateItem = new ItemBuilder(Material.ENDER_EYE)
            .name(Component.text("Allow Spectating").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Allow others to spectate your runs").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Status: ").color(NamedTextColor.YELLOW)
                    .append(Component.text("Enabled").color(NamedTextColor.GREEN)),
                Component.empty(),
                Component.text("Click to toggle").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(37, spectateItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                // TODO: Toggle spectate permission
                player.sendMessage(Component.text("Spectate permission toggled!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
        
        // Leaderboard visibility
        ItemStack leaderboardItem = new ItemBuilder(Material.LECTERN)
            .name(Component.text("Leaderboard Visibility").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Show your scores on leaderboards").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Status: ").color(NamedTextColor.YELLOW)
                    .append(Component.text("Public").color(NamedTextColor.GREEN)),
                Component.empty(),
                Component.text("Click to toggle").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(38, leaderboardItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                // TODO: Toggle leaderboard visibility
                player.sendMessage(Component.text("Leaderboard visibility toggled!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
    }
    
    private void setupPerformanceSettings() {
        // Particle effects
        ItemStack particlesItem = new ItemBuilder(Material.BLAZE_POWDER)
            .name(Component.text("Particle Effects").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Show ability and combat particles").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Level: ").color(NamedTextColor.YELLOW)
                    .append(Component.text("High").color(NamedTextColor.GREEN)),
                Component.empty(),
                Component.text("Left-click: Decrease").color(NamedTextColor.AQUA),
                Component.text("Right-click: Increase").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(16, particlesItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                // TODO: Decrease particle level
                player.sendMessage(Component.text("Particle level decreased!")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 0.8f);
                refresh();
            } else if (clickType == ClickType.RIGHT) {
                // TODO: Increase particle level
                player.sendMessage(Component.text("Particle level increased!")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
        
        // Auto-rejoin
        ItemStack rejoinItem = new ItemBuilder(Material.RECOVERY_COMPASS)
            .name(Component.text("Auto-Rejoin").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Automatically rejoin on disconnect").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Status: ").color(NamedTextColor.YELLOW)
                    .append(Component.text("Enabled").color(NamedTextColor.GREEN)),
                Component.empty(),
                Component.text("Click to toggle").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(25, rejoinItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                // TODO: Toggle auto-rejoin
                player.sendMessage(Component.text("Auto-rejoin toggled!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
    }
}

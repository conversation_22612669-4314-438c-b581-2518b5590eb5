package com.ultimatedungeon.udx.api.progression;

import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * Immutable representation of achievement data for API consumers.
 */
public record AchievementData(
    @NotNull String id,
    @NotNull String name,
    @NotNull String description,
    @NotNull String category,
    @NotNull String rarity,
    int points,
    boolean hidden,
    @NotNull List<String> requirements
) {
    
    public AchievementData {
        if (id == null || id.isBlank()) throw new IllegalArgumentException("ID cannot be null or blank");
        if (name == null || name.isBlank()) throw new IllegalArgumentException("Name cannot be null or blank");
        if (description == null) throw new IllegalArgumentException("Description cannot be null");
        if (category == null || category.isBlank()) throw new IllegalArgumentException("Category cannot be null or blank");
        if (rarity == null || rarity.isBlank()) throw new IllegalArgumentException("Rarity cannot be null or blank");
        if (points < 0) throw new IllegalArgumentException("Points cannot be negative");
        if (requirements == null) throw new IllegalArgumentException("Requirements cannot be null");
    }
}

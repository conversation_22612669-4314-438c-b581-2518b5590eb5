# UltimateDungeonX - User Guide

## Overview

UltimateDungeonX is a comprehensive, no-dependency dungeon plugin for Paper 1.21.x that provides instanced dungeons with procedural generation, custom mobs, boss fights, and a complete GUI-first experience.

## Features

- **🏰 Instanced Dungeons**: Each party gets their own private dungeon world
- **🎲 Procedural Generation**: Dungeons are built from room templates with intelligent connector matching
- **👹 Custom Mobs & Bosses**: First-party mob system with behaviors, abilities, and multi-phase bosses
- **🎮 GUI-First Design**: Everything manageable through intuitive menus
- **👥 Party System**: Create parties, invite friends, and tackle dungeons together
- **🏆 Progression**: Unlock dungeons, earn achievements, climb leaderboards
- **⚡ Performance Optimized**: Async operations, tick budgeting, and memory management
- **🔧 No Dependencies**: Runs standalone with optional Vault/PlaceholderAPI integration

## Installation

### Requirements

- **Server**: Paper 1.21.x (latest recommended)
- **Java**: Java 21 or higher
- **RAM**: Minimum 2GB allocated to server
- **Optional**: Vault (economy), PlaceholderAPI (placeholders)

### Installation Steps

1. **Download** the latest `UltimateDungeonX-<version>.jar` from the releases
2. **Place** the jar file in your server's `plugins/` directory
3. **Start** your server to generate the plugin files
4. **Configure** the plugin using the files in `plugins/UltimateDungeonX/`
5. **Restart** the server to apply configuration changes

### First-Time Setup

1. **Create a Build World** (recommended name: `udx_build`)
   ```
   /mv create udx_build normal -g VoidGen
   ```

2. **Set Build World in Config**
   ```yaml
   # config.yml
   build_world: "udx_build"
   ```

3. **Give Yourself Admin Permissions**
   ```
   /lp user <username> permission set udx.admin true
   ```

4. **Access the Admin Hub**
   ```
   /udx admin
   ```

## Getting Started

### For Players

#### Joining Dungeons

1. **Open Main Menu**: `/udx`
2. **Browse Dungeons**: Click "Play" to see available dungeons
3. **Create/Join Party**: Invite friends or join existing parties
4. **Queue Up**: Select difficulty and enter the queue
5. **Ready Check**: Confirm you're ready when prompted
6. **Enter Dungeon**: Get teleported to your private instance

#### Party Management

- **Create Party**: `/udx` → "Parties" → "Create Party"
- **Invite Players**: Click on nearby players or type names
- **Leave Party**: Use the party menu or `/udx` → "Parties" → "Leave"
- **Transfer Leadership**: Party leaders can promote members

#### Progression

- **View Progress**: `/udx` → "Achievements" to see your progress
- **Leaderboards**: Check seasonal rankings and best times
- **Unlock Dungeons**: Complete prerequisite dungeons to unlock new ones

### For Administrators

#### Creating Your First Dungeon

1. **Access Admin Hub**: `/udx admin`
2. **Create Dungeon**: Click "Create New Dungeon"
3. **Configure Basic Settings**:
   - Name and description
   - Difficulty range
   - Party size requirements
   - Theme and environment

4. **Build Rooms**: 
   - Go to your build world
   - Create room layouts with blocks
   - Use `/udx editor` to capture rooms
   - Set connectors (N/E/S/W/UP/DOWN)
   - Place markers for spawns, chests, etc.

5. **Configure Mobs & Bosses**:
   - Use the mob editor to create custom enemies
   - Design boss fights with multiple phases
   - Set up loot tables and rewards

6. **Test Your Dungeon**:
   - Use the simulation feature to preview generation
   - Run test instances to verify balance
   - Adjust difficulty and rewards as needed

#### Room Building Guide

**Basic Room Structure**:
- Rooms should be enclosed (walls on all sides)
- Include clear floor and ceiling
- Leave space for connectors on edges
- Use consistent height (recommended: 5-7 blocks)

**Connectors**:
- Mark where rooms can connect to others
- Specify direction (North, East, South, West, Up, Down)
- Set size (1x1, 2x2, 3x3, etc.)
- Ensure connectors align between rooms

**Markers**:
- **SPAWN**: Where mobs appear (`{mobId, wave?, once?}`)
- **BOSS_SPAWN**: Boss encounter location
- **CHEST**: Loot container (`{tableId}`)
- **GATE**: Doors/barriers (`{openBy: kill/wave/lever}`)
- **TRIGGER**: Script activation points
- **CHECKPOINT**: Respawn locations
- **EFFECT**: Environmental effects (`{fog, light, weather}`)

## Commands

### Player Commands

| Command | Description | Permission |
|---------|-------------|------------|
| `/udx` | Open main menu | `udx.use` |
| `/udx spectate` | Spectate active runs | `udx.spectate` |

### Admin Commands

| Command | Description | Permission |
|---------|-------------|------------|
| `/udx admin` | Open admin hub | `udx.admin` |
| `/udx editor` | Open room/mob editors | `udx.editor.*` |
| `/udx portal` | Get portal keystone item | `udx.admin.portal` |

## Configuration

### Main Config (`config.yml`)

```yaml
# General Settings
build_world: "udx_build"
max_concurrent_instances: 10
instance_timeout_minutes: 60

# Performance Settings
tick_budget_per_frame: 5000  # microseconds
max_entities_per_instance: 100
async_world_generation: true

# GUI Settings
menu_update_interval: 20  # ticks
sound_effects_enabled: true
particle_effects_enabled: true

# Database Settings
database:
  type: "sqlite"  # sqlite or mysql
  file: "players.db"
  # For MySQL:
  # host: "localhost"
  # port: 3306
  # database: "udx"
  # username: "user"
  # password: "pass"

# Integration Settings
vault_integration: true
placeholderapi_integration: true

# Affix Rotation
affix_rotation:
  daily_count: 2
  weekly_count: 4
  rotation_time: "00:00"  # UTC time for daily rotation
```

### Dungeon Configuration

Each dungeon has its own `dungeon.yml` file:

```yaml
id: "my_dungeon"
name: "My Custom Dungeon"
description: "A challenging dungeon for experienced players"

# Basic Properties
theme: "medieval"
difficulty_base: 5
length_min: 6
length_max: 10

# Requirements
level_requirement:
  min: 20
  max: 50
  recommended: 35

# Party Settings
party:
  min_size: 2
  max_size: 5
  recommended_size: 4

# Rooms
rooms:
  start_room: "entrance_hall"
  boss_room: "throne_room"
  available_rooms:
    - "corridor_basic"
    - "chamber_large"
    - "treasure_room"

# Loot Tables
loot_tables:
  common_chest: "dungeon_common"
  boss_loot: "boss_rewards"
```

## Permissions

### Player Permissions

- `udx.use` - Access basic plugin features
- `udx.join` - Join dungeon runs
- `udx.party` - Create and manage parties
- `udx.spectate` - Spectate active runs

### Admin Permissions

- `udx.admin.*` - All administrative permissions
- `udx.admin` - Access admin hub
- `udx.admin.create` - Create new dungeons
- `udx.admin.manage` - Modify existing dungeons
- `udx.admin.teleport` - Teleport to instances
- `udx.admin.force` - Force start/stop runs
- `udx.admin.portal` - Create portal keystones
- `udx.admin.debug` - Access debug tools

### Editor Permissions

- `udx.editor.*` - All editor permissions
- `udx.editor.room` - Room editor access
- `udx.editor.spawner` - Spawner editor access
- `udx.editor.boss` - Boss editor access
- `udx.editor.loot` - Loot editor access

## Troubleshooting

### Common Issues

**"No dungeons available"**
- Ensure dungeons are properly configured
- Check that rooms have been created and saved
- Verify loot tables and mob definitions exist

**"Failed to create instance"**
- Check server has enough RAM
- Verify world generation permissions
- Look for errors in console logs

**"Mobs not spawning"**
- Ensure spawner definitions are valid
- Check mob definitions exist
- Verify room markers are placed correctly

**Performance Issues**
- Reduce `tick_budget_per_frame` in config
- Lower `max_entities_per_instance`
- Enable `async_world_generation`
- Check for memory leaks in console

### Debug Tools

Access debug information via `/udx admin` → "Debug":

- **Performance Monitor**: Real-time tick usage and memory stats
- **Instance Manager**: View active instances and their status
- **Entity Tracker**: Monitor mob counts and cleanup
- **Memory Profiler**: Check for memory leaks

### Getting Help

1. **Check Console Logs**: Look for error messages and stack traces
2. **Enable Debug Mode**: Set `debug: true` in config.yml
3. **Use Debug Menu**: Access via admin hub for real-time diagnostics
4. **Report Issues**: Include logs, config files, and reproduction steps

## Advanced Features

### Custom Affixes

Create custom dungeon modifiers:

```yaml
# affixes/my_affix.yml
id: "my_custom_affix"
name: "Custom Modifier"
description: "Does something interesting"
effects:
  mob_damage_multiplier: 1.5
  loot_multiplier: 1.2
```

### Portal Keystones

Create permanent dungeon portals:

1. Get keystone: `/udx portal`
2. Right-click to place portal
3. Configure target dungeon in GUI
4. Players right-click portal to open dungeon browser

### Seasonal Events

Configure special seasonal content:

```yaml
# config.yml
seasonal_events:
  halloween:
    start_date: "10-01"
    end_date: "11-01"
    special_affixes: ["spooky_mobs", "pumpkin_loot"]
    bonus_rewards: 2.0
```

## API for Developers

UltimateDungeonX provides a comprehensive API for addon development. See `docs/API.md` for detailed documentation.

### Basic Usage

```java
// Get the API
UltimateDungeonXAPI api = UltimateDungeonXAPI.getInstance();

// Listen for events
@EventHandler
public void onDungeonComplete(DungeonCompleteEvent event) {
    Player player = event.getPlayer();
    String dungeonId = event.getDungeonId();
    // Handle completion
}

// Access services
DungeonService dungeonService = api.getDungeonService();
PartyService partyService = api.getPartyService();
```

## Support

For support, bug reports, or feature requests:

- **Documentation**: Check `docs/` folder for detailed guides
- **Issues**: Report bugs with full error logs and reproduction steps
- **Discord**: Join our community server for real-time help
- **Wiki**: Visit the online wiki for tutorials and examples

---

*UltimateDungeonX - The ultimate dungeon experience for Minecraft servers*

package com.ultimatedungeon.udx.boss;

import com.ultimatedungeon.udx.boss.BossDef.*;
import org.bukkit.Location;
import org.bukkit.boss.BarColor;
import org.bukkit.boss.BarStyle;
import org.bukkit.boss.BossBar;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Represents an active boss instance in a dungeon.
 * 
 * <p>Manages the boss entity, current phase, active abilities,
 * and player interactions during a boss fight.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class BossInstance {
    
    private final String instanceId;
    private final BossDef definition;
    private final LivingEntity entity;
    private final Location spawnLocation;
    private final BossBar bossBar;
    
    // State management
    private BossPhase currentPhase;
    private BossState state;
    private long fightStartTime;
    private long phaseStartTime;
    
    // Ability tracking
    private final Map<String, Long> abilityCooldowns;
    private final Map<String, Integer> abilityUseCounts;
    private final Set<String> activeAbilities;
    
    // Player tracking
    private final Set<UUID> participants;
    private final Map<UUID, BossPlayerData> playerData;
    
    // Mechanics
    private final Map<String, Object> mechanicsData;
    private boolean enraged;
    private double enrageThreshold;
    
    public enum BossState {
        SPAWNING,
        ACTIVE,
        TRANSITIONING,
        DEFEATED,
        DESPAWNED
    }
    
    /**
     * Player-specific boss data.
     */
    public static class BossPlayerData {
        private double damageDealt;
        private double damageTaken;
        private int deathCount;
        private long lastDamageTime;
        private final Map<String, Object> customData;
        
        public BossPlayerData() {
            this.customData = new HashMap<>();
        }
        
        // Getters and setters
        public double getDamageDealt() { return damageDealt; }
        public void addDamageDealt(double damage) { this.damageDealt += damage; }
        
        public double getDamageTaken() { return damageTaken; }
        public void addDamageTaken(double damage) { 
            this.damageTaken += damage;
            this.lastDamageTime = System.currentTimeMillis();
        }
        
        public int getDeathCount() { return deathCount; }
        public void incrementDeaths() { this.deathCount++; }
        
        public long getLastDamageTime() { return lastDamageTime; }
        
        public Map<String, Object> getCustomData() { return customData; }
    }
    
    public BossInstance(@NotNull String instanceId, @NotNull BossDef definition, 
                       @NotNull LivingEntity entity, @NotNull Location spawnLocation) {
        this.instanceId = instanceId;
        this.definition = definition;
        this.entity = entity;
        this.spawnLocation = spawnLocation.clone();
        
        // Initialize state
        this.state = BossState.SPAWNING;
        this.currentPhase = definition.getFirstPhase();
        this.fightStartTime = System.currentTimeMillis();
        this.phaseStartTime = System.currentTimeMillis();
        
        // Initialize tracking
        this.abilityCooldowns = new ConcurrentHashMap<>();
        this.abilityUseCounts = new ConcurrentHashMap<>();
        this.activeAbilities = ConcurrentHashMap.newKeySet();
        this.participants = ConcurrentHashMap.newKeySet();
        this.playerData = new ConcurrentHashMap<>();
        this.mechanicsData = new ConcurrentHashMap<>();
        
        // Initialize enrage
        this.enraged = false;
        this.enrageThreshold = 0.1; // 10% health by default
        
        // Create boss bar
        this.bossBar = createBossBar();
        
        // Apply initial phase
        applyPhase(currentPhase);
    }
    
    /**
     * Creates the boss bar for this instance.
     */
    private BossBar createBossBar() {
        BossBar bar = org.bukkit.Bukkit.createBossBar(
            definition.displayName(),
            BarColor.RED,
            BarStyle.SOLID
        );
        
        bar.setProgress(1.0);
        return bar;
    }
    
    /**
     * Updates the boss instance state.
     */
    public void update() {
        if (state != BossState.ACTIVE) {
            return;
        }
        
        // Update boss bar
        updateBossBar();
        
        // Check for phase transitions
        checkPhaseTransitions();
        
        // Check for enrage
        checkEnrage();
        
        // Update abilities
        updateAbilities();
        
        // Clean up expired data
        cleanupExpiredData();
    }
    
    /**
     * Updates the boss bar display.
     */
    private void updateBossBar() {
        if (entity.isDead()) {
            bossBar.setProgress(0.0);
            return;
        }
        
        double healthPercent = entity.getHealth() / entity.getAttribute(
            org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
        bossBar.setProgress(Math.max(0.0, Math.min(1.0, healthPercent)));
        
        // Update color based on health
        if (enraged) {
            bossBar.setColor(BarColor.PURPLE);
        } else if (healthPercent < 0.25) {
            bossBar.setColor(BarColor.RED);
        } else if (healthPercent < 0.5) {
            bossBar.setColor(BarColor.YELLOW);
        } else {
            bossBar.setColor(BarColor.GREEN);
        }
    }
    
    /**
     * Checks for phase transition conditions.
     */
    private void checkPhaseTransitions() {
        BossPhase nextPhase = definition.getNextPhase(currentPhase);
        if (nextPhase == null) {
            return; // No more phases
        }
        
        PhaseTrigger trigger = nextPhase.trigger();
        boolean shouldTransition = false;
        
        switch (trigger.type()) {
            case HEALTH_PERCENT -> {
                double healthPercent = (entity.getHealth() / entity.getAttribute(
                    org.bukkit.attribute.Attribute.MAX_HEALTH).getValue()) * 100;
                shouldTransition = healthPercent <= trigger.value();
            }
            case TIMER -> {
                long phaseTime = System.currentTimeMillis() - phaseStartTime;
                shouldTransition = phaseTime >= (long) (trigger.value() * 1000);
            }
            case PLAYER_COUNT -> {
                shouldTransition = participants.size() <= (int) trigger.value();
            }
            // Add more trigger types as needed
        }
        
        if (shouldTransition) {
            transitionToPhase(nextPhase);
        }
    }
    
    /**
     * Transitions to a new phase.
     */
    public void transitionToPhase(@NotNull BossPhase newPhase) {
        state = BossState.TRANSITIONING;
        
        // Announce phase change
        if (newPhase.announcement() != null) {
            announceToParticipants(newPhase.announcement());
        }
        
        // Clear current abilities
        activeAbilities.clear();
        
        // Apply new phase
        applyPhase(newPhase);
        
        this.currentPhase = newPhase;
        this.phaseStartTime = System.currentTimeMillis();
        this.state = BossState.ACTIVE;
    }
    
    /**
     * Applies phase configuration to the boss.
     */
    private void applyPhase(@NotNull BossPhase phase) {
        // Apply environmental changes
        applyEnvironmentalChanges(phase.environment());
        
        // Initialize phase abilities
        for (BossAbility ability : phase.abilities()) {
            initializeAbility(ability);
        }
        
        // Apply mechanics
        for (String mechanic : phase.mechanics()) {
            applyMechanic(mechanic);
        }
    }
    
    /**
     * Applies environmental changes for a phase.
     */
    private void applyEnvironmentalChanges(@NotNull Map<String, Object> environment) {
        // Implementation depends on specific environmental effects
        // Examples: weather changes, lighting, particle effects, etc.
    }
    
    /**
     * Initializes an ability for use.
     */
    private void initializeAbility(@NotNull BossAbility ability) {
        abilityCooldowns.put(ability.id(), 0L);
        abilityUseCounts.put(ability.id(), 0);
        
        // Schedule interval-based abilities
        if (ability.timing().type() == AbilityTiming.TimingType.INTERVAL) {
            // This would be handled by the BossService scheduler
        }
    }
    
    /**
     * Applies a mechanic to the boss fight.
     */
    private void applyMechanic(@NotNull String mechanic) {
        // Implementation depends on specific mechanics
        // Examples: arena barriers, hazards, special rules, etc.
        mechanicsData.put(mechanic, System.currentTimeMillis());
    }
    
    /**
     * Checks for enrage conditions.
     */
    private void checkEnrage() {
        if (enraged) {
            return;
        }
        
        double healthPercent = entity.getHealth() / entity.getAttribute(
            org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
        
        if (healthPercent <= enrageThreshold) {
            enrage();
        }
    }
    
    /**
     * Triggers enrage mode.
     */
    private void enrage() {
        enraged = true;
        announceToParticipants("§c§l" + definition.displayName() + " becomes enraged!");
        
        // Apply enrage effects (increased damage, speed, etc.)
        // This would modify the entity's attributes
    }
    
    /**
     * Updates ability states and triggers.
     */
    private void updateAbilities() {
        long currentTime = System.currentTimeMillis();
        
        for (BossAbility ability : currentPhase.abilities()) {
            if (canUseAbility(ability, currentTime)) {
                useAbility(ability);
            }
        }
    }
    
    /**
     * Checks if an ability can be used.
     */
    private boolean canUseAbility(@NotNull BossAbility ability, long currentTime) {
        // Check cooldown
        Long lastUse = abilityCooldowns.get(ability.id());
        if (lastUse != null && currentTime - lastUse < ability.timing().interval()) {
            return false;
        }
        
        // Check use count
        Integer useCount = abilityUseCounts.get(ability.id());
        if (useCount != null && useCount >= ability.timing().maxUses() && ability.timing().maxUses() > 0) {
            return false;
        }
        
        // Check if already active
        if (activeAbilities.contains(ability.id())) {
            return false;
        }
        
        // Check conditions
        return checkAbilityConditions(ability);
    }
    
    /**
     * Checks ability conditions.
     */
    private boolean checkAbilityConditions(@NotNull BossAbility ability) {
        // Implementation depends on specific conditions
        // Examples: player distance, health thresholds, etc.
        return true;
    }
    
    /**
     * Uses an ability.
     */
    private void useAbility(@NotNull BossAbility ability) {
        activeAbilities.add(ability.id());
        
        // Update tracking
        abilityCooldowns.put(ability.id(), System.currentTimeMillis());
        abilityUseCounts.merge(ability.id(), 1, Integer::sum);
        
        // This would be handled by the BossService to execute the actual ability
    }
    
    /**
     * Cleans up expired data.
     */
    private void cleanupExpiredData() {
        long currentTime = System.currentTimeMillis();
        
        // Remove old player data for disconnected players
        playerData.entrySet().removeIf(entry -> {
            Player player = org.bukkit.Bukkit.getPlayer(entry.getKey());
            return player == null || !player.isOnline() || 
                   currentTime - entry.getValue().getLastDamageTime() > 300000; // 5 minutes
        });
    }
    
    /**
     * Adds a player to the boss fight.
     */
    public void addParticipant(@NotNull Player player) {
        participants.add(player.getUniqueId());
        playerData.putIfAbsent(player.getUniqueId(), new BossPlayerData());
        bossBar.addPlayer(player);
    }
    
    /**
     * Removes a player from the boss fight.
     */
    public void removeParticipant(@NotNull Player player) {
        participants.remove(player.getUniqueId());
        bossBar.removePlayer(player);
    }
    
    /**
     * Announces a message to all participants.
     */
    private void announceToParticipants(@NotNull String message) {
        for (UUID playerId : participants) {
            Player player = org.bukkit.Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                player.sendMessage(message);
            }
        }
    }
    
    /**
     * Marks the boss as defeated.
     */
    public void defeat() {
        state = BossState.DEFEATED;
        bossBar.setProgress(0.0);
        announceToParticipants("§a§l" + definition.displayName() + " has been defeated!");
    }
    
    /**
     * Cleans up the boss instance.
     */
    public void cleanup() {
        state = BossState.DESPAWNED;
        bossBar.removeAll();
        activeAbilities.clear();
        participants.clear();
        playerData.clear();
        mechanicsData.clear();
    }
    
    // Getters
    
    @NotNull
    public String getInstanceId() {
        return instanceId;
    }
    
    @NotNull
    public BossDef getDefinition() {
        return definition;
    }
    
    @NotNull
    public LivingEntity getEntity() {
        return entity;
    }
    
    @NotNull
    public Location getSpawnLocation() {
        return spawnLocation.clone();
    }
    
    @NotNull
    public BossPhase getCurrentPhase() {
        return currentPhase;
    }
    
    @NotNull
    public BossState getState() {
        return state;
    }
    
    public long getFightStartTime() {
        return fightStartTime;
    }
    
    public long getPhaseStartTime() {
        return phaseStartTime;
    }
    
    @NotNull
    public Set<UUID> getParticipants() {
        return Collections.unmodifiableSet(participants);
    }
    
    @Nullable
    public BossPlayerData getPlayerData(@NotNull UUID playerId) {
        return playerData.get(playerId);
    }
    
    public boolean isEnraged() {
        return enraged;
    }
    
    @NotNull
    public BossBar getBossBar() {
        return bossBar;
    }
    
    @NotNull
    public Set<String> getActiveAbilities() {
        return Collections.unmodifiableSet(activeAbilities);
    }
    
    @NotNull
    public Map<String, Object> getMechanicsData() {
        return Collections.unmodifiableMap(mechanicsData);
    }
    
    /**
     * Gets the plugin instance.
     * 
     * @return the plugin instance
     */
    @NotNull
    public com.ultimatedungeon.udx.bootstrap.UltimateDungeonX getPlugin() {
        return com.ultimatedungeon.udx.bootstrap.UltimateDungeonX.getInstance();
    }
    
    /**
     * Gets participants as Player objects.
     * 
     * @return collection of online participant players
     */
    @NotNull
    public Collection<Player> getParticipantPlayers() {
        List<Player> players = new ArrayList<>();
        for (UUID playerId : participants) {
            Player player = org.bukkit.Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                players.add(player);
            }
        }
        return players;
    }
}

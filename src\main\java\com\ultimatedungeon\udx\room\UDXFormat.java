package com.ultimatedungeon.udx.room;

import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import org.bukkit.Material;
import org.jetbrains.annotations.NotNull;

import java.io.*;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.*;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * Handles serialization and deserialization of room templates in UDX format.
 * 
 * <p>The UDX format is a compressed JSON format that efficiently stores
 * room template data including blocks, connectors, markers, and metadata.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class UDXFormat {
    
    private static final String FORMAT_VERSION = "1.0";
    private static final int MAGIC_NUMBER = 0x55445820; // "UDX " in hex
    
    private final Gson gson;
    
    public UDXFormat() {
        this.gson = new GsonBuilder()
            .registerTypeAdapter(Material.class, new MaterialAdapter())
            .registerTypeAdapter(Instant.class, new InstantAdapter())
            .registerTypeAdapter(Connector.Direction.class, new EnumAdapter<>(Connector.Direction.class))
            .registerTypeAdapter(Connector.ConnectorSize.class, new EnumAdapter<>(Connector.ConnectorSize.class))
            .registerTypeAdapter(Connector.ConnectorType.class, new EnumAdapter<>(Connector.ConnectorType.class))
            .registerTypeAdapter(Marker.MarkerType.class, new EnumAdapter<>(Marker.MarkerType.class))
            .registerTypeAdapter(RoomTemplate.RoomType.class, new EnumAdapter<>(RoomTemplate.RoomType.class))
            .setPrettyPrinting()
            .create();
    }
    
    /**
     * Saves a room template to a file.
     * 
     * @param template the room template to save
     * @param file the file to save to
     * @throws IOException if an I/O error occurs
     */
    public void saveToFile(@NotNull RoomTemplate template, @NotNull File file) throws IOException {
        UDXData data = convertToUDXData(template);
        
        try (FileOutputStream fos = new FileOutputStream(file);
             GZIPOutputStream gzos = new GZIPOutputStream(fos);
             OutputStreamWriter writer = new OutputStreamWriter(gzos, StandardCharsets.UTF_8)) {
            
            gson.toJson(data, writer);
        }
    }
    
    /**
     * Loads a room template from a file.
     * 
     * @param file the file to load from
     * @return the loaded room template
     * @throws IOException if an I/O error occurs
     * @throws UDXFormatException if the file format is invalid
     */
    @NotNull
    public RoomTemplate loadFromFile(@NotNull File file) throws IOException, UDXFormatException {
        try (FileInputStream fis = new FileInputStream(file);
             GZIPInputStream gzis = new GZIPInputStream(fis);
             InputStreamReader reader = new InputStreamReader(gzis, StandardCharsets.UTF_8)) {
            
            UDXData data = gson.fromJson(reader, UDXData.class);
            if (data == null) {
                throw new UDXFormatException("Invalid UDX file: null data");
            }
            
            validateUDXData(data);
            return convertFromUDXData(data);
            
        } catch (JsonSyntaxException e) {
            throw new UDXFormatException("Invalid JSON in UDX file", e);
        }
    }
    
    /**
     * Converts a room template to UDX data format.
     */
    @NotNull
    private UDXData convertToUDXData(@NotNull RoomTemplate template) {
        UDXData data = new UDXData();
        
        // Header
        data.formatVersion = FORMAT_VERSION;
        data.magicNumber = MAGIC_NUMBER;
        
        // Metadata
        data.id = template.getId();
        data.name = template.getName();
        data.description = template.getDescription();
        data.author = template.getAuthor();
        data.createdAt = template.getCreatedAt();
        data.version = template.getVersion();
        
        // Dimensions
        data.width = template.getWidth();
        data.height = template.getHeight();
        data.depth = template.getDepth();
        
        // Anchor
        data.anchorX = template.getAnchorX();
        data.anchorY = template.getAnchorY();
        data.anchorZ = template.getAnchorZ();
        
        // Block data
        data.blockPalette = Arrays.asList(template.getBlockPalette());
        data.blockData = template.getBlockData();
        data.tileEntityData = template.getTileEntityData();
        
        // Room elements
        data.connectors = template.getConnectors();
        data.markers = template.getMarkers();
        data.tags = template.getTags();
        data.properties = template.getProperties();
        
        // Generation settings
        data.weight = template.getWeight();
        data.minDepth = template.getMinDepth();
        data.maxDepth = template.getMaxDepth();
        data.roomType = template.getRoomType();
        
        return data;
    }
    
    /**
     * Converts UDX data to a room template.
     */
    @NotNull
    private RoomTemplate convertFromUDXData(@NotNull UDXData data) throws UDXFormatException {
        try {
            RoomTemplate.Builder builder = new RoomTemplate.Builder(data.id)
                .name(data.name)
                .description(data.description)
                .author(data.author)
                .createdAt(data.createdAt)
                .version(data.version)
                .dimensions(data.width, data.height, data.depth)
                .anchor(data.anchorX, data.anchorY, data.anchorZ)
                .blockPalette(data.blockPalette)
                .blockData(data.blockData)
                .weight(data.weight)
                .depthRange(data.minDepth, data.maxDepth)
                .roomType(data.roomType);
            
            // Add tile entity data
            for (Map.Entry<Integer, String> entry : data.tileEntityData.entrySet()) {
                builder.tileEntity(entry.getKey(), entry.getValue());
            }
            
            // Add connectors
            for (Connector connector : data.connectors) {
                builder.connector(connector);
            }
            
            // Add markers
            for (Marker marker : data.markers) {
                builder.marker(marker);
            }
            
            // Add tags
            for (String tag : data.tags) {
                builder.tag(tag);
            }
            
            // Add properties
            for (Map.Entry<String, Object> entry : data.properties.entrySet()) {
                builder.property(entry.getKey(), entry.getValue());
            }
            
            return builder.build();
            
        } catch (Exception e) {
            throw new UDXFormatException("Failed to convert UDX data to room template", e);
        }
    }
    
    /**
     * Validates UDX data format.
     */
    private void validateUDXData(@NotNull UDXData data) throws UDXFormatException {
        if (data.magicNumber != MAGIC_NUMBER) {
            throw new UDXFormatException("Invalid magic number: " + Integer.toHexString(data.magicNumber));
        }
        
        if (!FORMAT_VERSION.equals(data.formatVersion)) {
            throw new UDXFormatException("Unsupported format version: " + data.formatVersion);
        }
        
        if (data.id == null || data.id.trim().isEmpty()) {
            throw new UDXFormatException("Room ID cannot be null or empty");
        }
        
        if (data.width <= 0 || data.height <= 0 || data.depth <= 0) {
            throw new UDXFormatException("Invalid dimensions: " + data.width + "x" + data.height + "x" + data.depth);
        }
        
        if (data.blockData == null || data.blockData.length != data.width * data.height * data.depth) {
            throw new UDXFormatException("Block data size mismatch");
        }
        
        if (data.blockPalette == null || data.blockPalette.isEmpty()) {
            throw new UDXFormatException("Block palette cannot be empty");
        }
    }
    
    /**
     * Internal data structure for UDX format.
     */
    private static class UDXData {
        // Header
        String formatVersion;
        int magicNumber;
        
        // Metadata
        String id;
        String name;
        String description;
        String author;
        Instant createdAt;
        int version;
        
        // Dimensions
        int width;
        int height;
        int depth;
        
        // Anchor
        int anchorX;
        int anchorY;
        int anchorZ;
        
        // Block data
        List<Material> blockPalette;
        int[] blockData;
        Map<Integer, String> tileEntityData;
        
        // Room elements
        List<Connector> connectors;
        List<Marker> markers;
        Set<String> tags;
        Map<String, Object> properties;
        
        // Generation settings
        int weight;
        int minDepth;
        int maxDepth;
        RoomTemplate.RoomType roomType;
    }
    
    /**
     * Custom adapter for Material enum.
     */
    private static class MaterialAdapter implements JsonSerializer<Material>, JsonDeserializer<Material> {
        @Override
        public JsonElement serialize(Material src, Type typeOfSrc, JsonSerializationContext context) {
            return new JsonPrimitive(src.name());
        }
        
        @Override
        public Material deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
            try {
                return Material.valueOf(json.getAsString());
            } catch (IllegalArgumentException e) {
                throw new JsonParseException("Unknown material: " + json.getAsString(), e);
            }
        }
    }
    
    /**
     * Custom adapter for Instant.
     */
    private static class InstantAdapter implements JsonSerializer<Instant>, JsonDeserializer<Instant> {
        @Override
        public JsonElement serialize(Instant src, Type typeOfSrc, JsonSerializationContext context) {
            return new JsonPrimitive(src.toString());
        }
        
        @Override
        public Instant deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
            try {
                return Instant.parse(json.getAsString());
            } catch (Exception e) {
                throw new JsonParseException("Invalid instant format: " + json.getAsString(), e);
            }
        }
    }
    
    /**
     * Generic enum adapter.
     */
    private static class EnumAdapter<T extends Enum<T>> implements JsonSerializer<T>, JsonDeserializer<T> {
        private final Class<T> enumClass;
        
        public EnumAdapter(Class<T> enumClass) {
            this.enumClass = enumClass;
        }
        
        @Override
        public JsonElement serialize(T src, Type typeOfSrc, JsonSerializationContext context) {
            return new JsonPrimitive(src.name());
        }
        
        @Override
        public T deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
            try {
                return Enum.valueOf(enumClass, json.getAsString());
            } catch (IllegalArgumentException e) {
                throw new JsonParseException("Unknown enum value: " + json.getAsString(), e);
            }
        }
    }
    
    /**
     * Exception thrown when UDX format is invalid.
     */
    public static class UDXFormatException extends Exception {
        public UDXFormatException(String message) {
            super(message);
        }
        
        public UDXFormatException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}

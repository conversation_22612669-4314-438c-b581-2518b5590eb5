package com.ultimatedungeon.udx.dungeon;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.Set;

/**
 * Represents a dungeon definition with all its configuration and metadata.
 * 
 * <p>This record contains all the information needed to generate and run
 * a dungeon instance, including room constraints, difficulty settings,
 * allowed affixes, and theme information.</p>
 * 
 * @param id unique identifier for the dungeon
 * @param displayName human-readable name shown in GUIs
 * @param description brief description of the dungeon
 * @param minLength minimum number of rooms
 * @param maxLength maximum number of rooms
 * @param maxBranching maximum branching factor for room generation
 * @param themeTags list of theme tags for filtering and room selection
 * @param allowedAffixes set of affix IDs that can be applied to this dungeon
 * @param requiredDungeons list of dungeon IDs that must be completed to unlock this one
 * @param minPlayerLevel minimum player level required (if level system is implemented)
 * @param maxPlayerLevel maximum player level allowed (for balance)
 * @param enabled whether this dungeon is currently available
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public record DungeonDef(
    @NotNull String id,
    @NotNull String displayName,
    @NotNull String description,
    int minLength,
    int maxLength,
    int maxBranching,
    @NotNull List<String> themeTags,
    @NotNull Set<String> allowedAffixes,
    @NotNull List<String> requiredDungeons,
    int minPlayerLevel,
    int maxPlayerLevel,
    boolean enabled
) {
    
    /**
     * Creates a new DungeonDef with validation.
     * 
     * @throws IllegalArgumentException if any parameters are invalid
     */
    public DungeonDef {
        if (id == null || id.trim().isEmpty()) {
            throw new IllegalArgumentException("Dungeon ID cannot be null or empty");
        }
        if (displayName == null || displayName.trim().isEmpty()) {
            throw new IllegalArgumentException("Display name cannot be null or empty");
        }
        if (description == null) {
            throw new IllegalArgumentException("Description cannot be null");
        }
        if (minLength < 1) {
            throw new IllegalArgumentException("Minimum length must be at least 1");
        }
        if (maxLength < minLength) {
            throw new IllegalArgumentException("Maximum length must be >= minimum length");
        }
        if (maxBranching < 0) {
            throw new IllegalArgumentException("Max branching cannot be negative");
        }
        if (themeTags == null) {
            throw new IllegalArgumentException("Theme tags cannot be null");
        }
        if (allowedAffixes == null) {
            throw new IllegalArgumentException("Allowed affixes cannot be null");
        }
        if (requiredDungeons == null) {
            throw new IllegalArgumentException("Required dungeons cannot be null");
        }
        if (minPlayerLevel < 0) {
            throw new IllegalArgumentException("Min player level cannot be negative");
        }
        if (maxPlayerLevel > 0 && maxPlayerLevel < minPlayerLevel) {
            throw new IllegalArgumentException("Max player level must be >= min player level");
        }
    }
    
    /**
     * Creates a builder for constructing DungeonDef instances.
     * 
     * @param id the dungeon ID
     * @return a new builder instance
     */
    @NotNull
    public static Builder builder(@NotNull String id) {
        return new Builder(id);
    }
    
    /**
     * Checks if this dungeon has the specified theme tag.
     * 
     * @param tag the theme tag to check
     * @return true if the dungeon has this theme tag
     */
    public boolean hasThemeTag(@NotNull String tag) {
        return themeTags.contains(tag);
    }
    
    /**
     * Checks if the specified affix is allowed for this dungeon.
     * 
     * @param affixId the affix ID to check
     * @return true if the affix is allowed
     */
    public boolean isAffixAllowed(@NotNull String affixId) {
        return allowedAffixes.contains(affixId);
    }
    
    /**
     * Checks if this dungeon requires completion of another dungeon.
     * 
     * @param dungeonId the dungeon ID to check
     * @return true if this dungeon is required
     */
    public boolean requiresDungeon(@NotNull String dungeonId) {
        return requiredDungeons.contains(dungeonId);
    }
    
    /**
     * Gets the average length of this dungeon.
     * 
     * @return the average of min and max length
     */
    public double getAverageLength() {
        return (minLength + maxLength) / 2.0;
    }
    
    /**
     * Checks if a player level is within the allowed range.
     * 
     * @param level the player level to check
     * @return true if the level is allowed
     */
    public boolean isLevelAllowed(int level) {
        if (minPlayerLevel > 0 && level < minPlayerLevel) {
            return false;
        }
        if (maxPlayerLevel > 0 && level > maxPlayerLevel) {
            return false;
        }
        return true;
    }
    
    /**
     * Builder class for creating DungeonDef instances.
     */
    public static class Builder {
        private final String id;
        private String displayName;
        private String description = "";
        private int minLength = 5;
        private int maxLength = 10;
        private int maxBranching = 1;
        private List<String> themeTags = List.of();
        private Set<String> allowedAffixes = Set.of();
        private List<String> requiredDungeons = List.of();
        private int minPlayerLevel = 0;
        private int maxPlayerLevel = 0;
        private boolean enabled = true;
        
        private Builder(@NotNull String id) {
            this.id = id;
            this.displayName = id; // Default display name
        }
        
        public Builder displayName(@NotNull String displayName) {
            this.displayName = displayName;
            return this;
        }
        
        public Builder description(@NotNull String description) {
            this.description = description;
            return this;
        }
        
        public Builder length(int minLength, int maxLength) {
            this.minLength = minLength;
            this.maxLength = maxLength;
            return this;
        }
        
        public Builder maxBranching(int maxBranching) {
            this.maxBranching = maxBranching;
            return this;
        }
        
        public Builder themeTags(@NotNull List<String> themeTags) {
            this.themeTags = List.copyOf(themeTags);
            return this;
        }
        
        public Builder allowedAffixes(@NotNull Set<String> allowedAffixes) {
            this.allowedAffixes = Set.copyOf(allowedAffixes);
            return this;
        }
        
        public Builder requiredDungeons(@NotNull List<String> requiredDungeons) {
            this.requiredDungeons = List.copyOf(requiredDungeons);
            return this;
        }
        
        public Builder playerLevelRange(int minLevel, int maxLevel) {
            this.minPlayerLevel = minLevel;
            this.maxPlayerLevel = maxLevel;
            return this;
        }
        
        public Builder enabled(boolean enabled) {
            this.enabled = enabled;
            return this;
        }
        
        @NotNull
        public DungeonDef build() {
            return new DungeonDef(
                id, displayName, description, minLength, maxLength, maxBranching,
                themeTags, allowedAffixes, requiredDungeons, minPlayerLevel, maxPlayerLevel, enabled
            );
        }
    }
}

package com.ultimatedungeon.udx.tools;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.gui.menus.SpawnerEditorMenu;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

/**
 * Handles interactions with UDX builder wands and tools.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class WandListener implements Listener {
    
    private final UltimateDungeonX plugin;
    
    public WandListener(@NotNull UltimateDungeonX plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onPlayerInteract(@NotNull PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        
        if (item == null || !item.hasItemMeta() || !item.getItemMeta().hasDisplayName()) {
            return;
        }
        
        Component displayName = item.getItemMeta().displayName();
        if (displayName == null) {
            return;
        }
        
        String name = ((net.kyori.adventure.text.TextComponent) displayName).content();
        
        // Check if player is in a dungeon world
        if (!player.getWorld().getName().startsWith("udx_dungeon_")) {
            return;
        }
        
        // Handle different wand types
        switch (name) {
            case "UDX Spawner Wand" -> handleSpawnerWand(event, player);
            case "UDX Mob Selection Wand" -> handleMobSelectionWand(event, player);
            case "UDX Chest Wand" -> handleChestWand(event, player);
            case "UDX Gate & Trigger Wand" -> handleGateWand(event, player);
            case "UDX Selection Wand" -> handleSelectionWand(event, player);
            case "UDX Info Tool" -> handleInfoTool(event, player);
        }
    }
    
    private void handleSpawnerWand(@NotNull PlayerInteractEvent event, @NotNull Player player) {
        event.setCancelled(true);
        
        if (event.getAction() == Action.LEFT_CLICK_BLOCK) {
            // Place spawner
            Block clickedBlock = event.getClickedBlock();
            if (clickedBlock != null) {
                Block targetBlock = clickedBlock.getRelative(event.getBlockFace());
                
                if (targetBlock.getType() == Material.AIR) {
                    targetBlock.setType(Material.SPAWNER);
                    player.sendMessage(Component.text("Spawner placed! Right-click to configure.").color(NamedTextColor.GREEN));
                    player.playSound(player.getLocation(), Sound.BLOCK_STONE_PLACE, 1.0f, 1.0f);
                } else {
                    player.sendMessage(Component.text("Cannot place spawner here - block is not empty.").color(NamedTextColor.RED));
                }
            }
        } else if (event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            // Configure spawner
            Block clickedBlock = event.getClickedBlock();
            if (clickedBlock != null && clickedBlock.getType() == Material.SPAWNER) {
                plugin.getMenuRegistry().openMenu(player, new SpawnerEditorMenu(plugin, player));
                player.sendMessage(Component.text("Opening spawner configuration...").color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
            } else {
                player.sendMessage(Component.text("Right-click a spawner to configure it.").color(NamedTextColor.YELLOW));
            }
        } else if (event.getAction() == Action.LEFT_CLICK_BLOCK && player.isSneaking()) {
            // Remove spawner
            Block clickedBlock = event.getClickedBlock();
            if (clickedBlock != null && clickedBlock.getType() == Material.SPAWNER) {
                clickedBlock.setType(Material.AIR);
                player.sendMessage(Component.text("Spawner removed.").color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.BLOCK_STONE_BREAK, 1.0f, 1.0f);
            }
        }
    }
    
    private void handleMobSelectionWand(@NotNull PlayerInteractEvent event, @NotNull Player player) {
        event.setCancelled(true);
        
        if (event.getAction() == Action.RIGHT_CLICK_AIR || event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            // Open mob selector menu
            openMobSelectorMenu(player);
        } else if (event.getAction() == Action.LEFT_CLICK_BLOCK) {
            // Set mob type for spawner
            Block clickedBlock = event.getClickedBlock();
            if (clickedBlock != null && clickedBlock.getType() == Material.SPAWNER) {
                // TODO: Set spawner mob type based on selected mob
                player.sendMessage(Component.text("Spawner mob type set to ZOMBIE (placeholder)").color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);
            } else {
                player.sendMessage(Component.text("Left-click a spawner to set its mob type.").color(NamedTextColor.YELLOW));
            }
        }
    }
    
    private void handleChestWand(@NotNull PlayerInteractEvent event, @NotNull Player player) {
        event.setCancelled(true);
        
        if (event.getAction() == Action.LEFT_CLICK_BLOCK) {
            // Place chest
            Block clickedBlock = event.getClickedBlock();
            if (clickedBlock != null) {
                Block targetBlock = clickedBlock.getRelative(event.getBlockFace());
                
                if (targetBlock.getType() == Material.AIR) {
                    targetBlock.setType(Material.CHEST);
                    player.sendMessage(Component.text("Loot chest placed! Right-click to configure loot table.").color(NamedTextColor.GREEN));
                    player.playSound(player.getLocation(), Sound.BLOCK_WOOD_PLACE, 1.0f, 1.0f);
                } else {
                    player.sendMessage(Component.text("Cannot place chest here - block is not empty.").color(NamedTextColor.RED));
                }
            }
        } else if (event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            // Configure loot table
            Block clickedBlock = event.getClickedBlock();
            if (clickedBlock != null && clickedBlock.getType() == Material.CHEST) {
                // TODO: Open loot table configuration
                player.sendMessage(Component.text("Loot table configuration coming soon!").color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            } else {
                player.sendMessage(Component.text("Right-click a chest to configure its loot table.").color(NamedTextColor.YELLOW));
            }
        }
    }
    
    private void handleGateWand(@NotNull PlayerInteractEvent event, @NotNull Player player) {
        event.setCancelled(true);
        
        if (event.getAction() == Action.LEFT_CLICK_BLOCK) {
            // Place gate/door
            Block clickedBlock = event.getClickedBlock();
            if (clickedBlock != null) {
                Block targetBlock = clickedBlock.getRelative(event.getBlockFace());
                
                if (targetBlock.getType() == Material.AIR) {
                    targetBlock.setType(Material.IRON_BARS);
                    player.sendMessage(Component.text("Gate placed! Shift-click to configure behavior.").color(NamedTextColor.GREEN));
                    player.playSound(player.getLocation(), Sound.BLOCK_METAL_PLACE, 1.0f, 1.0f);
                } else {
                    player.sendMessage(Component.text("Cannot place gate here - block is not empty.").color(NamedTextColor.RED));
                }
            }
        } else if (event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            // Place trigger zone
            Block clickedBlock = event.getClickedBlock();
            if (clickedBlock != null) {
                Location triggerLoc = clickedBlock.getLocation().add(0.5, 1, 0.5);
                // TODO: Create trigger zone at location
                player.sendMessage(Component.text("Trigger zone placed at " + 
                    triggerLoc.getBlockX() + ", " + triggerLoc.getBlockY() + ", " + triggerLoc.getBlockZ()).color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.BLOCK_NOTE_BLOCK_CHIME, 1.0f, 1.5f);
            }
        } else if (player.isSneaking() && (event.getAction() == Action.LEFT_CLICK_BLOCK || event.getAction() == Action.RIGHT_CLICK_BLOCK)) {
            // Configure behavior
            player.sendMessage(Component.text("Gate/trigger configuration coming soon!").color(NamedTextColor.YELLOW));
            player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
        }
    }
    
    private void handleSelectionWand(@NotNull PlayerInteractEvent event, @NotNull Player player) {
        event.setCancelled(true);
        
        Block clickedBlock = event.getClickedBlock();
        if (clickedBlock == null) {
            return;
        }
        
        Location loc = clickedBlock.getLocation();
        
        if (event.getAction() == Action.LEFT_CLICK_BLOCK) {
            // Set position 1
            // TODO: Store position 1 for player
            player.sendMessage(Component.text("Position 1 set to " + 
                loc.getBlockX() + ", " + loc.getBlockY() + ", " + loc.getBlockZ()).color(NamedTextColor.GREEN));
            player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.0f);
        } else if (event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            // Set position 2
            // TODO: Store position 2 for player
            player.sendMessage(Component.text("Position 2 set to " + 
                loc.getBlockX() + ", " + loc.getBlockY() + ", " + loc.getBlockZ()).color(NamedTextColor.YELLOW));
            player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);
        }
    }
    
    private void handleInfoTool(@NotNull PlayerInteractEvent event, @NotNull Player player) {
        event.setCancelled(true);
        
        if (event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            Block clickedBlock = event.getClickedBlock();
            if (clickedBlock != null) {
                Material blockType = clickedBlock.getType();
                Location loc = clickedBlock.getLocation();
                
                player.sendMessage(Component.text("=== Block Info ===").color(NamedTextColor.GOLD));
                player.sendMessage(Component.text("Type: " + blockType.name()).color(NamedTextColor.YELLOW));
                player.sendMessage(Component.text("Location: " + loc.getBlockX() + ", " + loc.getBlockY() + ", " + loc.getBlockZ()).color(NamedTextColor.GRAY));
                
                if (blockType == Material.SPAWNER) {
                    player.sendMessage(Component.text("Spawner Type: ZOMBIE (placeholder)").color(NamedTextColor.AQUA));
                    player.sendMessage(Component.text("Spawn Count: 3").color(NamedTextColor.AQUA));
                    player.sendMessage(Component.text("Trigger: ON_ENTER").color(NamedTextColor.AQUA));
                } else if (blockType == Material.CHEST) {
                    player.sendMessage(Component.text("Loot Table: default_chest").color(NamedTextColor.AQUA));
                    player.sendMessage(Component.text("Rarity: Common").color(NamedTextColor.AQUA));
                }
                
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        }
    }
    
    private void openMobSelectorMenu(@NotNull Player player) {
        // TODO: Create a proper mob selector menu
        player.sendMessage(Component.text("=== Mob Selector ===").color(NamedTextColor.GOLD));
        player.sendMessage(Component.text("Available mobs:").color(NamedTextColor.YELLOW));
        player.sendMessage(Component.text("• ZOMBIE (currently selected)").color(NamedTextColor.GREEN));
        player.sendMessage(Component.text("• SKELETON").color(NamedTextColor.WHITE));
        player.sendMessage(Component.text("• SPIDER").color(NamedTextColor.WHITE));
        player.sendMessage(Component.text("• CREEPER").color(NamedTextColor.WHITE));
        player.sendMessage(Component.text("• ENDERMAN").color(NamedTextColor.WHITE));
        player.sendMessage(Component.text("Mob selector GUI coming soon!").color(NamedTextColor.GRAY));
        player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
    }
}

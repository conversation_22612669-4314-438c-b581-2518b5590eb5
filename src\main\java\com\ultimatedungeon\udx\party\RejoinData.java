package com.ultimatedungeon.udx.party;

import org.jetbrains.annotations.NotNull;

import java.util.UUID;

/**
 * Represents rejoin data for a disconnected player.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public record RejoinData(
    @NotNull UUID playerId,
    @NotNull UUID partyId,
    @NotNull UUID instanceId,
    long disconnectTime,
    long graceExpiresAt,
    @NotNull RejoinStatus status
) {
    
    /**
     * Creates new rejoin data.
     * 
     * @param playerId the player ID
     * @param partyId the party ID
     * @param instanceId the instance ID
     * @param gracePeriodMs grace period in milliseconds
     * @return new rejoin data
     */
    @NotNull
    public static RejoinData create(@NotNull UUID playerId, @NotNull UUID partyId, 
                                  @NotNull UUID instanceId, long gracePeriodMs) {
        long now = System.currentTimeMillis();
        return new RejoinData(
            playerId,
            partyId,
            instanceId,
            now,
            now + gracePeriodMs,
            RejoinStatus.AVAILABLE
        );
    }
    
    /**
     * Creates a copy with updated status.
     * 
     * @param newStatus the new status
     * @return updated rejoin data
     */
    @NotNull
    public RejoinData withStatus(@NotNull RejoinStatus newStatus) {
        return new RejoinData(playerId, partyId, instanceId, disconnectTime, graceExpiresAt, newStatus);
    }
    
    /**
     * Checks if the grace period has expired.
     * 
     * @return true if expired
     */
    public boolean isExpired() {
        return System.currentTimeMillis() > graceExpiresAt;
    }
    
    /**
     * Gets remaining grace time in milliseconds.
     * 
     * @return remaining time, or 0 if expired
     */
    public long getRemainingGraceTime() {
        long remaining = graceExpiresAt - System.currentTimeMillis();
        return Math.max(0, remaining);
    }
    
    /**
     * Gets the time since disconnect in milliseconds.
     * 
     * @return time since disconnect
     */
    public long getDisconnectDuration() {
        return System.currentTimeMillis() - disconnectTime;
    }
    
    /**
     * Checks if rejoin is currently available.
     * 
     * @return true if rejoin is available
     */
    public boolean canRejoin() {
        return status == RejoinStatus.AVAILABLE && !isExpired();
    }
    
    /**
     * Rejoin status enumeration.
     */
    public enum RejoinStatus {
        /** Rejoin is available within grace period */
        AVAILABLE,
        /** Player has rejoined successfully */
        REJOINED,
        /** Grace period expired */
        EXPIRED,
        /** Rejoin was cancelled (party disbanded, instance ended, etc.) */
        CANCELLED,
        /** Rejoin failed due to error */
        FAILED
    }
}

package com.ultimatedungeon.udx.spawner.behavior;

import org.bukkit.entity.LivingEntity;
import org.jetbrains.annotations.NotNull;

import java.util.Map;

/**
 * Base interface for mob AI behaviors.
 * 
 * <p>Behaviors define how mobs act and react in different situations.
 * They can be combined to create complex AI patterns.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public interface MobBehavior {
    
    /**
     * Gets the behavior ID.
     * 
     * @return the unique behavior identifier
     */
    @NotNull
    String getId();
    
    /**
     * Gets the behavior priority.
     * Higher priority behaviors are executed first.
     * 
     * @return the priority (0-100, higher = more important)
     */
    int getPriority();
    
    /**
     * Checks if this behavior can be applied to the entity.
     * 
     * @param entity the entity
     * @param parameters behavior parameters
     * @return true if applicable
     */
    boolean canApply(@NotNull LivingEntity entity, @NotNull Map<String, Object> parameters);
    
    /**
     * Applies the behavior to the entity.
     * 
     * @param entity the entity
     * @param parameters behavior parameters
     */
    void apply(@NotNull LivingEntity entity, @NotNull Map<String, Object> parameters);
    
    /**
     * Removes the behavior from the entity.
     * 
     * @param entity the entity
     */
    void remove(@NotNull LivingEntity entity);
    
    /**
     * Updates the behavior (called periodically).
     * 
     * @param entity the entity
     * @param parameters behavior parameters
     */
    default void update(@NotNull LivingEntity entity, @NotNull Map<String, Object> parameters) {
        // Default: no periodic updates
    }
    
    /**
     * Checks if the behavior is currently active.
     * 
     * @param entity the entity
     * @return true if active
     */
    default boolean isActive(@NotNull LivingEntity entity) {
        return true;
    }
    
    /**
     * Gets the behavior description.
     * 
     * @return human-readable description
     */
    @NotNull
    default String getDescription() {
        return "Mob behavior: " + getId();
    }
}

package com.ultimatedungeon.udx.api.loot;

import org.jetbrains.annotations.NotNull;
import java.util.List;

/**
 * Immutable representation of loot table data for API consumers.
 */
public record LootTableData(
    @NotNull String id,
    @NotNull String name,
    @NotNull String description,
    @NotNull List<LootEntryData> entries,
    double totalWeight,
    int minRolls,
    int maxRolls,
    boolean jackpotEnabled,
    double jackpotChance
) {
    public LootTableData {
        if (id == null || id.isBlank()) throw new IllegalArgumentException("ID cannot be null or blank");
        if (name == null || name.isBlank()) throw new IllegalArgumentException("Name cannot be null or blank");
        if (description == null) throw new IllegalArgumentException("Description cannot be null");
        if (entries == null) throw new IllegalArgumentException("Entries cannot be null");
        if (totalWeight < 0) throw new IllegalArgumentException("Total weight cannot be negative");
        if (minRolls < 0) throw new IllegalArgumentException("Min rolls cannot be negative");
        if (maxRolls < minRolls) throw new IllegalArgumentException("Max rolls must be >= min rolls");
        if (jackpotChance < 0 || jackpotChance > 1) throw new IllegalArgumentException("Jackpot chance must be between 0 and 1");
    }
}

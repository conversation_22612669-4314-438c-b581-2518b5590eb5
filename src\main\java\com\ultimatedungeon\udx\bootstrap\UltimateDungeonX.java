package com.ultimatedungeon.udx.bootstrap;

import com.ultimatedungeon.udx.config.ConfigService;
import com.ultimatedungeon.udx.data.DataService;
import com.ultimatedungeon.udx.command.CommandRegistry;
import com.ultimatedungeon.udx.gui.MenuRegistry;
import com.ultimatedungeon.udx.instance.InstanceManager;
import com.ultimatedungeon.udx.spawner.MobService;
import com.ultimatedungeon.udx.boss.BossService;
import com.ultimatedungeon.udx.combat.CombatService;
import com.ultimatedungeon.udx.loot.LootService;
import com.ultimatedungeon.udx.party.PartyService;
import com.ultimatedungeon.udx.progression.ProgressionService;
import com.ultimatedungeon.udx.affix.AffixService;
import com.ultimatedungeon.udx.gen.GenerationService;
import com.ultimatedungeon.udx.room.RoomService;
import com.ultimatedungeon.udx.paste.FastPaster;
import com.ultimatedungeon.udx.dungeon.DungeonService;
import com.ultimatedungeon.udx.portal.PortalKeystoneListener;
import com.ultimatedungeon.udx.tools.WandListener;
import com.ultimatedungeon.udx.util.SchedulerUtil;
import com.ultimatedungeon.udx.api.UDXApiProvider;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.Bukkit;
import org.bukkit.NamespacedKey;
import org.bukkit.plugin.ServicePriority;
import org.bukkit.plugin.java.JavaPlugin;
import org.jetbrains.annotations.NotNull;

import java.util.logging.Level;

/**
 * Main plugin class for UltimateDungeonX.
 * 
 * <p>This class handles the bootstrap process, service initialization,
 * and graceful shutdown of the plugin. It follows a dependency injection
 * pattern where services are initialized in the correct order and made
 * available to other components.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class UltimateDungeonX extends JavaPlugin {
    
    private static UltimateDungeonX instance;
    
    // Core Services
    private ConfigService configService;
    private DataService dataService;
    private SchedulerUtil schedulerUtil;
    
    // Game Services
    private InstanceManager instanceManager;
    private RoomService roomService;
    private FastPaster fastPaster;
    private GenerationService generationService;
    private MobService mobService;
    private BossService bossService;
    private CombatService combatService;
    private LootService lootService;
    private PartyService partyService;
    private ProgressionService progressionService;
    private AffixService affixService;
    private DungeonService dungeonService;
    
    // UI Services
    private MenuRegistry menuRegistry;
    private CommandRegistry commandRegistry;
    
    // API
    private UDXApiProvider apiProvider;
    
    @Override
    public void onLoad() {
        instance = this;
        
        getLogger().info("Loading UltimateDungeonX v" + getPluginMeta().getVersion());
        
        // Pre-load validation
        if (!validateEnvironment()) {
            getLogger().severe("Environment validation failed! Plugin will not load.");
            setEnabled(false);
            return;
        }
        
        getLogger().info("Environment validation passed");
    }
    
    @Override
    public void onEnable() {
        long startTime = System.currentTimeMillis();
        
        try {
            // Initialize core services first
            initializeCoreServices();
            
            // Initialize game services
            initializeGameServices();
            
            // Initialize UI services
            initializeUIServices();
            
            // Initialize API
            initializeAPI();
            
            // Register listeners and commands
            registerListeners();
            registerCommands();
            
            // Post-initialization tasks
            postInitialization();
            
            long loadTime = System.currentTimeMillis() - startTime;
            getLogger().info(String.format("UltimateDungeonX enabled successfully in %dms", loadTime));
            
            // Send startup message to console
            Bukkit.getConsoleSender().sendMessage(
                Component.text("UltimateDungeonX v" + getPluginMeta().getVersion() + " is now active!")
                    .color(NamedTextColor.GREEN)
            );
            
        } catch (Exception e) {
            getLogger().log(Level.SEVERE, "Failed to enable UltimateDungeonX", e);
            setEnabled(false);
        }
    }
    
    @Override
    public void onDisable() {
        getLogger().info("Disabling UltimateDungeonX...");
        
        try {
            // Graceful shutdown in reverse order
            shutdownAPI();
            shutdownUIServices();
            shutdownGameServices();
            shutdownCoreServices();
            
            getLogger().info("UltimateDungeonX disabled successfully");
            
        } catch (Exception e) {
            getLogger().log(Level.SEVERE, "Error during plugin shutdown", e);
        } finally {
            instance = null;
        }
    }
    
    /**
     * Validates the server environment for compatibility.
     * 
     * @return true if environment is valid, false otherwise
     */
    private boolean validateEnvironment() {
        // Check Java version
        String javaVersion = System.getProperty("java.version");
        if (!javaVersion.startsWith("21")) {
            getLogger().warning("Java 21 is recommended. Current version: " + javaVersion);
        }
        
        // Check Paper API
        try {
            Class.forName("io.papermc.paper.event.player.AsyncChatEvent");
        } catch (ClassNotFoundException e) {
            getLogger().severe("Paper API not found! This plugin requires Paper 1.21.x");
            return false;
        }
        
        // Check server version
        String version = Bukkit.getVersion();
        if (!version.contains("1.21")) {
            getLogger().warning("This plugin is designed for Minecraft 1.21.x. Current version: " + version);
        }
        
        return true;
    }
    
    /**
     * Initializes core services that other services depend on.
     */
    private void initializeCoreServices() {
        getLogger().info("Initializing core services...");
        
        // Initialize scheduler first
        schedulerUtil = new SchedulerUtil(this);
        
        // Initialize and load configuration
        configService = new ConfigService(this);
        configService.loadConfig();
        
        // Now initialize data service with loaded config
        dataService = new DataService(this, configService);
        dataService.initialize();
        
        getLogger().info("Core services initialized");
    }
    
    /**
     * Initializes game-related services.
     */
    private void initializeGameServices() {
        getLogger().info("Initializing game services...");
        
        fastPaster = new FastPaster(this, schedulerUtil);
        roomService = new RoomService(this, configService, fastPaster);
        instanceManager = new InstanceManager(this, configService, schedulerUtil);
        generationService = new GenerationService(this, roomService, configService);
        combatService = new CombatService(this, schedulerUtil);
        lootService = new LootService(this, schedulerUtil);
        mobService = new MobService(this, configService, schedulerUtil);
        bossService = new BossService(this, mobService, schedulerUtil);
        partyService = new PartyService(this, dataService, schedulerUtil, instanceManager);
        
        // Initialize progression service
        progressionService = new ProgressionService(this, dataService, schedulerUtil);
        getLogger().info("ProgressionService initialized");
        
        // Initialize affix service
        affixService = new AffixService(this, configService, schedulerUtil);
        getLogger().info("AffixService initialized");
        
        dungeonService = new DungeonService(this, configService, instanceManager, generationService);
        
        getLogger().info("Game services initialized");
    }
    
    /**
     * Initializes UI-related services.
     */
    private void initializeUIServices() {
        getLogger().info("Initializing UI services...");
        
        menuRegistry = new MenuRegistry(this);
        commandRegistry = new CommandRegistry(this, menuRegistry);
        
        getLogger().info("UI services initialized");
    }
    
    /**
     * Initializes the public API.
     */
    private void initializeAPI() {
        getLogger().info("Initializing API...");
        
        apiProvider = new UDXApiProvider(
            dungeonService,
            partyService,
            instanceManager,
            mobService,
            bossService
        );
        
        // Register API with Bukkit's service manager
        Bukkit.getServicesManager().register(
            UDXApiProvider.class,
            apiProvider,
            this,
            ServicePriority.Normal
        );
        
        getLogger().info("API initialized");
    }
    
    /**
     * Registers event listeners.
     */
    private void registerListeners() {
        getLogger().info("Registering event listeners...");
        
        // Register service listeners
        if (instanceManager != null) {
            Bukkit.getPluginManager().registerEvents(instanceManager, this);
        }
        if (menuRegistry != null) {
            Bukkit.getPluginManager().registerEvents(menuRegistry, this);
        }
        if (partyService != null) {
            Bukkit.getPluginManager().registerEvents(partyService, this);
        }
        if (mobService != null) {
            Bukkit.getPluginManager().registerEvents(mobService, this);
        }
        if (bossService != null) {
            Bukkit.getPluginManager().registerEvents(bossService, this);
        }
        if (combatService != null) {
            Bukkit.getPluginManager().registerEvents(combatService, this);
        }
        
        // Register portal keystone listener
        Bukkit.getPluginManager().registerEvents(new PortalKeystoneListener(this), this);

        // Register wand listener for builder tools
        Bukkit.getPluginManager().registerEvents(new WandListener(this), this);

        getLogger().info("Event listeners registered");
    }
    
    /**
     * Registers commands.
     */
    private void registerCommands() {
        getLogger().info("Registering commands...");
        
        if (commandRegistry != null) {
            commandRegistry.registerCommands();
        }
        
        getLogger().info("Commands registered");
    }
    
    /**
     * Performs post-initialization tasks.
     */
    private void postInitialization() {
        getLogger().info("Running post-initialization tasks...");
        
        // Load sample dungeons if first run
        if (configService.isFirstRun()) {
            getLogger().info("First run detected, loading sample content...");
            dungeonService.loadSampleDungeons();
            configService.setFirstRun(false);
        }
        
        // Start background tasks
        schedulerUtil.runTaskTimerAsynchronously(() -> {
            // Periodic cleanup and maintenance
            instanceManager.performMaintenance();
            dataService.performMaintenance();
        }, 20L * 60L, 20L * 60L); // Every minute
        
        getLogger().info("Post-initialization complete");
    }
    
    /**
     * Shuts down core services.
     */
    private void shutdownCoreServices() {
        if (dataService != null) {
            dataService.shutdown();
        }
        if (schedulerUtil != null) {
            schedulerUtil.shutdown();
        }
    }
    
    /**
     * Shuts down game services.
     */
    private void shutdownGameServices() {
        if (instanceManager != null) {
            instanceManager.shutdown();
        }
        
        // Shutdown progression service
        if (progressionService != null) {
            progressionService.shutdown();
        }
        
        // Shutdown affix service
        if (affixService != null) {
            affixService.shutdown();
        }
        
        if (partyService != null) {
            partyService.shutdown();
        }
        if (mobService != null) {
            mobService.shutdown();
        }
        if (bossService != null) {
            bossService.shutdown();
        }
        if (combatService != null) {
            combatService.shutdown();
        }
        
        if (lootService != null) {
            lootService.shutdown();
        }
    }
    
    /**
     * Shuts down UI services.
     */
    private void shutdownUIServices() {
        if (menuRegistry != null) {
            menuRegistry.shutdown();
        }
    }
    
    /**
     * Shuts down API.
     */
    private void shutdownAPI() {
        if (apiProvider != null) {
            Bukkit.getServicesManager().unregister(UDXApiProvider.class, apiProvider);
        }
    }
    
    /**
     * Creates a NamespacedKey for this plugin.
     * 
     * @param key the key name
     * @return the NamespacedKey
     */
    @NotNull
    public NamespacedKey getNamespacedKey(@NotNull String key) {
        return new NamespacedKey(this, key);
    }
    
    // Getters for services
    
    @NotNull
    public static UltimateDungeonX getInstance() {
        if (instance == null) {
            throw new IllegalStateException("Plugin not initialized");
        }
        return instance;
    }
    
    @NotNull
    public ConfigService getConfigService() {
        return configService;
    }
    
    @NotNull
    public DataService getDataService() {
        return dataService;
    }
    
    @NotNull
    public SchedulerUtil getSchedulerUtil() {
        return schedulerUtil;
    }
    
    @NotNull
    public InstanceManager getInstanceManager() {
        return instanceManager;
    }
    
    @NotNull
    public RoomService getRoomService() {
        return roomService;
    }
    
    @NotNull
    public FastPaster getFastPaster() {
        return fastPaster;
    }
    
    @NotNull
    public GenerationService getGenerationService() {
        return generationService;
    }
    
    @NotNull
    public MobService getMobService() {
        return mobService;
    }
    
    @NotNull
    public BossService getBossService() {
        return bossService;
    }
    
    public CombatService getCombatService() {
        return combatService;
    }
    
    public LootService getLootService() {
        return lootService;
    }
    
    @NotNull
    public PartyService getPartyService() {
        return partyService;
    }
    
    public ProgressionService getProgressionService() {
        return progressionService;
    }
    
    @NotNull
    public AffixService getAffixService() {
        return affixService;
    }
    
    @NotNull
    public DungeonService getDungeonService() {
        return dungeonService;
    }
    
    @NotNull
    public MenuRegistry getMenuRegistry() {
        return menuRegistry;
    }
    
    @NotNull
    public CommandRegistry getCommandRegistry() {
        return commandRegistry;
    }
    
    @NotNull
    public UDXApiProvider getApiProvider() {
        return apiProvider;
    }
}

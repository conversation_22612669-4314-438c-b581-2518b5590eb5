package com.ultimatedungeon.udx.gui.menus;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.gui.Menu;
import com.ultimatedungeon.udx.util.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

/**
 * Loot editor for creating rewarding loot systems.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class LootEditorMenu extends Menu {
    
    private final UltimateDungeonX plugin;
    private String selectedRarity = "COMMON";
    private String selectedTableType = "CHEST";
    private int dropChance = 75;
    private boolean perPlayerLoot = false;
    
    public LootEditorMenu(@NotNull UltimateDungeonX plugin, @NotNull Player player) {
        super(player, Component.text("Loot Editor").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true), 
              54, plugin.getMenuRegistry());
        this.plugin = plugin;
    }
    
    @Override
    protected void setupMenu() {
        inventory.clear();
        clickHandlers.clear();
        
        // Header
        ItemStack headerItem = new ItemBuilder(Material.CHEST)
            .name(Component.text("Loot Table Designer").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Create rewarding loot systems").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Design balanced loot tables").color(NamedTextColor.YELLOW),
                Component.text("with exciting rewards and rarities").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(4, headerItem, clickType -> {
            // No action for header
        });
        
        // Loot Table Configuration
        setupLootTableConfiguration();
        
        // Item Management
        setupItemManagement();
        
        // Rarity & Weights
        setupRarityAndWeights();
        
        // Advanced Settings
        setupAdvancedSettings();
        
        // Testing & Actions
        setupTestingAndActions();
        
        // Back button
        ItemStack backItem = new ItemBuilder(Material.ARROW)
            .name(Component.text("Back to Editor Hub").color(NamedTextColor.GRAY))
            .lore(Component.text("Click to return").color(NamedTextColor.DARK_GRAY))
            .build();
        
        setItem(49, backItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                plugin.getMenuRegistry().openEditorHub(player);
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        fillEmptySlots();
    }
    
    private void setupLootTableConfiguration() {
        // Table Type
        Material tableMaterial = switch (selectedTableType) {
            case "CHEST" -> Material.CHEST;
            case "MOB_DROP" -> Material.ZOMBIE_HEAD;
            case "BOSS_REWARD" -> Material.DRAGON_HEAD;
            case "COMPLETION" -> Material.EXPERIENCE_BOTTLE;
            case "JACKPOT" -> Material.NETHER_STAR;
            default -> Material.CHEST;
        };
        
        ItemStack tableTypeItem = new ItemBuilder(tableMaterial)
            .name(Component.text("Table Type: " + selectedTableType).color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Select the loot table purpose").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Available table types:").color(NamedTextColor.YELLOW),
                Component.text("• CHEST - Treasure chests").color(selectedTableType.equals("CHEST") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• MOB_DROP - Mob death drops").color(selectedTableType.equals("MOB_DROP") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• BOSS_REWARD - Boss kill rewards").color(selectedTableType.equals("BOSS_REWARD") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• COMPLETION - Dungeon completion").color(selectedTableType.equals("COMPLETION") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• JACKPOT - Rare bonus rewards").color(selectedTableType.equals("JACKPOT") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to cycle table types").color(NamedTextColor.AQUA)
            )
            .glow()
            .build();
        
        setItem(10, tableTypeItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                selectedTableType = switch (selectedTableType) {
                    case "CHEST" -> "MOB_DROP";
                    case "MOB_DROP" -> "BOSS_REWARD";
                    case "BOSS_REWARD" -> "COMPLETION";
                    case "COMPLETION" -> "JACKPOT";
                    default -> "CHEST";
                };
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
        
        // Drop Chance
        ItemStack chanceItem = new ItemBuilder(Material.REDSTONE)
            .name(Component.text("Drop Chance: " + dropChance + "%").color(NamedTextColor.RED))
            .lore(
                Component.text("Probability of loot generation").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Current chance: " + dropChance + "%").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Left-click: +5%").color(NamedTextColor.GREEN),
                Component.text("Right-click: -5%").color(NamedTextColor.RED),
                Component.text("Shift-click: +25%").color(NamedTextColor.AQUA)
            )
            .amount(Math.max(1, Math.min(dropChance / 5, 64)))
            .build();
        
        setItem(11, chanceItem, clickType -> {
            switch (clickType) {
                case LEFT -> dropChance = Math.min(dropChance + 5, 100);
                case RIGHT -> dropChance = Math.max(dropChance - 5, 0);
                case SHIFT_LEFT -> dropChance = Math.min(dropChance + 25, 100);
            }
            player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            refresh();
        });
        
        // Loot Distribution
        ItemStack distributionItem = new ItemBuilder(perPlayerLoot ? Material.PLAYER_HEAD : Material.CHEST)
            .name(Component.text("Loot Distribution").color(NamedTextColor.BLUE))
            .lore(
                Component.text("How loot is distributed to players").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Current mode: " + (perPlayerLoot ? "Per-Player" : "Shared")).color(NamedTextColor.YELLOW),
                Component.empty(),
                perPlayerLoot ?
                    Component.text("Each player gets individual loot").color(NamedTextColor.GREEN) :
                    Component.text("All players share the same loot").color(NamedTextColor.AQUA),
                Component.empty(),
                Component.text("Click to toggle distribution").color(NamedTextColor.AQUA)
            )
            .glow(perPlayerLoot)
            .build();
        
        setItem(12, distributionItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                perPlayerLoot = !perPlayerLoot;
                player.sendMessage(Component.text("Loot distribution set to: " + (perPlayerLoot ? "Per-Player" : "Shared"))
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
    }
    
    private void setupItemManagement() {
        // Current Items Display
        ItemStack itemsHeaderItem = new ItemBuilder(Material.ITEM_FRAME)
            .name(Component.text("Loot Table Items").color(NamedTextColor.AQUA).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Items in this loot table").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Total items: 8").color(NamedTextColor.YELLOW),
                Component.text("Total weight: 100").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Click to view all items").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(19, itemsHeaderItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Opening item list...")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                // TODO: Open item list submenu
            }
        });
        
        // Sample Items (showing current table contents)
        setupSampleItems();
        
        // Add Item
        ItemStack addItemItem = new ItemBuilder(Material.LIME_DYE)
            .name(Component.text("Add Item").color(NamedTextColor.GREEN))
            .lore(
                Component.text("Add a new item to the loot table").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to add item").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(25, addItemItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Opening item selector...")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                // TODO: Open item selector submenu
            }
        });
    }
    
    private void setupSampleItems() {
        // Sample items showing what's in the current loot table
        ItemStack[] sampleItems = {
            new ItemBuilder(Material.IRON_SWORD)
                .name(Component.text("Iron Blade").color(NamedTextColor.WHITE))
                .lore(
                    Component.text("Weight: 25").color(NamedTextColor.GRAY),
                    Component.text("Rarity: Common").color(NamedTextColor.WHITE)
                )
                .build(),
            
            new ItemBuilder(Material.GOLDEN_APPLE)
                .name(Component.text("Healing Apple").color(NamedTextColor.YELLOW))
                .lore(
                    Component.text("Weight: 15").color(NamedTextColor.GRAY),
                    Component.text("Rarity: Uncommon").color(NamedTextColor.GREEN)
                )
                .build(),
            
            new ItemBuilder(Material.DIAMOND)
                .name(Component.text("Precious Gem").color(NamedTextColor.AQUA))
                .lore(
                    Component.text("Weight: 8").color(NamedTextColor.GRAY),
                    Component.text("Rarity: Rare").color(NamedTextColor.BLUE)
                )
                .build()
        };
        
        int[] itemSlots = {20, 21, 22};
        
        for (int i = 0; i < Math.min(sampleItems.length, itemSlots.length); i++) {
            final int index = i;
            setItem(itemSlots[i], sampleItems[i], clickType -> {
                if (clickType == ClickType.LEFT) {
                    player.sendMessage(Component.text("Editing item " + (index + 1) + "...")
                        .color(NamedTextColor.YELLOW));
                    player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                }
            });
        }
    }
    
    private void setupRarityAndWeights() {
        // Rarity Selection
        NamedTextColor rarityColor = switch (selectedRarity) {
            case "COMMON" -> NamedTextColor.WHITE;
            case "UNCOMMON" -> NamedTextColor.GREEN;
            case "RARE" -> NamedTextColor.BLUE;
            case "EPIC" -> NamedTextColor.LIGHT_PURPLE;
            case "LEGENDARY" -> NamedTextColor.GOLD;
            default -> NamedTextColor.WHITE;
        };
        
        Material rarityMaterial = switch (selectedRarity) {
            case "COMMON" -> Material.COBBLESTONE;
            case "UNCOMMON" -> Material.IRON_INGOT;
            case "RARE" -> Material.GOLD_INGOT;
            case "EPIC" -> Material.DIAMOND;
            case "LEGENDARY" -> Material.NETHERITE_INGOT;
            default -> Material.COBBLESTONE;
        };
        
        ItemStack rarityItem = new ItemBuilder(rarityMaterial)
            .name(Component.text("Rarity: " + selectedRarity).color(rarityColor).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Configure item rarity tiers").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to cycle rarities").color(NamedTextColor.AQUA)
            )
            .glow(selectedRarity.equals("LEGENDARY"))
            .build();
        
        setItem(28, rarityItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                selectedRarity = switch (selectedRarity) {
                    case "COMMON" -> "UNCOMMON";
                    case "UNCOMMON" -> "RARE";
                    case "RARE" -> "EPIC";
                    case "EPIC" -> "LEGENDARY";
                    default -> "COMMON";
                };
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
        
        // Weight Distribution
        ItemStack weightsItem = new ItemBuilder(Material.COMPARATOR)
            .name(Component.text("Weight Distribution").color(NamedTextColor.YELLOW))
            .lore(
                Component.text("Current loot table weights").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Total weight: 100").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Click to auto-balance weights").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(29, weightsItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Loot table weights auto-balanced!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);
                refresh();
            }
        });
    }
    
    private void setupAdvancedSettings() {
        // Chest Regeneration
        ItemStack regenItem = new ItemBuilder(Material.HOPPER)
            .name(Component.text("Chest Regeneration").color(NamedTextColor.BLUE))
            .lore(
                Component.text("Configure chest refill behavior").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to configure regeneration").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(37, regenItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Chest regeneration configured!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                refresh();
            }
        });
    }
    
    private void setupTestingAndActions() {
        // Test Loot Generation
        ItemStack testItem = new ItemBuilder(Material.FISHING_ROD)
            .name(Component.text("Test Loot Generation").color(NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Generate test loot from this table").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Left-click: Single test").color(NamedTextColor.GREEN),
                Component.text("Right-click: Bulk test").color(NamedTextColor.BLUE)
            )
            .glow()
            .build();
        
        setItem(46, testItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Rolling loot table...")
                    .color(NamedTextColor.YELLOW));
                player.sendMessage(Component.text("✓ Generated: 2x Iron Sword, 1x Golden Apple")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);
            } else if (clickType == ClickType.RIGHT) {
                player.sendMessage(Component.text("Running 100 loot simulations...")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        // Save Loot Table
        ItemStack saveItem = new ItemBuilder(Material.WRITABLE_BOOK)
            .name(Component.text("Save Loot Table").color(NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Save current loot table configuration").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to save loot table").color(NamedTextColor.AQUA)
            )
            .glow()
            .build();
        
        setItem(47, saveItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Saving loot table configuration...")
                    .color(NamedTextColor.YELLOW));
                player.sendMessage(Component.text("✓ Loot table saved as 'treasure_chest.json'")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);
            }
        });
        
        // Load Loot Table
        ItemStack loadItem = new ItemBuilder(Material.BOOK)
            .name(Component.text("Load Loot Table").color(NamedTextColor.BLUE))
            .lore(
                Component.text("Load existing loot table configuration").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to browse tables").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(48, loadItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Opening loot table library...")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                // TODO: Open loot table browser menu
            }
        });
    }
}

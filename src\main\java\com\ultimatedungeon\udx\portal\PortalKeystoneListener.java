package com.ultimatedungeon.udx.portal;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.jetbrains.annotations.NotNull;

/**
 * Handles Portal Keystone interactions.
 * 
 * <p>This listener detects when players right-click with a Portal Keystone
 * and opens the dungeon browser for them.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class PortalKeystoneListener implements Listener {
    
    private final UltimateDungeonX plugin;
    
    public PortalKeystoneListener(@NotNull UltimateDungeonX plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onPlayerInteract(@NotNull PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        
        // Check if player is right-clicking with an item
        if (!event.getAction().isRightClick() || item == null) {
            return;
        }
        
        // Check if the item is a Portal Keystone
        if (!isPortalKeystone(item)) {
            return;
        }
        
        // Cancel the event to prevent other interactions
        event.setCancelled(true);
        
        // Check permissions
        if (!player.hasPermission("udx.use")) {
            player.sendMessage(Component.text("You don't have permission to use Portal Keystones.")
                .color(NamedTextColor.RED));
            return;
        }
        
        // Open dungeon browser
        try {
            plugin.getMenuRegistry().openDungeonBrowser(player);
            player.playSound(player.getLocation(), Sound.BLOCK_PORTAL_AMBIENT, 0.5f, 1.2f);
            
            // Send feedback message
            player.sendMessage(Component.text("Portal activated! Choose your dungeon.")
                .color(NamedTextColor.LIGHT_PURPLE));
                
        } catch (Exception e) {
            plugin.getLogger().severe("Error opening dungeon browser via portal keystone for " + player.getName() + ": " + e.getMessage());
            player.sendMessage(Component.text("Portal malfunction! Please try again.")
                .color(NamedTextColor.RED));
        }
    }
    
    /**
     * Checks if an item is a Portal Keystone.
     */
    private boolean isPortalKeystone(@NotNull ItemStack item) {
        // Check material
        if (item.getType() != Material.NETHER_STAR) {
            return false;
        }
        
        // Check display name
        ItemMeta meta = item.getItemMeta();
        if (meta == null || !meta.hasDisplayName()) {
            return false;
        }
        
        Component displayName = meta.displayName();
        if (displayName == null) {
            return false;
        }
        
        // Check if the display name contains "Portal Keystone"
        String plainText = Component.text().append(displayName).build().toString();
        return plainText.contains("Portal Keystone");
    }
}

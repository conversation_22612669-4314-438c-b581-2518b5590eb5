package com.ultimatedungeon.udx.api;

import com.ultimatedungeon.udx.boss.BossService;
import com.ultimatedungeon.udx.dungeon.DungeonService;
import com.ultimatedungeon.udx.instance.InstanceManager;
import com.ultimatedungeon.udx.party.PartyService;
import com.ultimatedungeon.udx.spawner.MobService;
import org.jetbrains.annotations.NotNull;

/**
 * Main API provider for UltimateDungeonX addon development.
 * 
 * <p>This class provides a stable API surface for third-party addons
 * to integrate with UltimateDungeonX functionality.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class UDXApiProvider {
    
    private final DungeonService dungeonService;
    private final PartyService partyService;
    private final InstanceManager instanceManager;
    private final MobService mobService;
    private final BossService bossService;
    
    public UDXApiProvider(@NotNull DungeonService dungeonService, @NotNull PartyService partyService,
                         @NotNull InstanceManager instanceManager, @NotNull MobService mobService,
                         @NotNull BossService bossService) {
        this.dungeonService = dungeonService;
        this.partyService = partyService;
        this.instanceManager = instanceManager;
        this.mobService = mobService;
        this.bossService = bossService;
    }
    
    /**
     * Gets the dungeon service.
     * 
     * @return the dungeon service
     */
    @NotNull
    public DungeonService getDungeonService() {
        return dungeonService;
    }
    
    /**
     * Gets the party service.
     * 
     * @return the party service
     */
    @NotNull
    public PartyService getPartyService() {
        return partyService;
    }
    
    /**
     * Gets the instance manager.
     * 
     * @return the instance manager
     */
    @NotNull
    public InstanceManager getInstanceManager() {
        return instanceManager;
    }
    
    /**
     * Gets the mob service.
     * 
     * @return the mob service
     */
    @NotNull
    public MobService getMobService() {
        return mobService;
    }
    
    /**
     * Gets the boss service.
     * 
     * @return the boss service
     */
    @NotNull
    public BossService getBossService() {
        return bossService;
    }
}

# Crypt of Echoes - Sample Dungeon Configuration
# A dark, underground crypt filled with undead creatures and ancient secrets

id: "crypt_of_echoes"
name: "Crypt of Echoes"
description: "A haunted crypt where the dead refuse to rest. Ancient whispers echo through the corridors, and skeletal guardians protect forgotten treasures."

# Basic Properties
theme: "undead"
environment: "underground"
biome: "DEEP_DARK"
difficulty_base: 3
length_min: 5
length_max: 8
branching_factor: 1

# Level Requirements
level_requirement:
  min: 10
  max: 50
  recommended: 20

# Party Configuration
party:
  min_size: 1
  max_size: 4
  recommended_size: 3
  scaling_enabled: true

# Room Configuration
rooms:
  start_room: "crypt_entrance"
  boss_room: "lich_chamber"
  available_rooms:
    - "skeleton_corridor"
    - "zombie_pit"
    - "spider_nest"
    - "treasure_vault"
    - "ritual_chamber"
    - "bone_bridge"

# Difficulty Scaling
difficulty_tiers:
  normal:
    mob_health_multiplier: 1.0
    mob_damage_multiplier: 1.0
    loot_multiplier: 1.0
    experience_multiplier: 1.0
  hard:
    mob_health_multiplier: 1.5
    mob_damage_multiplier: 1.3
    loot_multiplier: 1.2
    experience_multiplier: 1.3
  mythic:
    mob_health_multiplier: 2.0
    mob_damage_multiplier: 1.6
    loot_multiplier: 1.5
    experience_multiplier: 1.6
  nightmare:
    mob_health_multiplier: 3.0
    mob_damage_multiplier: 2.0
    loot_multiplier: 2.0
    experience_multiplier: 2.0

# Allowed Affixes
allowed_affixes:
  - "undead_resilience"
  - "bone_armor"
  - "necrotic_aura"
  - "shadow_step"
  - "life_drain"
  - "cursed_ground"
  - "spectral_minions"
  - "death_explosion"

# Loot Configuration
loot_tables:
  common_chest: "crypt_common_loot"
  rare_chest: "crypt_rare_loot"
  boss_loot: "lich_king_loot"
  completion_rewards: "crypt_completion"

# Objectives
objectives:
  primary:
    - type: "kill_boss"
      target: "lich_king"
      description: "Defeat the Lich King"
  secondary:
    - type: "collect_items"
      target: "ancient_relic"
      count: 3
      description: "Collect 3 Ancient Relics"
    - type: "survive_time"
      duration: 1200 # 20 minutes
      description: "Complete within 20 minutes"

# Environmental Effects
environment:
  lighting: "dim"
  fog_enabled: true
  fog_color: "#2C1810"
  ambient_sounds:
    - "AMBIENT_CAVE"
    - "ENTITY_SKELETON_AMBIENT"
  particle_effects:
    - type: "SMOKE_NORMAL"
      density: "low"
      areas: ["corridors"]
    - type: "SPELL_WITCH"
      density: "medium"
      areas: ["ritual_chamber"]

# Checkpoints
checkpoints:
  - name: "entrance_cleared"
    description: "Entrance Hall Cleared"
    position: "after_start_room"
  - name: "mid_point"
    description: "Halfway Through"
    position: "middle_rooms"
  - name: "boss_chamber"
    description: "Boss Chamber Reached"
    position: "before_boss"

# Rewards
rewards:
  currency:
    base_amount: 100
    bonus_per_difficulty: 50
  experience:
    base_amount: 500
    bonus_per_difficulty: 200
  titles:
    - condition: "first_completion"
      title: "Crypt Walker"
    - condition: "nightmare_completion"
      title: "Undead Slayer"
  achievements:
    - "crypt_explorer"
    - "bone_collector"
    - "lich_slayer"

# Metadata
version: "1.0.0"
author: "UltimateDungeon Team"
created: "2024-01-01"
last_modified: "2024-01-01"

package com.ultimatedungeon.udx.combat;

import org.jetbrains.annotations.NotNull;

/**
 * Enumeration of damage types in the dungeon system.
 * 
 * <p>Different damage types can have different resistances and effects.
 * This allows for complex combat mechanics and strategic gameplay.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public enum DamageType {
    
    /**
     * Physical damage from melee attacks.
     */
    PHYSICAL("Physical", "§7"),
    
    /**
     * Magical damage from spells and abilities.
     */
    MAGIC("Magic", "§9"),
    
    /**
     * Fire damage from flames and heat.
     */
    FIRE("Fire", "§c"),
    
    /**
     * Ice damage from cold and frost.
     */
    ICE("Ice", "§b"),
    
    /**
     * Lightning damage from electrical attacks.
     */
    LIGHTNING("Lightning", "§e"),
    
    /**
     * Poison damage from toxins and venom.
     */
    POISON("Poison", "§2"),
    
    /**
     * Necrotic damage from death magic.
     */
    NECROTIC("Necrotic", "§8"),
    
    /**
     * Radiant damage from holy magic.
     */
    RADIANT("Radiant", "§f"),
    
    /**
     * Psychic damage from mental attacks.
     */
    PSYCHIC("Psychic", "§d"),
    
    /**
     * True damage that ignores resistances.
     */
    TRUE("True", "§4");
    
    private final String displayName;
    private final String colorCode;
    
    DamageType(@NotNull String displayName, @NotNull String colorCode) {
        this.displayName = displayName;
        this.colorCode = colorCode;
    }
    
    /**
     * Gets the display name of this damage type.
     * 
     * @return the display name
     */
    @NotNull
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * Gets the color code for this damage type.
     * 
     * @return the color code
     */
    @NotNull
    public String getColorCode() {
        return colorCode;
    }
    
    /**
     * Gets the colored display name.
     * 
     * @return the colored display name
     */
    @NotNull
    public String getColoredName() {
        return colorCode + displayName;
    }
    
    /**
     * Checks if this damage type is elemental.
     * 
     * @return true if elemental, false otherwise
     */
    public boolean isElemental() {
        return this == FIRE || this == ICE || this == LIGHTNING;
    }
    
    /**
     * Checks if this damage type is magical.
     * 
     * @return true if magical, false otherwise
     */
    public boolean isMagical() {
        return this == MAGIC || this == NECROTIC || this == RADIANT || this == PSYCHIC || isElemental();
    }
    
    /**
     * Gets the opposite damage type (for resistances/weaknesses).
     * 
     * @return the opposite damage type, or null if none
     */
    @NotNull
    public DamageType getOpposite() {
        return switch (this) {
            case FIRE -> ICE;
            case ICE -> FIRE;
            case NECROTIC -> RADIANT;
            case RADIANT -> NECROTIC;
            default -> this;
        };
    }
}

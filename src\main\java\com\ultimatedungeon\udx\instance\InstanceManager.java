package com.ultimatedungeon.udx.instance;

import com.ultimatedungeon.udx.config.ConfigService;
import com.ultimatedungeon.udx.party.Party;
import com.ultimatedungeon.udx.util.SchedulerUtil;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.*;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.world.WorldUnloadEvent;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitTask;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.io.File;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;

/**
 * Manages dungeon instances and their lifecycle.
 * 
 * <p>This service handles creating, managing, and disposing of isolated
 * dungeon worlds with proper lifecycle management and resource cleanup.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class InstanceManager implements Listener {
    
    private final Plugin plugin;
    private final ConfigService configService;
    private final SchedulerUtil schedulerUtil;
    private final ProtectionService protectionService;
    
    private final Map<UUID, DungeonInstance> instances;
    private final Map<String, DungeonInstance> worldToInstance;
    private final VoidWorldGenerator voidGenerator;
    
    private BukkitTask maintenanceTask;
    private boolean shutdownInProgress = false;
    
    // Configuration
    private int maxInstances = 50;
    private long instanceTimeoutTicks = 20L * 60L * 30L; // 30 minutes
    private long maintenanceIntervalTicks = 20L * 60L; // 1 minute
    
    public InstanceManager(@NotNull Plugin plugin, @NotNull ConfigService configService, @NotNull SchedulerUtil schedulerUtil) {
        this.plugin = plugin;
        this.configService = configService;
        this.schedulerUtil = schedulerUtil;
        this.protectionService = new ProtectionService(plugin, this);
        
        this.instances = new ConcurrentHashMap<>();
        this.worldToInstance = new ConcurrentHashMap<>();
        this.voidGenerator = new VoidWorldGenerator();
        
        // Load configuration
        loadConfiguration();
        
        // Register protection service
        Bukkit.getPluginManager().registerEvents(protectionService, plugin);
        
        // Start maintenance task
        startMaintenanceTask();
        
        plugin.getLogger().info("Instance manager initialized");
    }
    
    /**
     * Loads configuration settings.
     */
    private void loadConfiguration() {
        // TODO: Load from config service
        this.maxInstances = 50;
        this.instanceTimeoutTicks = 20L * 60L * 30L;
        this.maintenanceIntervalTicks = 20L * 60L;
    }
    
    /**
     * Creates a new dungeon instance.
     * 
     * @param dungeonId the dungeon ID
     * @param party the party (can be null for solo)
     * @return future that completes with the created instance
     */
    @NotNull
    public CompletableFuture<DungeonInstance> createInstance(@NotNull String dungeonId, @Nullable Party party) {
        if (instances.size() >= maxInstances) {
            return CompletableFuture.failedFuture(new IllegalStateException("Maximum instances reached"));
        }
        
        DungeonInstance instance = new DungeonInstance(dungeonId);
        instance.setParty(party);
        instance.setTimeoutTicks(instanceTimeoutTicks);
        
        // Register instance
        instances.put(instance.getInstanceId(), instance);
        
        plugin.getLogger().info("Creating instance " + instance.getInstanceId() + " for dungeon " + dungeonId);
        
        // Create world asynchronously
        return CompletableFuture.supplyAsync(() -> {
            try {
                World world = createInstanceWorld(instance);
                instance.setWorld(world);
                
                // Set spawn location (will be updated when rooms are placed)
                Location spawn = new Location(world, 0, 100, 0);
                instance.setSpawnLocation(spawn);
                
                // Add world protection
                protectionService.addProtectedWorld(world.getName());
                worldToInstance.put(world.getName(), instance);
                
                // Transition to ready state
                instance.transitionTo(InstanceState.READY);
                
                plugin.getLogger().info("Instance " + instance.getInstanceId() + " created successfully");
                return instance;
                
            } catch (Exception e) {
                plugin.getLogger().log(Level.SEVERE, "Failed to create instance " + instance.getInstanceId(), e);
                
                // Clean up failed instance
                instances.remove(instance.getInstanceId());
                instance.transitionTo(InstanceState.DISPOSING);
                
                throw new RuntimeException("Failed to create instance", e);
            }
        });
    }
    
    /**
     * Creates the world for an instance.
     */
    @NotNull
    private World createInstanceWorld(@NotNull DungeonInstance instance) {
        String worldName = instance.getWorldName();
        
        // Create world creator
        WorldCreator creator = new WorldCreator(worldName);
        creator.generator(voidGenerator);
        creator.environment(World.Environment.NORMAL);
        creator.type(WorldType.FLAT);
        creator.generateStructures(false);
        
        // Create the world
        World world = creator.createWorld();
        if (world == null) {
            throw new RuntimeException("Failed to create world: " + worldName);
        }
        
        // Configure world settings
        world.setDifficulty(Difficulty.NORMAL);
        world.setSpawnFlags(false, false); // No monsters or animals
        world.setPVP(false); // Controlled by protection service
        world.setGameRule(GameRule.DO_DAYLIGHT_CYCLE, false);
        world.setGameRule(GameRule.DO_WEATHER_CYCLE, false);
        world.setGameRule(GameRule.DO_MOB_SPAWNING, false);
        world.setGameRule(GameRule.KEEP_INVENTORY, true);
        world.setGameRule(GameRule.ANNOUNCE_ADVANCEMENTS, false);
        world.setTime(6000); // Noon
        world.setStorm(false);
        world.setThundering(false);
        
        return world;
    }
    
    /**
     * Gets an instance by ID.
     * 
     * @param instanceId the instance ID
     * @return the instance, or null if not found
     */
    @Nullable
    public DungeonInstance getInstance(@NotNull UUID instanceId) {
        return instances.get(instanceId);
    }
    
    /**
     * Gets an instance by world.
     * 
     * @param world the world
     * @return the instance, or null if not found
     */
    @Nullable
    public DungeonInstance getInstanceByWorld(@NotNull World world) {
        return worldToInstance.get(world.getName());
    }
    
    /**
     * Gets an instance by player.
     * 
     * @param player the player
     * @return the instance the player is in, or null
     */
    @Nullable
    public DungeonInstance getInstanceByPlayer(@NotNull Player player) {
        World world = player.getWorld();
        return getInstanceByWorld(world);
    }
    
    /**
     * Gets all active instances.
     * 
     * @return collection of all instances
     */
    @NotNull
    public Collection<DungeonInstance> getAllInstances() {
        return new ArrayList<>(instances.values());
    }
    
    /**
     * Gets instances by state.
     * 
     * @param state the state to filter by
     * @return list of instances in the specified state
     */
    @NotNull
    public List<DungeonInstance> getInstancesByState(@NotNull InstanceState state) {
        return instances.values().stream()
            .filter(instance -> instance.getState() == state)
            .toList();
    }
    
    /**
     * Adds a player to an instance.
     * 
     * @param player the player to add
     * @param instance the instance to add to
     * @return true if successful
     */
    public boolean addPlayerToInstance(@NotNull Player player, @NotNull DungeonInstance instance) {
        if (instance.addPlayer(player)) {
            plugin.getLogger().info("Player " + player.getName() + " joined instance " + instance.getInstanceId());
            return true;
        }
        return false;
    }
    
    /**
     * Removes a player from their current instance.
     * 
     * @param player the player to remove
     * @return true if player was in an instance and removed
     */
    public boolean removePlayerFromInstance(@NotNull Player player) {
        DungeonInstance instance = getInstanceByPlayer(player);
        if (instance != null) {
            if (instance.removePlayer(player)) {
                plugin.getLogger().info("Player " + player.getName() + " left instance " + instance.getInstanceId());
                return true;
            }
        }
        return false;
    }
    
    /**
     * Forces completion of an instance.
     * 
     * @param instance the instance to complete
     * @param success whether the completion was successful
     */
    public void completeInstance(@NotNull DungeonInstance instance, boolean success) {
        if (instance.transitionTo(InstanceState.COMPLETING)) {
            plugin.getLogger().info("Instance " + instance.getInstanceId() + " completed (success: " + success + ")");
            
            // Notify players
            Component message = success ?
                Component.text("Dungeon completed successfully!").color(NamedTextColor.GREEN) :
                Component.text("Dungeon failed!").color(NamedTextColor.RED);
            instance.broadcastMessage(message);
            
            // Schedule disposal after a delay
            schedulerUtil.runTaskLater(() -> disposeInstance(instance), 20L * 10L); // 10 seconds
        }
    }
    
    /**
     * Disposes of an instance and cleans up resources.
     * 
     * @param instance the instance to dispose
     */
    public void disposeInstance(@NotNull DungeonInstance instance) {
        if (!instance.transitionTo(InstanceState.DISPOSING)) {
            return;
        }
        
        plugin.getLogger().info("Disposing instance " + instance.getInstanceId());
        
        try {
            // Remove all players
            List<Player> players = instance.getOnlinePlayers();
            for (Player player : players) {
                instance.removePlayer(player);
            }
            
            // Unload and delete world
            World world = instance.getWorld();
            if (world != null) {
                unloadAndDeleteWorld(world);
            }
            
            // Remove from tracking
            instances.remove(instance.getInstanceId());
            worldToInstance.remove(instance.getWorldName());
            protectionService.removeProtectedWorld(instance.getWorldName());
            
            plugin.getLogger().info("Instance " + instance.getInstanceId() + " disposed successfully");
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error disposing instance " + instance.getInstanceId(), e);
        }
    }
    
    /**
     * Unloads and deletes a world.
     */
    private void unloadAndDeleteWorld(@NotNull World world) {
        String worldName = world.getName();
        
        // Teleport any remaining players out
        for (Player player : world.getPlayers()) {
            Location spawn = Bukkit.getWorlds().get(0).getSpawnLocation();
            player.teleport(spawn);
        }
        
        // Unload world
        if (Bukkit.unloadWorld(world, false)) {
            plugin.getLogger().info("Unloaded world: " + worldName);
            
            // Delete world folder
            schedulerUtil.runTaskAsynchronously(() -> {
                try {
                    File worldFolder = new File(Bukkit.getWorldContainer(), worldName);
                    if (worldFolder.exists()) {
                        deleteDirectory(worldFolder);
                        plugin.getLogger().info("Deleted world folder: " + worldName);
                    }
                } catch (Exception e) {
                    plugin.getLogger().log(Level.WARNING, "Failed to delete world folder: " + worldName, e);
                }
            });
        } else {
            plugin.getLogger().warning("Failed to unload world: " + worldName);
        }
    }
    
    /**
     * Recursively deletes a directory.
     */
    private void deleteDirectory(@NotNull File directory) {
        File[] files = directory.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    deleteDirectory(file);
                } else {
                    file.delete();
                }
            }
        }
        directory.delete();
    }
    
    /**
     * Starts the maintenance task.
     */
    private void startMaintenanceTask() {
        maintenanceTask = schedulerUtil.runTaskTimerAsynchronously(
            this::performMaintenance,
            maintenanceIntervalTicks,
            maintenanceIntervalTicks
        );
    }
    
    /**
     * Performs periodic maintenance tasks.
     */
    public void performMaintenance() {
        if (shutdownInProgress) {
            return;
        }
        
        try {
            Instant now = Instant.now();
            List<DungeonInstance> toDispose = new ArrayList<>();
            
            for (DungeonInstance instance : instances.values()) {
                // Check for timeouts
                if (instance.isTimedOut()) {
                    plugin.getLogger().info("Instance " + instance.getInstanceId() + " timed out");
                    toDispose.add(instance);
                    continue;
                }
                
                // Check for empty instances that should be disposed
                if (instance.getPlayerCount() == 0 && instance.getState().isActive()) {
                    // Grace period for rejoining
                    if (instance.getStartedAt() != null) {
                        long minutesSinceStart = (now.toEpochMilli() - instance.getStartedAt().toEpochMilli()) / (1000 * 60);
                        if (minutesSinceStart > 5) { // 5 minute grace period
                            plugin.getLogger().info("Instance " + instance.getInstanceId() + " empty for too long");
                            toDispose.add(instance);
                        }
                    }
                }
            }
            
            // Dispose instances that need cleanup
            for (DungeonInstance instance : toDispose) {
                schedulerUtil.runTask(() -> disposeInstance(instance));
            }
            
            // Log statistics
            if (instances.size() > 0) {
                plugin.getLogger().info("Instance statistics: " + instances.size() + " active instances");
            }
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error during instance maintenance", e);
        }
    }
    
    @EventHandler
    public void onPlayerJoin(@NotNull PlayerJoinEvent event) {
        // Handle player rejoining instances
        Player player = event.getPlayer();
        
        // Check if player was in an instance before disconnecting
        // TODO: Implement rejoin logic with grace period
    }
    
    @EventHandler
    public void onPlayerQuit(@NotNull PlayerQuitEvent event) {
        // Handle player leaving instances
        Player player = event.getPlayer();
        DungeonInstance instance = getInstanceByPlayer(player);
        
        if (instance != null) {
            // Don't remove immediately - allow rejoin grace period
            plugin.getLogger().info("Player " + player.getName() + " disconnected from instance " + instance.getInstanceId());
        }
    }
    
    @EventHandler
    public void onWorldUnload(@NotNull WorldUnloadEvent event) {
        // Prevent unloading of instance worlds unless we're disposing them
        DungeonInstance instance = getInstanceByWorld(event.getWorld());
        if (instance != null && instance.getState() != InstanceState.DISPOSING) {
            event.setCancelled(true);
        }
    }
    
    /**
     * Shuts down the instance manager.
     */
    public void shutdown() {
        shutdownInProgress = true;
        
        plugin.getLogger().info("Shutting down instance manager...");
        
        // Cancel maintenance task
        if (maintenanceTask != null && !maintenanceTask.isCancelled()) {
            maintenanceTask.cancel();
        }
        
        // Dispose all instances
        List<DungeonInstance> allInstances = new ArrayList<>(instances.values());
        for (DungeonInstance instance : allInstances) {
            try {
                disposeInstance(instance);
            } catch (Exception e) {
                plugin.getLogger().log(Level.SEVERE, "Error disposing instance during shutdown", e);
            }
        }
        
        plugin.getLogger().info("Instance manager shut down, disposed " + allInstances.size() + " instances");
    }
    
    // Getters
    
    @NotNull
    public ProtectionService getProtectionService() {
        return protectionService;
    }
    
    public int getMaxInstances() {
        return maxInstances;
    }
    
    public void setMaxInstances(int maxInstances) {
        this.maxInstances = Math.max(1, maxInstances);
    }
    
    public long getInstanceTimeoutTicks() {
        return instanceTimeoutTicks;
    }
    
    public void setInstanceTimeoutTicks(long instanceTimeoutTicks) {
        this.instanceTimeoutTicks = Math.max(0, instanceTimeoutTicks);
    }
    
    public int getActiveInstanceCount() {
        return instances.size();
    }
}

package com.ultimatedungeon.udx.gui.menus;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.dungeon.DungeonDef;
import com.ultimatedungeon.udx.dungeon.DungeonService;
import com.ultimatedungeon.udx.gui.Menu;
import com.ultimatedungeon.udx.party.Party;
import com.ultimatedungeon.udx.party.PartyService;
import com.ultimatedungeon.udx.util.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

/**
 * Menu for dungeon matchmaking queue.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class QueueMenu extends Menu {
    
    private final UltimateDungeonX plugin;
    private final PartyService partyService;
    private final DungeonService dungeonService;
    
    public QueueMenu(@NotNull UltimateDungeonX plugin, @NotNull Player player) {
        super(player, Component.text("Dungeon Queue").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true), 
              54, plugin.getMenuRegistry());
        this.plugin = plugin;
        this.partyService = plugin.getPartyService();
        this.dungeonService = plugin.getDungeonService();
    }
    
    @Override
    protected void setupMenu() {
        inventory.clear();
        clickHandlers.clear();
        
        Party party = partyService.getPlayerParty(player.getUniqueId());
        if (party == null) {
            setupNoPartyView();
        } else {
            setupDungeonSelectionView(party);
        }
        
        // Back button
        ItemStack backItem = new ItemBuilder(Material.ARROW)
            .name(Component.text("Back to Party").color(NamedTextColor.GRAY))
            .lore(Component.text("Click to return").color(NamedTextColor.DARK_GRAY))
            .build();
        
        setItem(49, backItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                new PartyMenu(plugin, player).open();
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        fillEmptySlots();
    }
    
    private void setupNoPartyView() {
        ItemStack noPartyItem = new ItemBuilder(Material.BARRIER)
            .name(Component.text("No Party").color(NamedTextColor.RED).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("You need to be in a party").color(NamedTextColor.GRAY),
                Component.text("to queue for dungeons.").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Create or join a party first!").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(22, noPartyItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                new PartyMenu(plugin, player).open();
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
    }
    
    private void setupDungeonSelectionView(@NotNull Party party) {
        // Party info
        List<Component> partyLore = new ArrayList<>();
        partyLore.add(Component.text("Party Size: " + party.getMembers().size()).color(NamedTextColor.GRAY));
        partyLore.add(Component.text("Leader: " + getPlayerName(party.getLeader())).color(NamedTextColor.GRAY));
        partyLore.add(Component.empty());
        partyLore.add(Component.text("Select a dungeon to queue for:").color(NamedTextColor.YELLOW));
        
        ItemStack partyInfoItem = new ItemBuilder(Material.BOOK)
            .name(Component.text("Party Information").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(partyLore)
            .build();
        
        setItem(4, partyInfoItem, clickType -> {
            // Info only, no action
        });
        
        // Available dungeons (hardcoded sample dungeons for now)
        setupSampleDungeons(party);
        
        // Queue status (if already queued)
        var queueData = partyService.getPartyQueue(party.getPartyId());
        if (queueData != null) {
            ItemStack queueStatusItem = new ItemBuilder(Material.CLOCK)
                .name(Component.text("Queue Status").color(NamedTextColor.AQUA).decoration(TextDecoration.BOLD, true))
                .lore(
                    Component.text("Queued for: " + queueData.dungeonId()).color(NamedTextColor.GRAY),
                    Component.text("Difficulty: " + queueData.difficulty()).color(NamedTextColor.GRAY),
                    Component.text("Status: " + queueData.status()).color(NamedTextColor.GRAY),
                    Component.empty(),
                    Component.text("Click to leave queue").color(NamedTextColor.RED)
                )
                .glow()
                .build();
            
            setItem(40, queueStatusItem, clickType -> {
                if (clickType == ClickType.LEFT) {
                    boolean success = partyService.leaveQueue(party.getPartyId());
                    if (success) {
                        player.sendMessage(Component.text("Left matchmaking queue.")
                            .color(NamedTextColor.YELLOW));
                        player.playSound(player.getLocation(), Sound.BLOCK_NOTE_BLOCK_BASS, 1.0f, 0.8f);
                        refresh();
                    }
                }
            });
        }
    }
    
    private void setupSampleDungeons(@NotNull Party party) {
        // Crypt of Echoes
        setupSampleDungeonItem("crypt_of_echoes", "Crypt of Echoes", 
            "Ancient undead crypts filled with skeletal guardians", 19, party);
        
        // Ember Foundry
        setupSampleDungeonItem("ember_foundry", "Ember Foundry", 
            "Blazing forges defended by fire elementals", 20, party);
        
        // Skyfane Spire
        setupSampleDungeonItem("skyfane_spire", "Skyfane Spire", 
            "Floating tower with wind magic and aerial combat", 21, party);
    }
    
    private void setupSampleDungeonItem(@NotNull String dungeonId, @NotNull String displayName, 
                                       @NotNull String description, int slot, @NotNull Party party) {
        List<Component> dungeonLore = new ArrayList<>();
        dungeonLore.add(Component.text("Length: 5-10 rooms").color(NamedTextColor.GRAY));
        dungeonLore.add(Component.text("Recommended Level: 1+").color(NamedTextColor.GRAY));
        dungeonLore.add(Component.text("Max Players: 5").color(NamedTextColor.GRAY));
        dungeonLore.add(Component.empty());
        
        // Add description
        dungeonLore.add(Component.text(description).color(NamedTextColor.YELLOW));
        dungeonLore.add(Component.empty());
        
        // Difficulty options
        dungeonLore.add(Component.text("Available Difficulties:").color(NamedTextColor.GOLD));
        dungeonLore.add(Component.text("• Normal").color(NamedTextColor.GREEN));
        dungeonLore.add(Component.text("• Hard").color(NamedTextColor.YELLOW));
        dungeonLore.add(Component.text("• Mythic").color(NamedTextColor.RED));
        dungeonLore.add(Component.empty());
        dungeonLore.add(Component.text("Click to select difficulty").color(NamedTextColor.AQUA));
        
        Material dungeonMaterial = getDungeonMaterial(dungeonId);
        ItemStack dungeonItem = new ItemBuilder(dungeonMaterial)
            .name(Component.text(displayName).color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(dungeonLore)
            .build();
        
        setItem(slot, dungeonItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                // Open difficulty selection
                new DifficultySelectionMenu(plugin, player, dungeonId).open();
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
            }
        });
    }
    
    private Material getDungeonMaterial(@NotNull String dungeonId) {
        return switch (dungeonId.toLowerCase()) {
            case "crypt_of_echoes" -> Material.SKELETON_SKULL;
            case "ember_foundry" -> Material.BLAZE_POWDER;
            case "skyfane_spire" -> Material.END_CRYSTAL;
            default -> Material.MOSSY_COBBLESTONE;
        };
    }
    
    private String getPlayerName(@NotNull java.util.UUID playerId) {
        Player player = plugin.getServer().getPlayer(playerId);
        return player != null ? player.getName() : "Unknown";
    }
    
    /**
     * Inner menu for difficulty selection.
     */
    private static class DifficultySelectionMenu extends Menu {
        
        private final UltimateDungeonX plugin;
        private final String dungeonId;
        private final PartyService partyService;
        
        public DifficultySelectionMenu(@NotNull UltimateDungeonX plugin, @NotNull Player player, @NotNull String dungeonId) {
            super(player, Component.text("Select Difficulty").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true), 
                  27, plugin.getMenuRegistry());
            this.plugin = plugin;
            this.dungeonId = dungeonId;
            this.partyService = plugin.getPartyService();
        }
        
        @Override
        protected void setupMenu() {
            inventory.clear();
            clickHandlers.clear();
            
            // Normal difficulty
            ItemStack normalItem = new ItemBuilder(Material.IRON_SWORD)
                .name(Component.text("Normal").color(NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true))
                .lore(
                    Component.text("Standard difficulty").color(NamedTextColor.GRAY),
                    Component.text("Recommended for new players").color(NamedTextColor.GRAY),
                    Component.empty(),
                    Component.text("Rewards: Standard").color(NamedTextColor.YELLOW),
                    Component.text("Click to queue").color(NamedTextColor.AQUA)
                )
                .build();
            
            setItem(11, normalItem, clickType -> {
                if (clickType == ClickType.LEFT) {
                    queueForDungeon("normal");
                }
            });
            
            // Hard difficulty
            ItemStack hardItem = new ItemBuilder(Material.DIAMOND_SWORD)
                .name(Component.text("Hard").color(NamedTextColor.YELLOW).decoration(TextDecoration.BOLD, true))
                .lore(
                    Component.text("Increased difficulty").color(NamedTextColor.GRAY),
                    Component.text("More challenging enemies").color(NamedTextColor.GRAY),
                    Component.empty(),
                    Component.text("Rewards: Enhanced (1.5x)").color(NamedTextColor.YELLOW),
                    Component.text("Click to queue").color(NamedTextColor.AQUA)
                )
                .build();
            
            setItem(13, hardItem, clickType -> {
                if (clickType == ClickType.LEFT) {
                    queueForDungeon("hard");
                }
            });
            
            // Mythic difficulty
            ItemStack mythicItem = new ItemBuilder(Material.NETHERITE_SWORD)
                .name(Component.text("Mythic").color(NamedTextColor.RED).decoration(TextDecoration.BOLD, true))
                .lore(
                    Component.text("Maximum difficulty").color(NamedTextColor.GRAY),
                    Component.text("Extremely challenging").color(NamedTextColor.GRAY),
                    Component.text("Requires coordination").color(NamedTextColor.GRAY),
                    Component.empty(),
                    Component.text("Rewards: Premium (2x)").color(NamedTextColor.YELLOW),
                    Component.text("Click to queue").color(NamedTextColor.AQUA)
                )
                .glow()
                .build();
            
            setItem(15, mythicItem, clickType -> {
                if (clickType == ClickType.LEFT) {
                    queueForDungeon("mythic");
                }
            });
            
            // Back button
            ItemStack backItem = new ItemBuilder(Material.ARROW)
                .name(Component.text("Back").color(NamedTextColor.GRAY))
                .lore(Component.text("Return to dungeon selection").color(NamedTextColor.DARK_GRAY))
                .build();
            
            setItem(22, backItem, clickType -> {
                if (clickType == ClickType.LEFT) {
                    new QueueMenu(plugin, player).open();
                    player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                }
            });
            
            fillEmptySlots();
        }
        
        private void queueForDungeon(@NotNull String difficulty) {
            Party party = partyService.getPlayerParty(player.getUniqueId());
            if (party == null) {
                player.sendMessage(Component.text("You are not in a party!")
                    .color(NamedTextColor.RED));
                return;
            }
            
            if (!party.getLeader().equals(player.getUniqueId())) {
                player.sendMessage(Component.text("Only the party leader can queue for dungeons!")
                    .color(NamedTextColor.RED));
                return;
            }
            
            boolean success = partyService.queueForMatchmaking(party.getPartyId(), dungeonId, difficulty);
            if (success) {
                player.sendMessage(Component.text("Successfully queued for " + dungeonId + " (" + difficulty + ")")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.2f);
                
                // Return to queue menu to show status
                new QueueMenu(plugin, player).open();
            } else {
                player.sendMessage(Component.text("Failed to queue for dungeon. You may already be in queue.")
                    .color(NamedTextColor.RED));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 0.8f);
            }
        }
    }
}

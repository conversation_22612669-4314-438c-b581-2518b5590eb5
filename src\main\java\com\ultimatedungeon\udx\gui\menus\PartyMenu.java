package com.ultimatedungeon.udx.gui.menus;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.gui.Menu;
import com.ultimatedungeon.udx.party.Party;
import com.ultimatedungeon.udx.party.PartyService;
import com.ultimatedungeon.udx.util.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Menu for party management - create, join, invite, and manage parties.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class PartyMenu extends Menu {
    
    private final UltimateDungeonX plugin;
    private final PartyService partyService;
    
    public PartyMenu(@NotNull UltimateDungeonX plugin, @NotNull Player player) {
        super(player, Component.text("Party Management").color(NamedTextColor.BLUE).decoration(TextDecoration.BOLD, true), 
              45, plugin.getMenuRegistry());
        this.plugin = plugin;
        this.partyService = plugin.getPartyService();
    }
    
    @Override
    protected void setupMenu() {
        inventory.clear();
        clickHandlers.clear();
        
        Party currentParty = partyService.getPlayerParty(player.getUniqueId());
        
        if (currentParty != null) {
            setupPartyManagementView(currentParty);
        } else {
            setupPartyCreationView();
        }
        
        // Back button
        ItemStack backItem = new ItemBuilder(Material.ARROW)
            .name(Component.text("Back to Main Menu").color(NamedTextColor.GRAY))
            .lore(Component.text("Click to return").color(NamedTextColor.DARK_GRAY))
            .build();
        
        setItem(40, backItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                new PlayerMainMenu(plugin, player).open();
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        fillEmptySlots();
    }
    
    private void setupPartyCreationView() {
        // Create Party
        ItemStack createItem = new ItemBuilder(Material.EMERALD)
            .name(Component.text("Create Party").color(NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Start a new party").color(NamedTextColor.GRAY),
                Component.text("Invite friends to join you").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to create party").color(NamedTextColor.YELLOW)
            )
            .glow()
            .build();
        
        setItem(20, createItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                Party party = partyService.createParty(player);
                if (party != null) {
                    player.sendMessage(Component.text("Party created! You are now the party leader.")
                        .color(NamedTextColor.GREEN));
                    player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.2f);
                    refresh();
                } else {
                    player.sendMessage(Component.text("Failed to create party. You may already be in one.")
                        .color(NamedTextColor.RED));
                    player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 0.8f);
                }
            }
        });
        
        // Join Party (placeholder for invite system)
        ItemStack joinItem = new ItemBuilder(Material.PLAYER_HEAD)
            .name(Component.text("Join Party").color(NamedTextColor.BLUE))
            .lore(
                Component.text("Join an existing party").color(NamedTextColor.GRAY),
                Component.text("Requires an invitation").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("No pending invitations").color(NamedTextColor.RED)
            )
            .build();
        
        setItem(24, joinItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("No pending party invitations.")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
    }
    
    private void setupPartyManagementView(@NotNull Party party) {
        // Party Info
        List<Component> partyLore = new ArrayList<>();
        partyLore.add(Component.text("Party ID: " + party.getPartyId()).color(NamedTextColor.GRAY));
        partyLore.add(Component.text("Members: " + party.getMembers().size() + "/5").color(NamedTextColor.GRAY));
        partyLore.add(Component.empty());
        
        // Add member list
        party.getMembers().forEach(memberId -> {
            Player member = plugin.getServer().getPlayer(memberId);
            String memberName = member != null ? member.getName() : "Unknown";
            boolean isLeader = party.getLeader().equals(memberId);
            
            Component memberComponent = Component.text("• " + memberName)
                .color(isLeader ? NamedTextColor.GOLD : NamedTextColor.WHITE);
            if (isLeader) {
                memberComponent = memberComponent.append(Component.text(" (Leader)").color(NamedTextColor.YELLOW));
            }
            partyLore.add(memberComponent);
        });
        
        ItemStack partyInfoItem = new ItemBuilder(Material.BOOK)
            .name(Component.text("Party Information").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(partyLore)
            .build();
        
        setItem(13, partyInfoItem, clickType -> {
            // Info only, no action
        });
        
        // Leave Party
        ItemStack leaveItem = new ItemBuilder(Material.BARRIER)
            .name(Component.text("Leave Party").color(NamedTextColor.RED))
            .lore(
                Component.text("Leave the current party").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to leave").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(30, leaveItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                boolean success = partyService.leaveParty(player.getUniqueId());
                if (success) {
                    player.sendMessage(Component.text("You left the party.")
                        .color(NamedTextColor.YELLOW));
                    player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 1.0f);
                    refresh();
                } else {
                    player.sendMessage(Component.text("Failed to leave party.")
                        .color(NamedTextColor.RED));
                }
            }
        });
        
        // Leader-only actions
        if (party.getLeader().equals(player.getUniqueId())) {
            // Invite Player
            ItemStack inviteItem = new ItemBuilder(Material.WRITABLE_BOOK)
                .name(Component.text("Invite Player").color(NamedTextColor.GREEN))
                .lore(
                    Component.text("Invite a player to your party").color(NamedTextColor.GRAY),
                    Component.empty(),
                    Component.text("Use: /party invite <player>").color(NamedTextColor.YELLOW)
                )
                .build();
            
            setItem(20, inviteItem, clickType -> {
                if (clickType == ClickType.LEFT) {
                    player.sendMessage(Component.text("Use /party invite <player> to invite someone to your party.")
                        .color(NamedTextColor.YELLOW));
                    player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                }
            });
            
            // Disband Party
            ItemStack disbandItem = new ItemBuilder(Material.TNT)
                .name(Component.text("Disband Party").color(NamedTextColor.DARK_RED))
                .lore(
                    Component.text("Permanently disband the party").color(NamedTextColor.GRAY),
                    Component.text("This will kick all members").color(NamedTextColor.RED),
                    Component.empty(),
                    Component.text("Click to disband").color(NamedTextColor.YELLOW)
                )
                .build();
            
            setItem(32, disbandItem, clickType -> {
                if (clickType == ClickType.LEFT) {
                    boolean success = partyService.disbandParty(party.getPartyId());
                    if (success) {
                        player.sendMessage(Component.text("Party disbanded.")
                            .color(NamedTextColor.RED));
                        player.playSound(player.getLocation(), Sound.ENTITY_GENERIC_EXPLODE, 1.0f, 1.0f);
                        refresh();
                    } else {
                        player.sendMessage(Component.text("Failed to disband party.")
                            .color(NamedTextColor.RED));
                    }
                }
            });
        }
        
        // Queue for Dungeon (if party is ready)
        if (party.getMembers().size() >= 1) { // Allow solo queueing for testing
            ItemStack queueItem = new ItemBuilder(Material.DIAMOND_SWORD)
                .name(Component.text("Queue for Dungeon").color(NamedTextColor.AQUA).decoration(TextDecoration.BOLD, true))
                .lore(
                    Component.text("Enter dungeon matchmaking").color(NamedTextColor.GRAY),
                    Component.text("Party members: " + party.getMembers().size()).color(NamedTextColor.GRAY),
                    Component.empty(),
                    Component.text("Click to queue").color(NamedTextColor.YELLOW)
                )
                .glow()
                .build();
            
            setItem(24, queueItem, clickType -> {
                if (clickType == ClickType.LEFT) {
                    new QueueMenu(plugin, player).open();
                    player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                }
            });
        }
    }
}

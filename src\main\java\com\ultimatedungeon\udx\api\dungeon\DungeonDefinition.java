package com.ultimatedungeon.udx.api.dungeon;

import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Set;

/**
 * Immutable representation of a dungeon definition for API consumers.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public record DungeonDefinition(
    @NotNull String id,
    @NotNull String displayName,
    @NotNull String description,
    int minLength,
    int maxLength,
    @NotNull List<String> themeTags,
    @NotNull Set<String> allowedAffixes,
    @NotNull List<String> availableDifficulties,
    boolean enabled
) {
    
    /**
     * Creates a new dungeon definition.
     * 
     * @param id Unique dungeon identifier
     * @param displayName Human-readable name
     * @param description Dungeon description
     * @param minLength Minimum number of rooms
     * @param maxLength Maximum number of rooms
     * @param themeTags Theme tags for categorization
     * @param allowedAffixes Set of allowed affix IDs
     * @param availableDifficulties List of available difficulty tiers
     * @param enabled Whether the dungeon is enabled
     */
    public DungeonDefinition {
        // Validation
        if (id == null || id.isBlank()) {
            throw new IllegalArgumentException("Dungeon ID cannot be null or blank");
        }
        if (displayName == null || displayName.isBlank()) {
            throw new IllegalArgumentException("Display name cannot be null or blank");
        }
        if (description == null) {
            throw new IllegalArgumentException("Description cannot be null");
        }
        if (minLength < 1) {
            throw new IllegalArgumentException("Minimum length must be at least 1");
        }
        if (maxLength < minLength) {
            throw new IllegalArgumentException("Maximum length must be >= minimum length");
        }
        if (themeTags == null) {
            throw new IllegalArgumentException("Theme tags cannot be null");
        }
        if (allowedAffixes == null) {
            throw new IllegalArgumentException("Allowed affixes cannot be null");
        }
        if (availableDifficulties == null || availableDifficulties.isEmpty()) {
            throw new IllegalArgumentException("Available difficulties cannot be null or empty");
        }
    }
    
    /**
     * Checks if a difficulty is available for this dungeon.
     * 
     * @param difficulty The difficulty to check
     * @return True if the difficulty is available
     */
    public boolean isDifficultyAvailable(@NotNull String difficulty) {
        return availableDifficulties.contains(difficulty);
    }
    
    /**
     * Checks if an affix is allowed for this dungeon.
     * 
     * @param affixId The affix ID to check
     * @return True if the affix is allowed
     */
    public boolean isAffixAllowed(@NotNull String affixId) {
        return allowedAffixes.contains(affixId);
    }
    
    /**
     * Checks if the dungeon has a specific theme tag.
     * 
     * @param tag The theme tag to check
     * @return True if the dungeon has the theme tag
     */
    public boolean hasThemeTag(@NotNull String tag) {
        return themeTags.contains(tag);
    }
}

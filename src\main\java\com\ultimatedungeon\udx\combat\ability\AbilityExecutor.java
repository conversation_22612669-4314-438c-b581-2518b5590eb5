package com.ultimatedungeon.udx.combat.ability;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.combat.CombatService;
import com.ultimatedungeon.udx.combat.DamageType;
import com.ultimatedungeon.udx.combat.StatusEffect;
import com.ultimatedungeon.udx.combat.ability.Ability.*;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.LivingEntity;
import org.bukkit.util.BoundingBox;
import org.bukkit.util.RayTraceResult;
import org.bukkit.util.Vector;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Executes abilities with proper targeting, hitbox detection, and visual effects.
 * 
 * <p>This class handles the complex logic of ability execution including
 * area targeting, line of sight checks, particle effects, and damage application.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class AbilityExecutor {
    
    private final UltimateDungeonX plugin;
    private final CombatService combatService;
    
    public AbilityExecutor(@NotNull UltimateDungeonX plugin, @NotNull CombatService combatService) {
        this.plugin = plugin;
        this.combatService = combatService;
    }
    
    /**
     * Executes an ability with the given parameters.
     * 
     * @param ability the ability to execute
     * @param caster the entity casting the ability
     * @param target the target entity (can be null for area/ground abilities)
     * @param targetLocation the target location (can be null for entity-targeted abilities)
     * @return a future that completes when the ability execution is finished
     */
    @NotNull
    public CompletableFuture<Boolean> executeAbility(@NotNull Ability ability, @NotNull LivingEntity caster,
                                                    @Nullable LivingEntity target, @Nullable Location targetLocation) {
        
        CompletableFuture<Boolean> future = new CompletableFuture<>();
        
        // Validate ability usage
        if (!ability.canUse(caster)) {
            future.complete(false);
            return future;
        }
        
        // Validate targeting
        if (!validateTargeting(ability, caster, target, targetLocation)) {
            future.complete(false);
            return future;
        }
        
        // Show cast visuals
        showCastVisuals(ability, caster);
        
        // Handle cast time
        if (ability.castTimeMs() > 0) {
            plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                executeAbilityEffects(ability, caster, target, targetLocation);
                future.complete(true);
            }, ability.castTimeMs() / 50L); // Convert ms to ticks
        } else {
            executeAbilityEffects(ability, caster, target, targetLocation);
            future.complete(true);
        }
        
        return future;
    }
    
    /**
     * Validates the targeting for an ability.
     */
    private boolean validateTargeting(@NotNull Ability ability, @NotNull LivingEntity caster,
                                    @Nullable LivingEntity target, @Nullable Location targetLocation) {
        
        return switch (ability.targetType()) {
            case SELF -> caster.equals(target);
            case SINGLE_ENEMY, SINGLE_ALLY, SINGLE_ANY -> target != null && ability.canTarget(caster, target);
            case GROUND_TARGET -> targetLocation != null && ability.canTarget(caster, targetLocation);
            case AREA_ENEMY, AREA_ALLY, AREA_ANY, CONE, LINE -> true; // These are validated during execution
        };
    }
    
    /**
     * Executes the actual effects of an ability.
     */
    private void executeAbilityEffects(@NotNull Ability ability, @NotNull LivingEntity caster,
                                     @Nullable LivingEntity target, @Nullable Location targetLocation) {
        
        // Get targets based on ability type
        List<LivingEntity> targets = getTargets(ability, caster, target, targetLocation);
        
        // Apply effects to each target
        for (LivingEntity targetEntity : targets) {
            applyAbilityEffects(ability, caster, targetEntity);
        }
        
        // Show hit visuals
        showHitVisuals(ability, caster, targets, targetLocation);
        
        // Set cooldown
        setCooldown(caster, ability);
    }
    
    /**
     * Gets all targets for an ability based on its targeting type.
     */
    @NotNull
    private List<LivingEntity> getTargets(@NotNull Ability ability, @NotNull LivingEntity caster,
                                        @Nullable LivingEntity target, @Nullable Location targetLocation) {
        
        List<LivingEntity> targets = new ArrayList<>();
        
        switch (ability.targetType()) {
            case SELF -> targets.add(caster);
            case SINGLE_ENEMY, SINGLE_ALLY, SINGLE_ANY -> {
                if (target != null) {
                    targets.add(target);
                }
            }
            case AREA_ENEMY, AREA_ALLY, AREA_ANY -> {
                Location center = targetLocation != null ? targetLocation : 
                                (target != null ? target.getLocation() : caster.getLocation());
                targets.addAll(getAreaTargets(ability, caster, center));
            }
            case CONE -> targets.addAll(getConeTargets(ability, caster));
            case LINE -> targets.addAll(getLineTargets(ability, caster, targetLocation));
            case GROUND_TARGET -> {
                if (targetLocation != null) {
                    targets.addAll(getAreaTargets(ability, caster, targetLocation));
                }
            }
        }
        
        return targets;
    }
    
    /**
     * Gets targets in an area around a center point.
     */
    @NotNull
    private List<LivingEntity> getAreaTargets(@NotNull Ability ability, @NotNull LivingEntity caster, @NotNull Location center) {
        List<LivingEntity> targets = new ArrayList<>();
        double radius = ability.getParameter("radius", 5.0);
        
        Collection<LivingEntity> nearbyEntities = center.getWorld().getNearbyLivingEntities(center, radius);
        
        for (LivingEntity entity : nearbyEntities) {
            if (entity.equals(caster)) {
                continue; // Skip caster unless self-targeting
            }
            
            if (ability.canTarget(caster, entity) && hasLineOfSight(caster.getEyeLocation(), entity.getEyeLocation())) {
                targets.add(entity);
            }
        }
        
        return targets;
    }
    
    /**
     * Gets targets in a cone in front of the caster.
     */
    @NotNull
    private List<LivingEntity> getConeTargets(@NotNull Ability ability, @NotNull LivingEntity caster) {
        List<LivingEntity> targets = new ArrayList<>();
        double range = ability.range();
        double angle = Math.toRadians(ability.getParameter("angle", 45.0));
        
        Vector direction = caster.getEyeLocation().getDirection();
        Location casterLocation = caster.getEyeLocation();
        
        Collection<LivingEntity> nearbyEntities = casterLocation.getWorld().getNearbyLivingEntities(casterLocation, range);
        
        for (LivingEntity entity : nearbyEntities) {
            if (entity.equals(caster)) {
                continue;
            }
            
            Vector toEntity = entity.getEyeLocation().toVector().subtract(casterLocation.toVector()).normalize();
            double entityAngle = direction.angle(toEntity);
            
            if (entityAngle <= angle / 2 && ability.canTarget(caster, entity) && 
                hasLineOfSight(casterLocation, entity.getEyeLocation())) {
                targets.add(entity);
            }
        }
        
        return targets;
    }
    
    /**
     * Gets targets in a line from the caster.
     */
    @NotNull
    private List<LivingEntity> getLineTargets(@NotNull Ability ability, @NotNull LivingEntity caster, @Nullable Location targetLocation) {
        List<LivingEntity> targets = new ArrayList<>();
        double range = ability.range();
        double width = ability.getParameter("width", 1.0);
        
        Location start = caster.getEyeLocation();
        Vector direction;
        
        if (targetLocation != null) {
            direction = targetLocation.toVector().subtract(start.toVector()).normalize();
        } else {
            direction = start.getDirection();
        }
        
        // Perform ray trace to find entities in line
        for (double d = 0; d <= range; d += 0.5) {
            Location checkLocation = start.clone().add(direction.clone().multiply(d));
            
            Collection<LivingEntity> nearbyEntities = checkLocation.getWorld().getNearbyLivingEntities(checkLocation, width);
            
            for (LivingEntity entity : nearbyEntities) {
                if (entity.equals(caster) || targets.contains(entity)) {
                    continue;
                }
                
                if (ability.canTarget(caster, entity)) {
                    targets.add(entity);
                }
            }
        }
        
        return targets;
    }
    
    /**
     * Applies all effects of an ability to a target.
     */
    private void applyAbilityEffects(@NotNull Ability ability, @NotNull LivingEntity caster, @NotNull LivingEntity target) {
        for (AbilityEffect effect : ability.effects()) {
            applyEffect(effect, caster, target);
        }
    }
    
    /**
     * Applies a single effect to a target.
     */
    private void applyEffect(@NotNull AbilityEffect effect, @NotNull LivingEntity caster, @NotNull LivingEntity target) {
        switch (effect.type()) {
            case DAMAGE -> {
                if (effect.damageType() != null) {
                    combatService.dealDamage(target, caster, effect.value(), effect.damageType(), "Ability");
                }
            }
            case HEAL -> combatService.healEntity(target, effect.value());
            case STATUS_EFFECT -> {
                if (effect.statusEffect() != null) {
                    combatService.applyStatusEffect(target, effect.statusEffect());
                }
            }
            case KNOCKBACK -> applyKnockback(caster, target, effect.value());
            case TELEPORT -> applyTeleport(target, effect);
            case SUMMON -> applySummon(caster, target, effect);
            case CUSTOM -> applyCustomEffect(effect, caster, target);
        }
    }
    
    /**
     * Applies knockback to a target.
     */
    private void applyKnockback(@NotNull LivingEntity caster, @NotNull LivingEntity target, double strength) {
        Vector direction = target.getLocation().toVector().subtract(caster.getLocation().toVector()).normalize();
        direction.multiply(strength);
        direction.setY(Math.max(0.1, direction.getY())); // Ensure some upward movement
        target.setVelocity(direction);
    }
    
    /**
     * Applies teleportation effect.
     */
    private void applyTeleport(@NotNull LivingEntity target, @NotNull AbilityEffect effect) {
        String teleportType = effect.parameters().getOrDefault("type", "forward").toString();
        double distance = ((Number) effect.parameters().getOrDefault("distance", 5.0)).doubleValue();
        
        Location currentLocation = target.getLocation();
        Location targetLocation = null;
        
        switch (teleportType.toLowerCase()) {
            case "forward" -> {
                Vector direction = currentLocation.getDirection().normalize().multiply(distance);
                targetLocation = currentLocation.clone().add(direction);
            }
            case "backward" -> {
                Vector direction = currentLocation.getDirection().normalize().multiply(-distance);
                targetLocation = currentLocation.clone().add(direction);
            }
            case "random" -> {
                double angle = Math.random() * 2 * Math.PI;
                double x = distance * Math.cos(angle);
                double z = distance * Math.sin(angle);
                targetLocation = currentLocation.clone().add(x, 0, z);
            }
        }
        
        if (targetLocation != null && isSafeLocation(targetLocation)) {
            target.teleport(targetLocation);
            
            // Teleport effects
            currentLocation.getWorld().spawnParticle(Particle.PORTAL, currentLocation, 20);
            targetLocation.getWorld().spawnParticle(Particle.PORTAL, targetLocation, 20);
        }
    }
    
    /**
     * Applies summon effect.
     */
    private void applySummon(@NotNull LivingEntity caster, @NotNull LivingEntity target, @NotNull AbilityEffect effect) {
        // Implementation would summon entities based on effect parameters
        // This is a placeholder for the summon mechanic
    }
    
    /**
     * Applies custom effects.
     */
    private void applyCustomEffect(@NotNull AbilityEffect effect, @NotNull LivingEntity caster, @NotNull LivingEntity target) {
        // Implementation would handle custom effects based on parameters
        // This allows for extensibility without modifying core code
    }
    
    /**
     * Shows visual effects when casting an ability.
     */
    private void showCastVisuals(@NotNull Ability ability, @NotNull LivingEntity caster) {
        AbilityVisuals visuals = ability.visuals();
        if (visuals == null) {
            return;
        }
        
        Location casterLocation = caster.getEyeLocation();
        
        // Cast particles
        if (visuals.castParticle() != null) {
            try {
                Particle particle = Particle.valueOf(visuals.castParticle().toUpperCase());
                casterLocation.getWorld().spawnParticle(particle, casterLocation, 10);
            } catch (IllegalArgumentException e) {
                // Invalid particle name
            }
        }
        
        // Cast sound
        if (visuals.castSound() != null) {
            try {
                Sound sound = Sound.valueOf(visuals.castSound().toUpperCase());
                casterLocation.getWorld().playSound(casterLocation, sound, 1.0f, 1.0f);
            } catch (IllegalArgumentException e) {
                // Invalid sound name
            }
        }
    }
    
    /**
     * Shows visual effects when an ability hits targets.
     */
    private void showHitVisuals(@NotNull Ability ability, @NotNull LivingEntity caster, 
                               @NotNull List<LivingEntity> targets, @Nullable Location targetLocation) {
        AbilityVisuals visuals = ability.visuals();
        if (visuals == null) {
            return;
        }
        
        // Hit particles on each target
        if (visuals.hitParticle() != null) {
            try {
                Particle particle = Particle.valueOf(visuals.hitParticle().toUpperCase());
                for (LivingEntity target : targets) {
                    target.getWorld().spawnParticle(particle, target.getEyeLocation(), 5);
                }
            } catch (IllegalArgumentException e) {
                // Invalid particle name
            }
        }
        
        // Area particles
        if (visuals.areaParticle() != null && targetLocation != null) {
            try {
                Particle particle = Particle.valueOf(visuals.areaParticle().toUpperCase());
                double radius = ability.getParameter("radius", 5.0);
                
                // Create circle of particles
                for (int i = 0; i < 360; i += 10) {
                    double angle = Math.toRadians(i);
                    double x = targetLocation.getX() + radius * Math.cos(angle);
                    double z = targetLocation.getZ() + radius * Math.sin(angle);
                    Location particleLocation = new Location(targetLocation.getWorld(), x, targetLocation.getY(), z);
                    
                    targetLocation.getWorld().spawnParticle(particle, particleLocation, 1);
                }
            } catch (IllegalArgumentException e) {
                // Invalid particle name
            }
        }
        
        // Hit sound
        if (visuals.hitSound() != null && !targets.isEmpty()) {
            try {
                Sound sound = Sound.valueOf(visuals.hitSound().toUpperCase());
                Location soundLocation = targets.get(0).getLocation();
                soundLocation.getWorld().playSound(soundLocation, sound, 1.0f, 1.0f);
            } catch (IllegalArgumentException e) {
                // Invalid sound name
            }
        }
    }
    
    /**
     * Sets cooldown for an ability on a caster.
     */
    private void setCooldown(@NotNull LivingEntity caster, @NotNull Ability ability) {
        combatService.setCooldown(caster, ability.id(), ability.getEffectiveCooldown(caster));
    }
    
    /**
     * Checks if there's line of sight between two locations.
     */
    private boolean hasLineOfSight(@NotNull Location from, @NotNull Location to) {
        RayTraceResult result = from.getWorld().rayTraceBlocks(from, to.toVector().subtract(from.toVector()), 
                                                              from.distance(to));
        return result == null || result.getHitBlock() == null;
    }
    
    /**
     * Checks if a location is safe for teleportation.
     */
    private boolean isSafeLocation(@NotNull Location location) {
        // Check if the location is not inside blocks and has ground below
        return location.getBlock().isPassable() && 
               location.clone().add(0, 1, 0).getBlock().isPassable() &&
               !location.clone().subtract(0, 1, 0).getBlock().isPassable();
    }
}

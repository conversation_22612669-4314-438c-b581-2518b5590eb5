package com.ultimatedungeon.udx.boss;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.boss.ability.*;
import com.ultimatedungeon.udx.boss.BossDef.*;
import com.ultimatedungeon.udx.spawner.MobService;
import com.ultimatedungeon.udx.util.SchedulerUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import net.kyori.adventure.text.Component;
import org.bukkit.Location;
import org.bukkit.attribute.Attribute;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.scheduler.BukkitTask;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Service for managing boss encounters and mechanics.
 * 
 * <p>Handles boss spawning, phase transitions, ability execution,
 * and integration with the dungeon system. Provides a framework
 * for complex boss fights with multiple phases and abilities.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class BossService implements Listener {
    
    private final UltimateDungeonX plugin;
    private final MobService mobService;
    private final SchedulerUtil schedulerUtil;
    private final Logger logger;
    private final Gson gson;
    
    // Boss management
    private final Map<String, BossDef> bossDefinitions;
    private final Map<String, BossInstance> activeBosses;
    private final Map<UUID, String> entityToBoss;
    
    // Ability executors
    private final Map<String, Class<? extends BossAbilityExecutor>> abilityExecutors;
    
    // Update task
    private BukkitTask updateTask;
    
    public BossService(@NotNull UltimateDungeonX plugin, @NotNull MobService mobService, 
                      @NotNull SchedulerUtil schedulerUtil) {
        this.plugin = plugin;
        this.mobService = mobService;
        this.schedulerUtil = schedulerUtil;
        this.logger = plugin.getLogger();
        this.gson = new GsonBuilder().setPrettyPrinting().create();
        
        this.bossDefinitions = new ConcurrentHashMap<>();
        this.activeBosses = new ConcurrentHashMap<>();
        this.entityToBoss = new ConcurrentHashMap<>();
        this.abilityExecutors = new HashMap<>();
        
        initialize();
        
        logger.info("BossService initialized");
    }
    
    /**
     * Initializes the boss service.
     */
    private void initialize() {
        // Register default ability executors
        registerAbilityExecutors();
        
        // Load boss definitions
        loadBossDefinitions();
        
        // Start update task
        startUpdateTask();
    }
    
    /**
     * Registers default ability executors.
     */
    private void registerAbilityExecutors() {
        abilityExecutors.put("PROJECTILE", BasicAbilities.ProjectileAbility.class);
        abilityExecutors.put("AOE_DAMAGE", BasicAbilities.AoEDamageAbility.class);
        abilityExecutors.put("SUMMON_MINIONS", BasicAbilities.SummonMinionsAbility.class);
        abilityExecutors.put("TELEPORT", BasicAbilities.TeleportAbility.class);
        abilityExecutors.put("CHARGE", BasicAbilities.ChargeAbility.class);
        abilityExecutors.put("HEAL", BasicAbilities.HealAbility.class);
    }
    
    /**
     * Loads boss definitions from files.
     */
    private void loadBossDefinitions() {
        File bossDir = new File(plugin.getDataFolder(), "bosses");
        if (!bossDir.exists()) {
            bossDir.mkdirs();
            createSampleBosses();
            return;
        }
        
        File[] bossFiles = bossDir.listFiles((dir, name) -> name.endsWith(".json"));
        if (bossFiles == null) return;
        
        for (File bossFile : bossFiles) {
            try {
                loadBossDefinition(bossFile);
            } catch (Exception e) {
                logger.log(Level.WARNING, "Failed to load boss definition: " + bossFile.getName(), e);
            }
        }
        
        logger.info("Loaded " + bossDefinitions.size() + " boss definitions");
    }
    
    /**
     * Loads a single boss definition from file.
     */
    private void loadBossDefinition(@NotNull File file) throws IOException {
        try (FileReader reader = new FileReader(file)) {
            BossDef bossDef = gson.fromJson(reader, BossDef.class);
            bossDef.validate();
            bossDefinitions.put(bossDef.id(), bossDef);
            logger.fine("Loaded boss definition: " + bossDef.id());
        }
    }
    
    /**
     * Creates sample boss definitions.
     */
    private void createSampleBosses() {
        // Create sample boss definitions
        createSampleBoss1();
        createSampleBoss2();
        createSampleBoss3();
    }
    
    /**
     * Creates the first sample boss.
     */
    private void createSampleBoss1() {
        BossAttributes attributes = new BossAttributes(
            200.0, // maxHealth
            15.0,  // attackDamage
            0.3,   // movementSpeed
            5.0,   // armor
            2.0,   // armorToughness
            0.8,   // knockbackResistance
            Map.of("FIRE", 0.5, "MAGIC", 0.3)
        );
        
        Telegraph telegraph = new Telegraph(
            2000, // 2 second telegraph
            "circle",
            Map.of("radius", 5.0),
            "ENTITY_ENDER_DRAGON_GROWL",
            "§cFireball incoming!"
        );
        
        BossAbility fireballAbility = new BossAbility(
            "fireball_barrage",
            "Fireball Barrage",
            BossAbility.AbilityType.PROJECTILE,
            new AbilityTiming(
                AbilityTiming.TimingType.INTERVAL,
                5000, // 5 second interval
                1000, // 1 second delay
                -1,   // unlimited uses
                List.of()
            ),
            Map.of(
                "projectileType", "FIREBALL",
                "damage", 12.0,
                "speed", 1.5,
                "count", 3
            ),
            telegraph
        );
        
        BossPhase phase1 = new BossPhase(
            "phase1",
            "Flame Guardian",
            new PhaseTrigger(PhaseTrigger.TriggerType.HEALTH_PERCENT, 100.0, null),
            List.of(fireballAbility),
            List.of("fire_immunity"),
            Map.of("weather", "CLEAR"),
            "§c§lThe Flame Guardian awakens!"
        );
        
        BossPhase phase2 = new BossPhase(
            "phase2",
            "Enraged Flame Guardian",
            new PhaseTrigger(PhaseTrigger.TriggerType.HEALTH_PERCENT, 50.0, null),
            List.of(fireballAbility, createAoEAbility()),
            List.of("fire_immunity", "increased_damage"),
            Map.of("weather", "STORM"),
            "§c§lThe Flame Guardian becomes enraged!"
        );
        
        BossArena arena = new BossArena(
            "circular",
            Map.of("radius", 20.0),
            List.of("fire_floor"),
            Map.of("biome", "NETHER_WASTES")
        );
        
        BossDef flameBoss = new BossDef(
            "flame_guardian",
            "§c§lFlame Guardian",
            EntityType.BLAZE,
            attributes,
            List.of(phase1, phase2),
            Map.of("helmet", "GOLDEN_HELMET"),
            List.of("FIRE", "LAVA"),
            arena,
            Map.of("difficulty", "NORMAL")
        );
        
        saveBossDefinition(flameBoss);
    }
    
    /**
     * Creates the second sample boss.
     */
    private void createSampleBoss2() {
        BossAttributes attributes = new BossAttributes(
            300.0, // maxHealth
            20.0,  // attackDamage
            0.25,  // movementSpeed
            8.0,   // armor
            3.0,   // armorToughness
            1.0,   // knockbackResistance
            Map.of("PHYSICAL", 0.3, "PROJECTILE", 0.5)
        );
        
        BossPhase phase1 = new BossPhase(
            "phase1",
            "Stone Colossus",
            new PhaseTrigger(PhaseTrigger.TriggerType.HEALTH_PERCENT, 100.0, null),
            List.of(createChargeAbility()),
            List.of("earthquake"),
            Map.of(),
            "§8§lThe Stone Colossus rises!"
        );
        
        BossArena arena = new BossArena(
            "rectangular",
            Map.of("width", 30.0, "height", 30.0),
            List.of("stone_pillars"),
            Map.of("biome", "STONE_SHORE")
        );
        
        BossDef stoneBoss = new BossDef(
            "stone_colossus",
            "§8§lStone Colossus",
            EntityType.IRON_GOLEM,
            attributes,
            List.of(phase1),
            Map.of(),
            List.of("FALL"),
            arena,
            Map.of("difficulty", "HARD")
        );
        
        saveBossDefinition(stoneBoss);
    }
    
    /**
     * Creates the third sample boss.
     */
    private void createSampleBoss3() {
        BossAttributes attributes = new BossAttributes(
            150.0, // maxHealth
            18.0,  // attackDamage
            0.4,   // movementSpeed
            3.0,   // armor
            1.0,   // armorToughness
            0.5,   // knockbackResistance
            Map.of("MAGIC", 0.8)
        );
        
        BossPhase phase1 = new BossPhase(
            "phase1",
            "Shadow Assassin",
            new PhaseTrigger(PhaseTrigger.TriggerType.HEALTH_PERCENT, 100.0, null),
            List.of(createTeleportAbility(), createSummonAbility()),
            List.of("invisibility"),
            Map.of("lighting", "DARK"),
            "§5§lThe Shadow Assassin emerges from darkness!"
        );
        
        BossArena arena = new BossArena(
            "maze",
            Map.of("complexity", 3),
            List.of("shadow_zones"),
            Map.of("biome", "DARK_FOREST")
        );
        
        BossDef shadowBoss = new BossDef(
            "shadow_assassin",
            "§5§lShadow Assassin",
            EntityType.WITHER_SKELETON,
            attributes,
            List.of(phase1),
            Map.of("weapon", "NETHERITE_SWORD"),
            List.of("WITHER"),
            arena,
            Map.of("difficulty", "MYTHIC")
        );
        
        saveBossDefinition(shadowBoss);
    }
    
    /**
     * Creates an AoE ability.
     */
    private BossAbility createAoEAbility() {
        return new BossAbility(
            "flame_burst",
            "Flame Burst",
            BossAbility.AbilityType.AOE_DAMAGE,
            new AbilityTiming(
                AbilityTiming.TimingType.INTERVAL,
                8000, // 8 second interval
                2000, // 2 second delay
                -1,   // unlimited uses
                List.of()
            ),
            Map.of(
                "damage", 18.0,
                "radius", 6.0,
                "damageType", "FIRE"
            ),
            new Telegraph(
                3000, // 3 second telegraph
                "circle",
                Map.of("radius", 6.0),
                "ENTITY_GHAST_WARN",
                "§c§lFLAME BURST!"
            )
        );
    }
    
    /**
     * Creates a charge ability.
     */
    private BossAbility createChargeAbility() {
        return new BossAbility(
            "stone_charge",
            "Stone Charge",
            BossAbility.AbilityType.CHARGE,
            new AbilityTiming(
                AbilityTiming.TimingType.INTERVAL,
                10000, // 10 second interval
                1500,  // 1.5 second delay
                -1,    // unlimited uses
                List.of()
            ),
            Map.of(
                "damage", 25.0,
                "speed", 2.5,
                "range", 20.0
            ),
            new Telegraph(
                2500, // 2.5 second telegraph
                "line",
                Map.of("length", 20.0),
                "ENTITY_RAVAGER_ROAR",
                "§8§lCHARGE INCOMING!"
            )
        );
    }
    
    /**
     * Creates a teleport ability.
     */
    private BossAbility createTeleportAbility() {
        return new BossAbility(
            "shadow_step",
            "Shadow Step",
            BossAbility.AbilityType.TELEPORT,
            new AbilityTiming(
                AbilityTiming.TimingType.INTERVAL,
                6000, // 6 second interval
                500,  // 0.5 second delay
                -1,   // unlimited uses
                List.of()
            ),
            Map.of(
                "type", "PLAYER",
                "range", 15.0
            ),
            null // No telegraph for stealth
        );
    }
    
    /**
     * Creates a summon ability.
     */
    private BossAbility createSummonAbility() {
        return new BossAbility(
            "shadow_minions",
            "Shadow Minions",
            BossAbility.AbilityType.SUMMON_MINIONS,
            new AbilityTiming(
                AbilityTiming.TimingType.INTERVAL,
                15000, // 15 second interval
                3000,  // 3 second delay
                3,     // max 3 uses
                List.of()
            ),
            Map.of(
                "minionType", "SKELETON",
                "count", 4,
                "radius", 5.0
            ),
            new Telegraph(
                4000, // 4 second telegraph
                "area",
                Map.of("width", 10.0, "height", 10.0),
                "ENTITY_WITHER_SPAWN",
                "§5§lShadows gather..."
            )
        );
    }
    
    /**
     * Saves a boss definition to file.
     */
    private void saveBossDefinition(@NotNull BossDef bossDef) {
        File bossDir = new File(plugin.getDataFolder(), "bosses");
        bossDir.mkdirs();
        
        File bossFile = new File(bossDir, bossDef.id() + ".json");
        try (FileWriter writer = new FileWriter(bossFile)) {
            gson.toJson(bossDef, writer);
            bossDefinitions.put(bossDef.id(), bossDef);
            logger.fine("Saved boss definition: " + bossDef.id());
        } catch (IOException e) {
            logger.log(Level.WARNING, "Failed to save boss definition: " + bossDef.id(), e);
        }
    }
    
    /**
     * Starts the boss update task.
     */
    private void startUpdateTask() {
        updateTask = schedulerUtil.runTaskTimer(() -> {
            for (BossInstance boss : activeBosses.values()) {
                try {
                    boss.update();
                    updateBossAbilities(boss);
                } catch (Exception e) {
                    logger.log(Level.WARNING, "Error updating boss: " + boss.getInstanceId(), e);
                }
            }
        }, 20L, 20L); // Update every second
    }
    
    /**
     * Updates boss abilities.
     */
    private void updateBossAbilities(@NotNull BossInstance boss) {
        if (boss.getState() != BossInstance.BossState.ACTIVE) {
            return;
        }
        
        for (BossAbility ability : boss.getCurrentPhase().abilities()) {
            if (shouldExecuteAbility(boss, ability)) {
                executeAbility(boss, ability);
            }
        }
    }
    
    /**
     * Checks if an ability should be executed.
     */
    private boolean shouldExecuteAbility(@NotNull BossInstance boss, @NotNull BossAbility ability) {
        // This logic would check cooldowns, conditions, etc.
        // For now, simplified implementation
        return !boss.getActiveAbilities().contains(ability.id());
    }
    
    /**
     * Executes a boss ability.
     */
    private void executeAbility(@NotNull BossInstance boss, @NotNull BossAbility ability) {
        Class<? extends BossAbilityExecutor> executorClass = abilityExecutors.get(ability.type().name());
        if (executorClass == null) {
            logger.warning("No executor found for ability type: " + ability.type());
            return;
        }
        
        try {
            BossAbilityExecutor executor = executorClass
                .getDeclaredConstructor(BossInstance.class, BossAbility.class)
                .newInstance(boss, ability);
            
            executor.execute().exceptionally(throwable -> {
                logger.log(Level.WARNING, "Error executing ability: " + ability.id(), throwable);
                return null;
            });
            
        } catch (Exception e) {
            logger.log(Level.WARNING, "Failed to create ability executor: " + ability.id(), e);
        }
    }
    
    /**
     * Spawns a boss at the specified location.
     */
    @NotNull
    public CompletableFuture<BossInstance> spawnBoss(@NotNull String bossId, @NotNull Location location, 
                                                    @NotNull String instanceId) {
        CompletableFuture<BossInstance> future = new CompletableFuture<>();
        
        BossDef definition = bossDefinitions.get(bossId);
        if (definition == null) {
            future.completeExceptionally(new IllegalArgumentException("Unknown boss: " + bossId));
            return future;
        }
        
        schedulerUtil.runTask(() -> {
            try {
                // Spawn entity
                LivingEntity entity = (LivingEntity) location.getWorld().spawnEntity(location, definition.entityType());
                
                // Apply boss attributes
                applyBossAttributes(entity, definition);
                
                // Create boss instance
                BossInstance bossInstance = new BossInstance(instanceId, definition, entity, location);
                
                // Register boss
                activeBosses.put(instanceId, bossInstance);
                entityToBoss.put(entity.getUniqueId(), instanceId);
                
                // Set boss state to active
                bossInstance.transitionToPhase(definition.getFirstPhase());
                
                logger.info("Spawned boss: " + bossId + " at " + location);
                future.complete(bossInstance);
                
            } catch (Exception e) {
                future.completeExceptionally(e);
            }
        });
        
        return future;
    }
    
    /**
     * Applies boss attributes to an entity.
     */
    private void applyBossAttributes(@NotNull LivingEntity entity, @NotNull BossDef definition) {
        BossAttributes attributes = definition.attributes();
        
        // Set health
        if (entity.getAttribute(Attribute.MAX_HEALTH) != null) {
            entity.getAttribute(Attribute.MAX_HEALTH).setBaseValue(attributes.maxHealth());
            entity.setHealth(attributes.maxHealth());
        }
        
        // Set other attributes
        if (entity.getAttribute(Attribute.ATTACK_DAMAGE) != null) {
            entity.getAttribute(Attribute.ATTACK_DAMAGE).setBaseValue(attributes.attackDamage());
        }
        
        if (entity.getAttribute(Attribute.MOVEMENT_SPEED) != null) {
            entity.getAttribute(Attribute.MOVEMENT_SPEED).setBaseValue(attributes.movementSpeed());
        }
        
        if (entity.getAttribute(Attribute.ARMOR) != null) {
            entity.getAttribute(Attribute.ARMOR).setBaseValue(attributes.armor());
        }
        
        if (entity.getAttribute(Attribute.ARMOR_TOUGHNESS) != null) {
            entity.getAttribute(Attribute.ARMOR_TOUGHNESS).setBaseValue(attributes.armorToughness());
        }
        
        if (entity.getAttribute(Attribute.KNOCKBACK_RESISTANCE) != null) {
            entity.getAttribute(Attribute.KNOCKBACK_RESISTANCE).setBaseValue(attributes.knockbackResistance());
        }
        
        // Set custom name using Adventure API
        entity.customName(Component.text(definition.displayName()));
        entity.setCustomNameVisible(true);
        
        // Apply equipment
        applyBossEquipment(entity, definition.equipment());
    }
    
    /**
     * Applies equipment to a boss entity.
     */
    private void applyBossEquipment(@NotNull LivingEntity entity, @NotNull Map<String, Object> equipment) {
        // Implementation would depend on equipment format
        // This is a simplified version
        for (Map.Entry<String, Object> entry : equipment.entrySet()) {
            String slot = entry.getKey();
            String item = entry.getValue().toString();
            
            try {
                org.bukkit.Material material = org.bukkit.Material.valueOf(item.toUpperCase());
                org.bukkit.inventory.ItemStack itemStack = new org.bukkit.inventory.ItemStack(material);
                
                switch (slot.toLowerCase()) {
                    case "helmet" -> entity.getEquipment().setHelmet(itemStack);
                    case "chestplate" -> entity.getEquipment().setChestplate(itemStack);
                    case "leggings" -> entity.getEquipment().setLeggings(itemStack);
                    case "boots" -> entity.getEquipment().setBoots(itemStack);
                    case "weapon", "mainhand" -> entity.getEquipment().setItemInMainHand(itemStack);
                    case "offhand" -> entity.getEquipment().setItemInOffHand(itemStack);
                }
                
            } catch (IllegalArgumentException e) {
                logger.warning("Invalid equipment material: " + item);
            }
        }
    }
    
    /**
     * Gets a boss instance by ID.
     */
    @Nullable
    public BossInstance getBoss(@NotNull String instanceId) {
        return activeBosses.get(instanceId);
    }
    
    /**
     * Gets a boss instance by entity.
     */
    @Nullable
    public BossInstance getBossByEntity(@NotNull LivingEntity entity) {
        String instanceId = entityToBoss.get(entity.getUniqueId());
        return instanceId != null ? activeBosses.get(instanceId) : null;
    }
    
    /**
     * Gets all active boss instances.
     */
    @NotNull
    public Collection<BossInstance> getActiveBosses() {
        return Collections.unmodifiableCollection(activeBosses.values());
    }
    
    /**
     * Gets a boss definition by ID.
     */
    @Nullable
    public BossDef getBossDefinition(@NotNull String bossId) {
        return bossDefinitions.get(bossId);
    }
    
    /**
     * Gets all boss definitions.
     */
    @NotNull
    public Collection<BossDef> getBossDefinitions() {
        return Collections.unmodifiableCollection(bossDefinitions.values());
    }
    
    /**
     * Removes a boss instance.
     */
    public void removeBoss(@NotNull String instanceId) {
        BossInstance boss = activeBosses.remove(instanceId);
        if (boss != null) {
            entityToBoss.remove(boss.getEntity().getUniqueId());
            boss.cleanup();
            logger.info("Removed boss instance: " + instanceId);
        }
    }
    
    @EventHandler
    public void onEntityDeath(@NotNull EntityDeathEvent event) {
        LivingEntity entity = event.getEntity();
        BossInstance boss = getBossByEntity(entity);
        
        if (boss != null) {
            boss.defeat();
            
            // Schedule cleanup
            schedulerUtil.runTaskLater(() -> {
                removeBoss(boss.getInstanceId());
            }, 100L); // 5 second delay
        }
    }
    
    @EventHandler
    public void onEntityDamage(@NotNull EntityDamageByEntityEvent event) {
        if (!(event.getEntity() instanceof LivingEntity target)) return;
        if (!(event.getDamager() instanceof Player player)) return;
        
        BossInstance boss = getBossByEntity(target);
        if (boss != null) {
            // Add player to boss fight
            boss.addParticipant(player);
            
            // Track damage
            BossInstance.BossPlayerData playerData = boss.getPlayerData(player.getUniqueId());
            if (playerData != null) {
                playerData.addDamageDealt(event.getFinalDamage());
            }
        }
    }
    
    @EventHandler
    public void onPlayerJoin(@NotNull PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // Add player to nearby boss fights
        for (BossInstance boss : activeBosses.values()) {
            if (boss.getEntity().getLocation().distance(player.getLocation()) <= 50) {
                boss.addParticipant(player);
            }
        }
    }
    
    @EventHandler
    public void onPlayerQuit(@NotNull PlayerQuitEvent event) {
        Player player = event.getPlayer();
        
        // Remove player from all boss fights
        for (BossInstance boss : activeBosses.values()) {
            boss.removeParticipant(player);
        }
    }
    
    /**
     * Shuts down the boss service.
     */
    public void shutdown() {
        logger.info("BossService shutting down");
        
        // Cancel update task
        if (updateTask != null) {
            updateTask.cancel();
        }
        
        // Cleanup all active bosses
        for (BossInstance boss : activeBosses.values()) {
            boss.cleanup();
        }
        
        activeBosses.clear();
        entityToBoss.clear();
        bossDefinitions.clear();
    }
}

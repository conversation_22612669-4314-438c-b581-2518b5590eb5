package com.ultimatedungeon.udx.boss.ability;

import com.ultimatedungeon.udx.boss.BossInstance;
import com.ultimatedungeon.udx.boss.BossDef.BossAbility;
import net.kyori.adventure.text.Component;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.entity.Projectile;
import org.bukkit.util.Vector;
import org.jetbrains.annotations.NotNull;

import java.util.Collection;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Collection of basic boss ability implementations.
 * 
 * <p>Provides concrete implementations of common boss abilities
 * such as projectiles, AoE attacks, summoning, teleportation, etc.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class BasicAbilities {
    
    /**
     * Projectile ability that launches projectiles at players.
     */
    public static class ProjectileAbility extends BossAbilityExecutor {
        
        public ProjectileAbility(@NotNull BossInstance bossInstance, @NotNull BossAbility ability) {
            super(bossInstance, ability);
        }
        
        @Override
        @NotNull
        protected CompletableFuture<Void> executeAbility() {
            CompletableFuture<Void> future = new CompletableFuture<>();
            
            Map<String, Object> params = ability.parameters();
            String projectileType = params.getOrDefault("projectileType", "FIREBALL").toString();
            double damage = ((Number) params.getOrDefault("damage", 10.0)).doubleValue();
            double speed = ((Number) params.getOrDefault("speed", 1.0)).doubleValue();
            int count = ((Number) params.getOrDefault("count", 1)).intValue();
            
            Location bossLocation = bossInstance.getEntity().getLocation();
            Collection<Player> targets = bossInstance.getParticipantPlayers();
            
            try {
                EntityType entityType = EntityType.valueOf(projectileType.toUpperCase());
                
                for (int i = 0; i < count; i++) {
                    for (Player target : targets) {
                        if (target.getLocation().distance(bossLocation) <= 50) {
                            launchProjectile(bossLocation, target.getLocation(), entityType, speed, damage);
                        }
                    }
                }
                
                future.complete(null);
            } catch (Exception e) {
                future.completeExceptionally(e);
            }
            
            return future;
        }
        
        private void launchProjectile(@NotNull Location from, @NotNull Location to, 
                                    @NotNull EntityType type, double speed, double damage) {
            Vector direction = to.toVector().subtract(from.toVector()).normalize();
            
            Projectile projectile = (Projectile) from.getWorld().spawnEntity(from, type);
            projectile.setVelocity(direction.multiply(speed));
            
            // Store damage in metadata for damage handling
            projectile.getPersistentDataContainer().set(
                bossInstance.getPlugin().getNamespacedKey("boss_damage"),
                org.bukkit.persistence.PersistentDataType.DOUBLE,
                damage
            );
        }
    }
    
    /**
     * AoE damage ability that damages players in an area.
     */
    public static class AoEDamageAbility extends BossAbilityExecutor {
        
        public AoEDamageAbility(@NotNull BossInstance bossInstance, @NotNull BossAbility ability) {
            super(bossInstance, ability);
        }
        
        @Override
        @NotNull
        protected CompletableFuture<Void> executeAbility() {
            CompletableFuture<Void> future = new CompletableFuture<>();
            
            Map<String, Object> params = ability.parameters();
            double damage = ((Number) params.getOrDefault("damage", 15.0)).doubleValue();
            double radius = ((Number) params.getOrDefault("radius", 5.0)).doubleValue();
            String damageType = params.getOrDefault("damageType", "MAGIC").toString();
            
            Location bossLocation = bossInstance.getEntity().getLocation();
            
            // Create explosion effect
            bossLocation.getWorld().spawnParticle(
                Particle.EXPLOSION,
                bossLocation,
                10,
                radius / 2, radius / 2, radius / 2,
                0.1
            );
            
            // Damage nearby players
            Collection<Player> targets = bossInstance.getParticipantPlayers();
            for (Player target : targets) {
                if (target.getLocation().distance(bossLocation) <= radius) {
                    target.damage(damage, bossInstance.getEntity());
                    
                    // Apply knockback
                    Vector knockback = target.getLocation().toVector()
                        .subtract(bossLocation.toVector())
                        .normalize()
                        .multiply(1.5);
                    target.setVelocity(knockback);
                }
            }
            
            future.complete(null);
            return future;
        }
    }
    
    /**
     * Summon minions ability that spawns helper entities.
     */
    public static class SummonMinionsAbility extends BossAbilityExecutor {
        
        public SummonMinionsAbility(@NotNull BossInstance bossInstance, @NotNull BossAbility ability) {
            super(bossInstance, ability);
        }
        
        @Override
        @NotNull
        protected CompletableFuture<Void> executeAbility() {
            CompletableFuture<Void> future = new CompletableFuture<>();
            
            Map<String, Object> params = ability.parameters();
            String minionType = params.getOrDefault("minionType", "SKELETON").toString();
            int count = ((Number) params.getOrDefault("count", 3)).intValue();
            double radius = ((Number) params.getOrDefault("radius", 3.0)).doubleValue();
            
            Location bossLocation = bossInstance.getEntity().getLocation();
            
            try {
                EntityType entityType = EntityType.valueOf(minionType.toUpperCase());
                
                for (int i = 0; i < count; i++) {
                    // Random position around boss
                    double angle = Math.random() * 2 * Math.PI;
                    double distance = Math.random() * radius;
                    
                    double x = bossLocation.getX() + distance * Math.cos(angle);
                    double z = bossLocation.getZ() + distance * Math.sin(angle);
                    Location spawnLocation = new Location(bossLocation.getWorld(), x, bossLocation.getY(), z);
                    
                    // Spawn minion
                    LivingEntity minion = (LivingEntity) bossLocation.getWorld().spawnEntity(spawnLocation, entityType);
                    minion.customName(Component.text("§c" + bossInstance.getDefinition().displayName() + "'s Minion"));
                    minion.setCustomNameVisible(true);
                    
                    // Mark as boss minion
                    minion.getPersistentDataContainer().set(
                        bossInstance.getPlugin().getNamespacedKey("boss_minion"),
                        org.bukkit.persistence.PersistentDataType.STRING,
                        bossInstance.getInstanceId()
                    );
                    
                    // Spawn effect
                    spawnLocation.getWorld().spawnParticle(
                        Particle.SMOKE,
                        spawnLocation,
                        20,
                        1.0, 1.0, 1.0,
                        0.1
                    );
                }
                
                future.complete(null);
            } catch (Exception e) {
                future.completeExceptionally(e);
            }
            
            return future;
        }
    }
    
    /**
     * Teleport ability that moves the boss to a new location.
     */
    public static class TeleportAbility extends BossAbilityExecutor {
        
        public TeleportAbility(@NotNull BossInstance bossInstance, @NotNull BossAbility ability) {
            super(bossInstance, ability);
        }
        
        @Override
        @NotNull
        protected CompletableFuture<Void> executeAbility() {
            CompletableFuture<Void> future = new CompletableFuture<>();
            
            Map<String, Object> params = ability.parameters();
            String teleportType = params.getOrDefault("type", "RANDOM").toString();
            double range = ((Number) params.getOrDefault("range", 10.0)).doubleValue();
            
            Location currentLocation = bossInstance.getEntity().getLocation();
            Location targetLocation = null;
            
            switch (teleportType.toUpperCase()) {
                case "PLAYER" -> {
                    // Teleport near a random player
                    Collection<Player> players = bossInstance.getParticipantPlayers();
                    if (!players.isEmpty()) {
                        Player randomPlayer = players.iterator().next();
                        targetLocation = randomPlayer.getLocation().clone();
                        
                        // Offset slightly to avoid telefragging
                        targetLocation.add(Math.random() * 4 - 2, 0, Math.random() * 4 - 2);
                    }
                }
                case "RANDOM" -> {
                    // Random location within range
                    double angle = Math.random() * 2 * Math.PI;
                    double distance = Math.random() * range;
                    
                    double x = currentLocation.getX() + distance * Math.cos(angle);
                    double z = currentLocation.getZ() + distance * Math.sin(angle);
                    targetLocation = new Location(currentLocation.getWorld(), x, currentLocation.getY(), z);
                }
            }
            
            if (targetLocation != null) {
                // Teleport effects
                currentLocation.getWorld().spawnParticle(
                    Particle.PORTAL,
                    currentLocation,
                    50,
                    1.0, 1.0, 1.0,
                    0.5
                );
                
                bossInstance.getEntity().teleport(targetLocation);
                
                targetLocation.getWorld().spawnParticle(
                    Particle.PORTAL,
                    targetLocation,
                    50,
                    1.0, 1.0, 1.0,
                    0.5
                );
            }
            
            future.complete(null);
            return future;
        }
    }
    
    /**
     * Charge ability that makes the boss charge at a target.
     */
    public static class ChargeAbility extends BossAbilityExecutor {
        
        public ChargeAbility(@NotNull BossInstance bossInstance, @NotNull BossAbility ability) {
            super(bossInstance, ability);
        }
        
        @Override
        @NotNull
        protected CompletableFuture<Void> executeAbility() {
            CompletableFuture<Void> future = new CompletableFuture<>();
            
            Map<String, Object> params = ability.parameters();
            double damage = ((Number) params.getOrDefault("damage", 20.0)).doubleValue();
            double speed = ((Number) params.getOrDefault("speed", 2.0)).doubleValue();
            double range = ((Number) params.getOrDefault("range", 15.0)).doubleValue();
            
            // Find nearest player
            Collection<Player> players = bossInstance.getParticipantPlayers();
            Player target = null;
            double nearestDistance = Double.MAX_VALUE;
            
            Location bossLocation = bossInstance.getEntity().getLocation();
            for (Player player : players) {
                double distance = player.getLocation().distance(bossLocation);
                if (distance < nearestDistance && distance <= range) {
                    nearestDistance = distance;
                    target = player;
                }
            }
            
            if (target != null) {
                Vector direction = target.getLocation().toVector()
                    .subtract(bossLocation.toVector())
                    .normalize()
                    .multiply(speed);
                
                bossInstance.getEntity().setVelocity(direction);
                
                // Schedule damage check
                Player finalTarget = target;
                bossInstance.getPlugin().getServer().getScheduler().runTaskLater(
                    bossInstance.getPlugin(),
                    () -> {
                        if (finalTarget.getLocation().distance(bossInstance.getEntity().getLocation()) <= 3.0) {
                            finalTarget.damage(damage, bossInstance.getEntity());
                            
                            // Knockback effect
                            Vector knockback = finalTarget.getLocation().toVector()
                                .subtract(bossInstance.getEntity().getLocation().toVector())
                                .normalize()
                                .multiply(2.0);
                            finalTarget.setVelocity(knockback);
                        }
                        future.complete(null);
                    },
                    20L // 1 second delay
                );
            } else {
                future.complete(null);
            }
            
            return future;
        }
    }
    
    /**
     * Heal ability that restores the boss's health.
     */
    public static class HealAbility extends BossAbilityExecutor {
        
        public HealAbility(@NotNull BossInstance bossInstance, @NotNull BossAbility ability) {
            super(bossInstance, ability);
        }
        
        @Override
        @NotNull
        protected CompletableFuture<Void> executeAbility() {
            CompletableFuture<Void> future = new CompletableFuture<>();
            
            Map<String, Object> params = ability.parameters();
            double healAmount = ((Number) params.getOrDefault("amount", 50.0)).doubleValue();
            boolean percentage = (Boolean) params.getOrDefault("percentage", false);
            
            LivingEntity boss = bossInstance.getEntity();
            double currentHealth = boss.getHealth();
            double maxHealth = boss.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
            
            double actualHealAmount;
            if (percentage) {
                actualHealAmount = maxHealth * (healAmount / 100.0);
            } else {
                actualHealAmount = healAmount;
            }
            
            double newHealth = Math.min(currentHealth + actualHealAmount, maxHealth);
            boss.setHealth(newHealth);
            
            // Healing effects
            Location bossLocation = boss.getLocation();
            bossLocation.getWorld().spawnParticle(
                Particle.HEART,
                bossLocation.clone().add(0, 2, 0),
                10,
                1.0, 1.0, 1.0,
                0.1
            );
            
            future.complete(null);
            return future;
        }
    }
}

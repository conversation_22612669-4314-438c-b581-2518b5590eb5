package com.ultimatedungeon.udx.util;

import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.jetbrains.annotations.NotNull;

import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.BooleanSupplier;
import java.util.logging.Logger;

/**
 * Manages tick budgets to prevent lag spikes from expensive operations.
 * 
 * <p>This class allows spreading expensive operations across multiple ticks
 * to maintain server performance. Operations are queued and executed within
 * a configurable time budget per tick.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class TickBudgeter {
    
    private final Plugin plugin;
    private final Logger logger;
    
    // Task queue and processing
    private final ConcurrentLinkedQueue<BudgetedTask> taskQueue;
    private BukkitTask processingTask;
    private final AtomicBoolean isProcessing;
    
    // Budget configuration
    private volatile long tickBudgetNanos;
    private volatile boolean adaptiveBudget;
    
    // Performance tracking
    private final AtomicLong totalTasksProcessed;
    private final AtomicLong totalTimeSpent;
    private final AtomicLong ticksWithBudgetExceeded;
    
    // Default configuration
    private static final long DEFAULT_BUDGET_NANOS = 6_000_000L; // 6ms per tick
    private static final long MIN_BUDGET_NANOS = 1_000_000L; // 1ms minimum
    private static final long MAX_BUDGET_NANOS = 15_000_000L; // 15ms maximum
    private static final double TPS_THRESHOLD = 18.0; // Reduce budget if TPS drops below this
    
    public TickBudgeter(@NotNull Plugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        
        this.taskQueue = new ConcurrentLinkedQueue<>();
        this.isProcessing = new AtomicBoolean(false);
        
        this.tickBudgetNanos = DEFAULT_BUDGET_NANOS;
        this.adaptiveBudget = true;
        
        this.totalTasksProcessed = new AtomicLong(0);
        this.totalTimeSpent = new AtomicLong(0);
        this.ticksWithBudgetExceeded = new AtomicLong(0);
        
        startProcessing();
    }
    
    /**
     * Submits a task to be executed within the tick budget.
     * 
     * @param name Task name for debugging
     * @param task The task to execute
     * @param priority Task priority (higher = more important)
     */
    public void submitTask(@NotNull String name, @NotNull Runnable task, int priority) {
        submitTask(name, () -> {
            task.run();
            return true; // Single execution
        }, priority);
    }
    
    /**
     * Submits a task that may need multiple ticks to complete.
     * 
     * @param name Task name for debugging
     * @param task The task to execute (returns true when complete)
     * @param priority Task priority (higher = more important)
     */
    public void submitTask(@NotNull String name, @NotNull BooleanSupplier task, int priority) {
        BudgetedTask budgetedTask = new BudgetedTask(name, task, priority, System.currentTimeMillis());
        
        // Insert in priority order
        if (taskQueue.isEmpty() || priority <= 0) {
            taskQueue.offer(budgetedTask);
        } else {
            // Find insertion point for priority
            ConcurrentLinkedQueue<BudgetedTask> tempQueue = new ConcurrentLinkedQueue<>();
            boolean inserted = false;
            
            BudgetedTask existing;
            while ((existing = taskQueue.poll()) != null) {
                if (!inserted && priority > existing.priority) {
                    tempQueue.offer(budgetedTask);
                    inserted = true;
                }
                tempQueue.offer(existing);
            }
            
            if (!inserted) {
                tempQueue.offer(budgetedTask);
            }
            
            // Put everything back
            taskQueue.addAll(tempQueue);
        }
    }
    
    /**
     * Submits a high-priority task.
     * 
     * @param name Task name for debugging
     * @param task The task to execute
     */
    public void submitHighPriorityTask(@NotNull String name, @NotNull Runnable task) {
        submitTask(name, task, 100);
    }
    
    /**
     * Submits a low-priority task.
     * 
     * @param name Task name for debugging
     * @param task The task to execute
     */
    public void submitLowPriorityTask(@NotNull String name, @NotNull Runnable task) {
        submitTask(name, task, -10);
    }
    
    /**
     * Sets the tick budget in milliseconds.
     * 
     * @param budgetMs Budget in milliseconds
     */
    public void setTickBudget(double budgetMs) {
        long budgetNanos = (long) (budgetMs * 1_000_000);
        this.tickBudgetNanos = Math.max(MIN_BUDGET_NANOS, Math.min(MAX_BUDGET_NANOS, budgetNanos));
        
        logger.info(String.format("Tick budget set to %.2fms", this.tickBudgetNanos / 1_000_000.0));
    }
    
    /**
     * Gets the current tick budget in milliseconds.
     */
    public double getTickBudget() {
        return tickBudgetNanos / 1_000_000.0;
    }
    
    /**
     * Enables or disables adaptive budgeting.
     * 
     * @param adaptive True to enable adaptive budgeting
     */
    public void setAdaptiveBudget(boolean adaptive) {
        this.adaptiveBudget = adaptive;
        logger.info("Adaptive budgeting " + (adaptive ? "enabled" : "disabled"));
    }
    
    /**
     * Gets the number of queued tasks.
     */
    public int getQueuedTaskCount() {
        return taskQueue.size();
    }
    
    /**
     * Gets the total number of tasks processed.
     */
    public long getTotalTasksProcessed() {
        return totalTasksProcessed.get();
    }
    
    /**
     * Gets the total time spent processing tasks in milliseconds.
     */
    public double getTotalTimeSpent() {
        return totalTimeSpent.get() / 1_000_000.0;
    }
    
    /**
     * Gets the number of ticks where budget was exceeded.
     */
    public long getTicksWithBudgetExceeded() {
        return ticksWithBudgetExceeded.get();
    }
    
    /**
     * Gets the average processing time per task in milliseconds.
     */
    public double getAverageTaskTime() {
        long tasks = totalTasksProcessed.get();
        if (tasks == 0) return 0.0;
        
        return (totalTimeSpent.get() / 1_000_000.0) / tasks;
    }
    
    /**
     * Clears all queued tasks.
     */
    public void clearQueue() {
        int cleared = taskQueue.size();
        taskQueue.clear();
        logger.info(String.format("Cleared %d queued tasks", cleared));
    }
    
    /**
     * Forces processing of all queued tasks (ignoring budget).
     */
    public void forceProcessAll() {
        logger.info("Force processing all queued tasks...");
        
        int processed = 0;
        long startTime = System.nanoTime();
        
        BudgetedTask task;
        while ((task = taskQueue.poll()) != null) {
            try {
                boolean completed = task.task.getAsBoolean();
                if (!completed) {
                    // Task needs more time, but we're forcing completion
                    logger.warning("Task '" + task.name + "' was not completed during force processing");
                }
                processed++;
            } catch (Exception e) {
                logger.warning("Error during force processing of task '" + task.name + "': " + e.getMessage());
            }
        }
        
        long totalTime = System.nanoTime() - startTime;
        logger.info(String.format("Force processed %d tasks in %.2fms", 
            processed, totalTime / 1_000_000.0));
    }
    
    /**
     * Starts the task processing system.
     */
    private void startProcessing() {
        if (isProcessing.get()) {
            return;
        }
        
        isProcessing.set(true);
        processingTask = new BukkitRunnable() {
            @Override
            public void run() {
                processTasks();
            }
        }.runTaskTimer(plugin, 1L, 1L); // Run every tick
        
        logger.info("Tick budgeter started");
    }
    
    /**
     * Processes tasks within the current tick budget.
     */
    private void processTasks() {
        if (taskQueue.isEmpty()) {
            return;
        }
        
        long tickStartTime = System.nanoTime();
        long currentBudget = getCurrentBudget();
        int tasksProcessed = 0;
        boolean budgetExceeded = false;
        
        while (!taskQueue.isEmpty()) {
            long taskStartTime = System.nanoTime();
            long elapsedTime = taskStartTime - tickStartTime;
            
            // Check if we have budget remaining
            if (elapsedTime >= currentBudget) {
                budgetExceeded = true;
                break;
            }
            
            BudgetedTask task = taskQueue.poll();
            if (task == null) {
                break;
            }
            
            try {
                boolean completed = task.task.getAsBoolean();
                
                if (!completed) {
                    // Task needs more time, re-queue it
                    taskQueue.offer(task);
                }
                
                tasksProcessed++;
                totalTasksProcessed.incrementAndGet();
                
            } catch (Exception e) {
                logger.warning("Error processing task '" + task.name + "': " + e.getMessage());
            }
            
            long taskTime = System.nanoTime() - taskStartTime;
            totalTimeSpent.addAndGet(taskTime);
            
            // Check if this single task exceeded our remaining budget
            long newElapsedTime = System.nanoTime() - tickStartTime;
            if (newElapsedTime >= currentBudget) {
                budgetExceeded = true;
                break;
            }
        }
        
        if (budgetExceeded) {
            ticksWithBudgetExceeded.incrementAndGet();
        }
        
        // Log performance if needed
        long totalTickTime = System.nanoTime() - tickStartTime;
        if (totalTickTime > currentBudget * 1.5) { // 50% over budget
            logger.fine(String.format("Tick budget exceeded: %.2fms used, %.2fms budget, %d tasks processed",
                totalTickTime / 1_000_000.0, currentBudget / 1_000_000.0, tasksProcessed));
        }
    }
    
    /**
     * Gets the current budget, potentially adjusted for server performance.
     */
    private long getCurrentBudget() {
        if (!adaptiveBudget) {
            return tickBudgetNanos;
        }
        
        // Get server TPS (simplified - in real implementation would use server TPS)
        double currentTPS = 20.0; // Placeholder
        
        if (currentTPS < TPS_THRESHOLD) {
            // Reduce budget if TPS is low
            double reduction = (TPS_THRESHOLD - currentTPS) / TPS_THRESHOLD;
            long reducedBudget = (long) (tickBudgetNanos * (1.0 - reduction * 0.5));
            return Math.max(MIN_BUDGET_NANOS, reducedBudget);
        }
        
        return tickBudgetNanos;
    }
    
    /**
     * Stops the task processing system.
     */
    public void shutdown() {
        logger.info("Shutting down TickBudgeter...");
        
        isProcessing.set(false);
        
        if (processingTask != null) {
            processingTask.cancel();
        }
        
        // Log final statistics
        logger.info("Final tick budgeter statistics:");
        logger.info(String.format("  Tasks Processed: %d", getTotalTasksProcessed()));
        logger.info(String.format("  Total Time Spent: %.2fms", getTotalTimeSpent()));
        logger.info(String.format("  Average Task Time: %.2fms", getAverageTaskTime()));
        logger.info(String.format("  Ticks Over Budget: %d", getTicksWithBudgetExceeded()));
        logger.info(String.format("  Remaining Queued Tasks: %d", getQueuedTaskCount()));
        
        // Process remaining tasks if any
        if (!taskQueue.isEmpty()) {
            logger.info("Processing remaining tasks during shutdown...");
            forceProcessAll();
        }
        
        logger.info("TickBudgeter shutdown complete");
    }
    
    /**
     * Represents a task with budget management.
     */
    private static class BudgetedTask {
        final String name;
        final BooleanSupplier task;
        final int priority;
        final long submissionTime;
        
        BudgetedTask(@NotNull String name, @NotNull BooleanSupplier task, int priority, long submissionTime) {
            this.name = name;
            this.task = task;
            this.priority = priority;
            this.submissionTime = submissionTime;
        }
    }
}

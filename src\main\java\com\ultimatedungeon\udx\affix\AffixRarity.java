package com.ultimatedungeon.udx.affix;

/**
 * Represents the rarity of an affix.
 * 
 * <p>Affix rarity determines how often an affix appears in rotations
 * and affects the visual presentation and impact of the affix.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public enum AffixRarity {
    
    /**
     * Common affixes that appear frequently.
     */
    COMMON("Common", "§f", 1.0, 50),
    
    /**
     * Uncommon affixes with moderate frequency.
     */
    UNCOMMON("Uncommon", "§a", 1.2, 30),
    
    /**
     * Rare affixes that appear less frequently.
     */
    RARE("Rare", "§9", 1.5, 15),
    
    /**
     * Epic affixes with significant impact.
     */
    EPIC("Epic", "§5", 2.0, 4),
    
    /**
     * Legendary affixes that are very rare and powerful.
     */
    LEGENDARY("Legendary", "§6", 3.0, 1);
    
    private final String displayName;
    private final String colorCode;
    private final double rewardMultiplier;
    private final int weight;
    
    AffixRarity(String displayName, String colorCode, double rewardMultiplier, int weight) {
        this.displayName = displayName;
        this.colorCode = colorCode;
        this.rewardMultiplier = rewardMultiplier;
        this.weight = weight;
    }
    
    /**
     * Gets the display name.
     * 
     * @return the display name
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * Gets the color code.
     * 
     * @return the color code
     */
    public String getColorCode() {
        return colorCode;
    }
    
    /**
     * Gets the reward multiplier.
     * 
     * @return the reward multiplier
     */
    public double getRewardMultiplier() {
        return rewardMultiplier;
    }
    
    /**
     * Gets the selection weight for random generation.
     * 
     * @return the weight
     */
    public int getWeight() {
        return weight;
    }
    
    /**
     * Gets the formatted display name with color.
     * 
     * @return the formatted display name
     */
    public String getFormattedName() {
        return colorCode + displayName;
    }
    
    /**
     * Gets the total weight of all rarities.
     * 
     * @return the total weight
     */
    public static int getTotalWeight() {
        int total = 0;
        for (AffixRarity rarity : values()) {
            total += rarity.weight;
        }
        return total;
    }
    
    /**
     * Gets a random rarity based on weights.
     * 
     * @param random the random value (0-totalWeight)
     * @return the selected rarity
     */
    public static AffixRarity getRandomRarity(int random) {
        int current = 0;
        for (AffixRarity rarity : values()) {
            current += rarity.weight;
            if (random < current) {
                return rarity;
            }
        }
        return COMMON; // Fallback
    }
}

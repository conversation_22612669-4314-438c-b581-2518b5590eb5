package com.ultimatedungeon.udx.util;

import net.kyori.adventure.text.Component;
import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Utility class for building ItemStacks with a fluent API.
 * 
 * <p>This class provides a convenient way to create and customize ItemStacks
 * using method chaining. It supports setting display names, lore, enchantments,
 * item flags, and other properties.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class ItemBuilder {
    
    private final ItemStack itemStack;
    private final ItemMeta itemMeta;
    
    /**
     * Creates a new ItemBuilder with the specified material.
     * 
     * @param material the material for the item
     */
    public ItemBuilder(@NotNull Material material) {
        this(material, 1);
    }
    
    /**
     * Creates a new ItemBuilder with the specified material and amount.
     * 
     * @param material the material for the item
     * @param amount the amount of items
     */
    public ItemBuilder(@NotNull Material material, int amount) {
        this.itemStack = new ItemStack(material, amount);
        this.itemMeta = itemStack.getItemMeta();
    }
    
    /**
     * Creates a new ItemBuilder from an existing ItemStack.
     * 
     * @param itemStack the existing ItemStack to copy
     */
    public ItemBuilder(@NotNull ItemStack itemStack) {
        this.itemStack = itemStack.clone();
        this.itemMeta = this.itemStack.getItemMeta();
    }
    
    /**
     * Sets the display name of the item.
     * 
     * @param name the display name component
     * @return this ItemBuilder for chaining
     */
    public ItemBuilder name(@NotNull Component name) {
        if (itemMeta != null) {
            itemMeta.displayName(name);
        }
        return this;
    }
    
    /**
     * Sets the lore of the item.
     * 
     * @param lore the lore components
     * @return this ItemBuilder for chaining
     */
    public ItemBuilder lore(@NotNull Component... lore) {
        return lore(Arrays.asList(lore));
    }
    
    /**
     * Sets the lore of the item.
     * 
     * @param lore the list of lore components
     * @return this ItemBuilder for chaining
     */
    public ItemBuilder lore(@NotNull List<Component> lore) {
        if (itemMeta != null) {
            itemMeta.lore(new ArrayList<>(lore));
        }
        return this;
    }
    
    /**
     * Adds lines to the existing lore.
     * 
     * @param lore the lore components to add
     * @return this ItemBuilder for chaining
     */
    public ItemBuilder addLore(@NotNull Component... lore) {
        return addLore(Arrays.asList(lore));
    }
    
    /**
     * Adds lines to the existing lore.
     * 
     * @param lore the list of lore components to add
     * @return this ItemBuilder for chaining
     */
    public ItemBuilder addLore(@NotNull List<Component> lore) {
        if (itemMeta != null) {
            List<Component> currentLore = itemMeta.lore();
            if (currentLore == null) {
                currentLore = new ArrayList<>();
            }
            currentLore.addAll(lore);
            itemMeta.lore(currentLore);
        }
        return this;
    }
    
    /**
     * Adds an enchantment to the item.
     * 
     * @param enchantment the enchantment to add
     * @param level the level of the enchantment
     * @return this ItemBuilder for chaining
     */
    public ItemBuilder enchant(@NotNull Enchantment enchantment, int level) {
        if (itemMeta != null) {
            itemMeta.addEnchant(enchantment, level, true);
        }
        return this;
    }
    
    /**
     * Adds a glow effect to the item by adding a hidden enchantment.
     * 
     * @return this ItemBuilder for chaining
     */
    public ItemBuilder glow() {
        return glow(true);
    }
    
    /**
     * Adds or removes a glow effect to/from the item.
     * 
     * @param glow whether to add the glow effect
     * @return this ItemBuilder for chaining
     */
    public ItemBuilder glow(boolean glow) {
        if (glow) {
            enchant(Enchantment.UNBREAKING, 1);
            addItemFlags(ItemFlag.HIDE_ENCHANTS);
        }
        return this;
    }
    
    /**
     * Adds item flags to hide certain properties.
     * 
     * @param flags the item flags to add
     * @return this ItemBuilder for chaining
     */
    public ItemBuilder addItemFlags(@NotNull ItemFlag... flags) {
        if (itemMeta != null) {
            itemMeta.addItemFlags(flags);
        }
        return this;
    }
    
    /**
     * Sets the item as unbreakable.
     * 
     * @return this ItemBuilder for chaining
     */
    public ItemBuilder unbreakable() {
        return unbreakable(true);
    }
    
    /**
     * Sets the unbreakable state of the item.
     * 
     * @param unbreakable whether the item should be unbreakable
     * @return this ItemBuilder for chaining
     */
    public ItemBuilder unbreakable(boolean unbreakable) {
        if (itemMeta != null) {
            itemMeta.setUnbreakable(unbreakable);
        }
        return this;
    }
    
    /**
     * Sets the custom model data of the item.
     * 
     * @param data the custom model data
     * @return this ItemBuilder for chaining
     */
    public ItemBuilder customModelData(int data) {
        if (itemMeta != null) {
            itemMeta.setCustomModelData(data);
        }
        return this;
    }
    
    /**
     * Sets the amount of items in the stack.
     * 
     * @param amount the amount
     * @return this ItemBuilder for chaining
     */
    public ItemBuilder amount(int amount) {
        itemStack.setAmount(Math.max(1, Math.min(64, amount)));
        return this;
    }
    
    /**
     * Builds and returns the final ItemStack.
     * 
     * @return the built ItemStack
     */
    @NotNull
    public ItemStack build() {
        if (itemMeta != null) {
            itemStack.setItemMeta(itemMeta);
        }
        return itemStack;
    }
    
    /**
     * Creates a simple ItemStack with just a material and display name.
     * 
     * @param material the material
     * @param name the display name
     * @return the created ItemStack
     */
    @NotNull
    public static ItemStack simple(@NotNull Material material, @NotNull Component name) {
        return new ItemBuilder(material).name(name).build();
    }
    
    /**
     * Creates a simple ItemStack with material, name, and lore.
     * 
     * @param material the material
     * @param name the display name
     * @param lore the lore lines
     * @return the created ItemStack
     */
    @NotNull
    public static ItemStack simple(@NotNull Material material, @NotNull Component name, @NotNull Component... lore) {
        return new ItemBuilder(material).name(name).lore(lore).build();
    }
}

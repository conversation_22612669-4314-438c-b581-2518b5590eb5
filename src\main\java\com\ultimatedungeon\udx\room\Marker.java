package com.ultimatedungeon.udx.room;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Map;

/**
 * Represents a metadata marker in a room template.
 * 
 * <p>Markers define special locations and behaviors within rooms,
 * such as spawn points, chests, triggers, and interactive elements.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public record Marker(
    @NotNull MarkerType type,
    int x,
    int y,
    int z,
    @NotNull Map<String, Object> properties
) {
    
    /**
     * Type of marker defining its behavior.
     */
    public enum MarkerType {
        // Spawning markers
        SPAWN("spawn"),                    // Generic mob spawn point
        BOSS_SPAWN("boss_spawn"),          // Boss spawn point
        PLAYER_SPAWN("player_spawn"),      // Player spawn/checkpoint
        
        // Loot markers
        CHEST("chest"),                    // Loot chest location
        TREASURE("treasure"),              // Special treasure location
        
        // Interactive markers
        LEVER("lever"),                    // Lever/switch
        BUTTON("button"),                  // Button/pressure plate
        GATE("gate"),                      // Gate/door mechanism
        PORTAL("portal"),                  // Teleportation portal
        
        // Trigger markers
        TRIGGER("trigger"),                // Generic trigger zone
        WAVE_TRIGGER("wave_trigger"),      // Mob wave trigger
        SCRIPT_TRIGGER("script_trigger"),  // Custom script trigger
        
        // Environment markers
        LIGHT("light"),                    // Light source
        EFFECT("effect"),                  // Particle/sound effect
        HAZARD("hazard"),                  // Environmental hazard
        
        // Navigation markers
        CHECKPOINT("checkpoint"),          // Save/respawn point
        EXIT("exit"),                      // Room exit
        ENTRANCE("entrance"),              // Room entrance
        
        // Special markers
        ANCHOR("anchor"),                  // Room anchor point
        CONNECTOR_POINT("connector");      // Connector reference point
        
        private final String id;
        
        MarkerType(String id) {
            this.id = id;
        }
        
        @NotNull
        public String getId() {
            return id;
        }
        
        @Nullable
        public static MarkerType fromId(@NotNull String id) {
            for (MarkerType type : values()) {
                if (type.id.equals(id)) {
                    return type;
                }
            }
            return null;
        }
    }
    
    /**
     * Gets a property value.
     * 
     * @param key the property key
     * @return the property value, or null if not found
     */
    @Nullable
    public Object getProperty(@NotNull String key) {
        return properties.get(key);
    }
    
    /**
     * Gets a property value as a string.
     * 
     * @param key the property key
     * @param defaultValue default value if not found
     * @return the property value as string
     */
    @NotNull
    public String getStringProperty(@NotNull String key, @NotNull String defaultValue) {
        Object value = properties.get(key);
        return value != null ? value.toString() : defaultValue;
    }
    
    /**
     * Gets a property value as an integer.
     * 
     * @param key the property key
     * @param defaultValue default value if not found or invalid
     * @return the property value as integer
     */
    public int getIntProperty(@NotNull String key, int defaultValue) {
        Object value = properties.get(key);
        if (value instanceof Number number) {
            return number.intValue();
        }
        if (value instanceof String str) {
            try {
                return Integer.parseInt(str);
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }
    
    /**
     * Gets a property value as a boolean.
     * 
     * @param key the property key
     * @param defaultValue default value if not found
     * @return the property value as boolean
     */
    public boolean getBooleanProperty(@NotNull String key, boolean defaultValue) {
        Object value = properties.get(key);
        if (value instanceof Boolean bool) {
            return bool;
        }
        if (value instanceof String str) {
            return Boolean.parseBoolean(str);
        }
        return defaultValue;
    }
    
    /**
     * Gets a property value as a double.
     * 
     * @param key the property key
     * @param defaultValue default value if not found or invalid
     * @return the property value as double
     */
    public double getDoubleProperty(@NotNull String key, double defaultValue) {
        Object value = properties.get(key);
        if (value instanceof Number number) {
            return number.doubleValue();
        }
        if (value instanceof String str) {
            try {
                return Double.parseDouble(str);
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }
    
    /**
     * Gets the world position of this marker relative to a room position.
     * 
     * @param roomX room X coordinate
     * @param roomY room Y coordinate
     * @param roomZ room Z coordinate
     * @return world coordinates of the marker
     */
    @NotNull
    public int[] getWorldPosition(int roomX, int roomY, int roomZ) {
        return new int[]{
            roomX + x,
            roomY + y,
            roomZ + z
        };
    }
    
    /**
     * Checks if this marker is of a spawning type.
     * 
     * @return true if this is a spawn marker
     */
    public boolean isSpawnMarker() {
        return type == MarkerType.SPAWN || 
               type == MarkerType.BOSS_SPAWN || 
               type == MarkerType.PLAYER_SPAWN;
    }
    
    /**
     * Checks if this marker is of a loot type.
     * 
     * @return true if this is a loot marker
     */
    public boolean isLootMarker() {
        return type == MarkerType.CHEST || 
               type == MarkerType.TREASURE;
    }
    
    /**
     * Checks if this marker is interactive.
     * 
     * @return true if this marker can be interacted with
     */
    public boolean isInteractive() {
        return type == MarkerType.LEVER || 
               type == MarkerType.BUTTON || 
               type == MarkerType.GATE || 
               type == MarkerType.PORTAL;
    }
    
    /**
     * Checks if this marker is a trigger.
     * 
     * @return true if this is a trigger marker
     */
    public boolean isTrigger() {
        return type == MarkerType.TRIGGER || 
               type == MarkerType.WAVE_TRIGGER || 
               type == MarkerType.SCRIPT_TRIGGER;
    }
    
    @Override
    public String toString() {
        return String.format("Marker{%s at (%d,%d,%d) props=%s}", 
            type, x, y, z, properties);
    }
}

package com.ultimatedungeon.udx.api.progression;

import org.jetbrains.annotations.NotNull;
import java.util.UUID;

public record LeaderboardEntry(
    int rank,
    @NotNull UUID playerId,
    @NotNull String playerName,
    long completionTimeMs,
    int score,
    @NotNull String dungeonId,
    @NotNull String difficulty
) {
    public LeaderboardEntry {
        if (rank < 1) throw new IllegalArgumentException("Rank must be positive");
        if (playerId == null) throw new IllegalArgumentException("Player ID cannot be null");
        if (playerName == null || playerName.isBlank()) throw new IllegalArgumentException("Player name cannot be null or blank");
        if (completionTimeMs < 0) throw new IllegalArgumentException("Completion time cannot be negative");
        if (score < 0) throw new IllegalArgumentException("Score cannot be negative");
        if (dungeonId == null || dungeonId.isBlank()) throw new IllegalArgumentException("Dungeon ID cannot be null or blank");
        if (difficulty == null || difficulty.isBlank()) throw new IllegalArgumentException("Difficulty cannot be null or blank");
    }
}

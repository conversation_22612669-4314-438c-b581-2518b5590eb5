package com.ultimatedungeon.udx.api.loot;

import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.Instant;
import java.util.UUID;

/**
 * Immutable representation of loot history entry for API consumers.
 */
public record LootHistoryEntry(
    @NotNull UUID entryId,
    @NotNull UUID playerId,
    @NotNull String playerName,
    @NotNull String dungeonId,
    @NotNull String difficulty,
    @NotNull String lootTableId,
    @Nullable ItemStack item,
    @Nullable String command,
    double currencyAmount,
    @NotNull ItemRarity rarity,
    @NotNull Instant timestamp,
    @NotNull UUID dungeonInstanceId
) {
    
    public LootHistoryEntry {
        if (entryId == null) throw new IllegalArgumentException("Entry ID cannot be null");
        if (playerId == null) throw new IllegalArgumentException("Player ID cannot be null");
        if (playerName == null || playerName.isBlank()) throw new IllegalArgumentException("Player name cannot be null or blank");
        if (dungeonId == null || dungeonId.isBlank()) throw new IllegalArgumentException("Dungeon ID cannot be null or blank");
        if (difficulty == null || difficulty.isBlank()) throw new IllegalArgumentException("Difficulty cannot be null or blank");
        if (lootTableId == null || lootTableId.isBlank()) throw new IllegalArgumentException("Loot table ID cannot be null or blank");
        if (currencyAmount < 0) throw new IllegalArgumentException("Currency amount cannot be negative");
        if (rarity == null) throw new IllegalArgumentException("Rarity cannot be null");
        if (timestamp == null) throw new IllegalArgumentException("Timestamp cannot be null");
        if (dungeonInstanceId == null) throw new IllegalArgumentException("Dungeon instance ID cannot be null");
    }
    
    /**
     * Gets the type of loot that was received.
     * 
     * @return The loot type
     */
    @NotNull
    public LootType getLootType() {
        if (item != null) {
            return LootType.ITEM;
        } else if (command != null && !command.isBlank()) {
            return LootType.COMMAND;
        } else if (currencyAmount > 0) {
            return LootType.CURRENCY;
        } else {
            return LootType.UNKNOWN;
        }
    }
    
    /**
     * Gets a display name for the loot received.
     * 
     * @return The display name
     */
    @NotNull
    public String getLootDisplayName() {
        return switch (getLootType()) {
            case ITEM -> item != null && item.hasItemMeta() && item.getItemMeta().hasDisplayName() 
                ? item.getItemMeta().getDisplayName() 
                : item != null ? item.getType().name() : "Unknown Item";
            case COMMAND -> "Command Reward";
            case CURRENCY -> String.format("%.2f Currency", currencyAmount);
            case UNKNOWN -> "Unknown Reward";
        };
    }
    
    /**
     * Gets the estimated value of this loot entry.
     * 
     * @return The estimated value
     */
    public double getEstimatedValue() {
        double baseValue = switch (getLootType()) {
            case ITEM -> item != null ? item.getAmount() * rarity.getValueMultiplier() : 0.0;
            case CURRENCY -> currencyAmount;
            case COMMAND -> rarity.getValueMultiplier() * 10.0; // Arbitrary base value for commands
            case UNKNOWN -> 0.0;
        };
        
        return baseValue * rarity.getValueMultiplier();
    }
    
    /**
     * Enumeration of loot types.
     */
    public enum LootType {
        ITEM,
        COMMAND,
        CURRENCY,
        UNKNOWN
    }
}

package com.ultimatedungeon.udx.room;

import com.ultimatedungeon.udx.config.ConfigService;
import com.ultimatedungeon.udx.paste.FastPaster;
import com.ultimatedungeon.udx.util.SchedulerUtil;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.stream.Collectors;

/**
 * Service for managing room templates and the UDX schematic format.
 * 
 * <p>This service handles loading, saving, and managing room templates
 * using the custom UDX format without external dependencies.</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public final class RoomService {
    
    private final Plugin plugin;
    private final ConfigService configService;
    private final FastPaster fastPaster;
    private final SchedulerUtil schedulerUtil;
    private final UDXFormat udxFormat;
    
    private final Map<String, RoomTemplate> roomTemplates;
    private final Map<String, Set<String>> roomsByTag;
    private final Map<RoomTemplate.RoomType, Set<String>> roomsByType;
    
    private File roomsDirectory;
    
    public RoomService(@NotNull Plugin plugin, @NotNull ConfigService configService, @NotNull FastPaster fastPaster) {
        this.plugin = plugin;
        this.configService = configService;
        this.fastPaster = fastPaster;
        this.schedulerUtil = new SchedulerUtil(plugin);
        this.udxFormat = new UDXFormat();
        
        this.roomTemplates = new ConcurrentHashMap<>();
        this.roomsByTag = new ConcurrentHashMap<>();
        this.roomsByType = new ConcurrentHashMap<>();
    }
    
    /**
     * Initializes the room service.
     */
    public void initialize() {
        // Create rooms directory
        roomsDirectory = new File(plugin.getDataFolder(), "rooms");
        if (!roomsDirectory.exists()) {
            roomsDirectory.mkdirs();
        }
        
        // Load existing room templates
        loadAllRoomTemplates();
        
        plugin.getLogger().info("Room service initialized with " + roomTemplates.size() + " templates");
    }
    
    /**
     * Loads all room templates from disk.
     */
    private void loadAllRoomTemplates() {
        File[] files = roomsDirectory.listFiles((dir, name) -> name.endsWith(".udxroom"));
        if (files == null) {
            return;
        }
        
        int loaded = 0;
        int failed = 0;
        
        for (File file : files) {
            try {
                RoomTemplate template = udxFormat.loadFromFile(file);
                registerRoomTemplate(template);
                loaded++;
            } catch (Exception e) {
                plugin.getLogger().log(Level.WARNING, "Failed to load room template: " + file.getName(), e);
                failed++;
            }
        }
        
        plugin.getLogger().info("Loaded " + loaded + " room templates (" + failed + " failed)");
    }
    
    /**
     * Registers a room template in the service.
     */
    private void registerRoomTemplate(@NotNull RoomTemplate template) {
        roomTemplates.put(template.getId(), template);
        
        // Index by tags
        for (String tag : template.getTags()) {
            roomsByTag.computeIfAbsent(tag, k -> ConcurrentHashMap.newKeySet()).add(template.getId());
        }
        
        // Index by type
        roomsByType.computeIfAbsent(template.getRoomType(), k -> ConcurrentHashMap.newKeySet()).add(template.getId());
    }
    
    /**
     * Unregisters a room template from the service.
     */
    private void unregisterRoomTemplate(@NotNull String templateId) {
        RoomTemplate template = roomTemplates.remove(templateId);
        if (template == null) {
            return;
        }
        
        // Remove from tag index
        for (String tag : template.getTags()) {
            Set<String> taggedRooms = roomsByTag.get(tag);
            if (taggedRooms != null) {
                taggedRooms.remove(templateId);
                if (taggedRooms.isEmpty()) {
                    roomsByTag.remove(tag);
                }
            }
        }
        
        // Remove from type index
        Set<String> typedRooms = roomsByType.get(template.getRoomType());
        if (typedRooms != null) {
            typedRooms.remove(templateId);
            if (typedRooms.isEmpty()) {
                roomsByType.remove(template.getRoomType());
            }
        }
    }
    
    /**
     * Saves a room template to disk.
     * 
     * @param template the template to save
     * @return future that completes when saved
     */
    @NotNull
    public CompletableFuture<Void> saveRoomTemplate(@NotNull RoomTemplate template) {
        return CompletableFuture.runAsync(() -> {
            try {
                File file = new File(roomsDirectory, template.getId() + ".udxroom");
                udxFormat.saveToFile(template, file);
                
                // Register in memory
                schedulerUtil.runTask(() -> registerRoomTemplate(template));
                
                plugin.getLogger().info("Saved room template: " + template.getId());
                
            } catch (IOException e) {
                plugin.getLogger().log(Level.SEVERE, "Failed to save room template: " + template.getId(), e);
                throw new RuntimeException("Failed to save room template", e);
            }
        });
    }
    
    /**
     * Deletes a room template.
     * 
     * @param templateId the template ID to delete
     * @return true if deleted successfully
     */
    public boolean deleteRoomTemplate(@NotNull String templateId) {
        RoomTemplate template = roomTemplates.get(templateId);
        if (template == null) {
            return false;
        }
        
        // Delete file
        File file = new File(roomsDirectory, templateId + ".udxroom");
        if (file.exists() && !file.delete()) {
            plugin.getLogger().warning("Failed to delete room template file: " + file.getName());
            return false;
        }
        
        // Unregister from memory
        unregisterRoomTemplate(templateId);
        
        plugin.getLogger().info("Deleted room template: " + templateId);
        return true;
    }
    
    /**
     * Gets a room template by ID.
     * 
     * @param templateId the template ID
     * @return the template, or null if not found
     */
    @Nullable
    public RoomTemplate getRoomTemplate(@NotNull String templateId) {
        return roomTemplates.get(templateId);
    }
    
    /**
     * Gets all room templates.
     * 
     * @return collection of all templates
     */
    @NotNull
    public Collection<RoomTemplate> getAllRoomTemplates() {
        return new ArrayList<>(roomTemplates.values());
    }
    
    /**
     * Gets room templates by type.
     * 
     * @param roomType the room type
     * @return list of templates of the specified type
     */
    @NotNull
    public List<RoomTemplate> getRoomTemplatesByType(@NotNull RoomTemplate.RoomType roomType) {
        Set<String> templateIds = roomsByType.get(roomType);
        if (templateIds == null) {
            return Collections.emptyList();
        }
        
        return templateIds.stream()
            .map(roomTemplates::get)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }
    
    /**
     * Gets room templates by tag.
     * 
     * @param tag the tag to filter by
     * @return list of templates with the specified tag
     */
    @NotNull
    public List<RoomTemplate> getRoomTemplatesByTag(@NotNull String tag) {
        Set<String> templateIds = roomsByTag.get(tag);
        if (templateIds == null) {
            return Collections.emptyList();
        }
        
        return templateIds.stream()
            .map(roomTemplates::get)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }
    
    /**
     * Gets room templates suitable for a specific depth.
     * 
     * @param depth the dungeon depth
     * @return list of suitable templates
     */
    @NotNull
    public List<RoomTemplate> getRoomTemplatesForDepth(int depth) {
        return roomTemplates.values().stream()
            .filter(template -> template.isSuitableForDepth(depth))
            .collect(Collectors.toList());
    }
    
    /**
     * Gets room templates with specific connector requirements.
     * 
     * @param requiredConnectors map of direction to required count
     * @return list of compatible templates
     */
    @NotNull
    public List<RoomTemplate> getRoomTemplatesWithConnectors(@NotNull Map<Connector.Direction, Integer> requiredConnectors) {
        return roomTemplates.values().stream()
            .filter(template -> hasRequiredConnectors(template, requiredConnectors))
            .collect(Collectors.toList());
    }
    
    /**
     * Checks if a template has the required connectors.
     */
    private boolean hasRequiredConnectors(@NotNull RoomTemplate template, @NotNull Map<Connector.Direction, Integer> required) {
        Map<Connector.Direction, Long> available = template.getConnectors().stream()
            .collect(Collectors.groupingBy(Connector::direction, Collectors.counting()));
        
        for (Map.Entry<Connector.Direction, Integer> entry : required.entrySet()) {
            long availableCount = available.getOrDefault(entry.getKey(), 0L);
            if (availableCount < entry.getValue()) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Captures a room template from a world region.
     * 
     * @param world the world to capture from
     * @param pos1 first corner of the region
     * @param pos2 second corner of the region
     * @param templateId the ID for the new template
     * @param name the name for the template
     * @param author the author of the template
     * @return future that completes with the captured template
     */
    @NotNull
    public CompletableFuture<RoomTemplate> captureRoomTemplate(
        @NotNull World world,
        @NotNull Location pos1,
        @NotNull Location pos2,
        @NotNull String templateId,
        @NotNull String name,
        @NotNull String author
    ) {
        return CompletableFuture.supplyAsync(() -> {
            // Calculate bounds
            int minX = Math.min(pos1.getBlockX(), pos2.getBlockX());
            int maxX = Math.max(pos1.getBlockX(), pos2.getBlockX());
            int minY = Math.min(pos1.getBlockY(), pos2.getBlockY());
            int maxY = Math.max(pos1.getBlockY(), pos2.getBlockY());
            int minZ = Math.min(pos1.getBlockZ(), pos2.getBlockZ());
            int maxZ = Math.max(pos1.getBlockZ(), pos2.getBlockZ());
            
            int width = maxX - minX + 1;
            int height = maxY - minY + 1;
            int depth = maxZ - minZ + 1;
            
            // Build block palette and data
            List<Material> palette = new ArrayList<>();
            Map<Material, Integer> paletteMap = new HashMap<>();
            int[] blockData = new int[width * height * depth];
            
            int index = 0;
            for (int y = minY; y <= maxY; y++) {
                for (int z = minZ; z <= maxZ; z++) {
                    for (int x = minX; x <= maxX; x++) {
                        Block block = world.getBlockAt(x, y, z);
                        Material material = block.getType();
                        
                        // Add to palette if not present
                        int paletteIndex = paletteMap.computeIfAbsent(material, mat -> {
                            palette.add(mat);
                            return palette.size() - 1;
                        });
                        
                        blockData[index++] = paletteIndex;
                    }
                }
            }
            
            // Create template
            RoomTemplate.Builder builder = new RoomTemplate.Builder(templateId)
                .name(name)
                .author(author)
                .dimensions(width, height, depth)
                .anchor(0, 0, 0) // Default anchor at origin
                .blockPalette(palette)
                .blockData(blockData)
                .roomType(RoomTemplate.RoomType.CHAMBER); // Default type
            
            return builder.build();
        });
    }
    
    /**
     * Gets all available room tags.
     * 
     * @return set of all tags used by room templates
     */
    @NotNull
    public Set<String> getAllRoomTags() {
        return new HashSet<>(roomsByTag.keySet());
    }
    
    /**
     * Gets statistics about the room library.
     * 
     * @return map of statistics
     */
    @NotNull
    public Map<String, Object> getRoomStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        stats.put("totalRooms", roomTemplates.size());
        stats.put("totalTags", roomsByTag.size());
        
        // Count by type
        Map<String, Integer> typeStats = new HashMap<>();
        for (RoomTemplate.RoomType type : RoomTemplate.RoomType.values()) {
            Set<String> rooms = roomsByType.get(type);
            typeStats.put(type.name(), rooms != null ? rooms.size() : 0);
        }
        stats.put("roomsByType", typeStats);
        
        // Average room size
        if (!roomTemplates.isEmpty()) {
            double avgVolume = roomTemplates.values().stream()
                .mapToInt(RoomTemplate::getVolume)
                .average()
                .orElse(0.0);
            stats.put("averageVolume", avgVolume);
        }
        
        return stats;
    }
    
    /**
     * Validates a room template.
     * 
     * @param template the template to validate
     * @return list of validation errors (empty if valid)
     */
    @NotNull
    public List<String> validateRoomTemplate(@NotNull RoomTemplate template) {
        List<String> errors = new ArrayList<>();
        
        // Check basic properties
        if (template.getId().trim().isEmpty()) {
            errors.add("Room ID cannot be empty");
        }
        
        if (template.getWidth() <= 0 || template.getHeight() <= 0 || template.getDepth() <= 0) {
            errors.add("Room dimensions must be positive");
        }
        
        // Check connectors are within bounds
        for (Connector connector : template.getConnectors()) {
            if (connector.x() < 0 || connector.x() >= template.getWidth() ||
                connector.y() < 0 || connector.y() >= template.getHeight() ||
                connector.z() < 0 || connector.z() >= template.getDepth()) {
                errors.add("Connector at (" + connector.x() + "," + connector.y() + "," + connector.z() + ") is out of bounds");
            }
        }
        
        // Check markers are within bounds
        for (Marker marker : template.getMarkers()) {
            if (marker.x() < 0 || marker.x() >= template.getWidth() ||
                marker.y() < 0 || marker.y() >= template.getHeight() ||
                marker.z() < 0 || marker.z() >= template.getDepth()) {
                errors.add("Marker at (" + marker.x() + "," + marker.y() + "," + marker.z() + ") is out of bounds");
            }
        }
        
        // Check for required connectors based on room type
        if (template.getRoomType() != RoomTemplate.RoomType.ENTRANCE && 
            template.getRoomType() != RoomTemplate.RoomType.EXIT &&
            template.getConnectors().isEmpty()) {
            errors.add("Room must have at least one connector (except entrance/exit rooms)");
        }
        
        return errors;
    }
    
    /**
     * Shuts down the room service.
     */
    public void shutdown() {
        roomTemplates.clear();
        roomsByTag.clear();
        roomsByType.clear();
        
        plugin.getLogger().info("Room service shut down");
    }
}

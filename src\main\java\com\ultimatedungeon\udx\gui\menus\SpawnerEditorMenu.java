package com.ultimatedungeon.udx.gui.menus;

import com.ultimatedungeon.udx.bootstrap.UltimateDungeonX;
import com.ultimatedungeon.udx.gui.Menu;
import com.ultimatedungeon.udx.util.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

/**
 * Spawner editor for configuring mob spawners and waves.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class SpawnerEditorMenu extends Menu {
    
    private final UltimateDungeonX plugin;
    private String selectedMobType = "ZOMBIE";
    private String selectedTrigger = "ON_ENTER";
    private int spawnCount = 3;
    private int waveDelay = 5;
    
    public SpawnerEditorMenu(@NotNull UltimateDungeonX plugin, @NotNull Player player) {
        super(player, Component.text("Spawner Editor").color(NamedTextColor.RED).decoration(TextDecoration.BOLD, true), 
              54, plugin.getMenuRegistry());
        this.plugin = plugin;
    }
    
    @Override
    protected void setupMenu() {
        inventory.clear();
        clickHandlers.clear();
        
        // Header
        ItemStack headerItem = new ItemBuilder(Material.SPAWNER)
            .name(Component.text("Mob Spawner Editor").color(NamedTextColor.GOLD).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Configure mob spawners and waves").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Design custom mob encounters").color(NamedTextColor.YELLOW),
                Component.text("with advanced AI and behaviors").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(4, headerItem, clickType -> {
            // No action for header
        });
        
        // Mob Configuration
        setupMobConfiguration();
        
        // Wave Settings
        setupWaveSettings();
        
        // Behavior Configuration
        setupBehaviorConfiguration();
        
        // Testing Tools
        setupTestingTools();
        
        // Actions
        setupActions();
        
        // Back button
        ItemStack backItem = new ItemBuilder(Material.ARROW)
            .name(Component.text("Back to Editor Hub").color(NamedTextColor.GRAY))
            .lore(Component.text("Click to return").color(NamedTextColor.DARK_GRAY))
            .build();
        
        setItem(49, backItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                plugin.getMenuRegistry().openEditorHub(player);
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            }
        });
        
        fillEmptySlots();
    }
    
    private void setupMobConfiguration() {
        // Mob Type Selection
        Material mobMaterial = switch (selectedMobType) {
            case "ZOMBIE" -> Material.ZOMBIE_HEAD;
            case "SKELETON" -> Material.SKELETON_SKULL;
            case "SPIDER" -> Material.SPIDER_EYE;
            case "CREEPER" -> Material.GUNPOWDER;
            case "ENDERMAN" -> Material.ENDER_PEARL;
            case "BLAZE" -> Material.BLAZE_ROD;
            case "WITHER_SKELETON" -> Material.WITHER_SKELETON_SKULL;
            default -> Material.ZOMBIE_HEAD;
        };
        
        ItemStack mobTypeItem = new ItemBuilder(mobMaterial)
            .name(Component.text("Mob Type: " + selectedMobType).color(NamedTextColor.RED).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Select the mob to spawn").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Available mobs:").color(NamedTextColor.YELLOW),
                Component.text("• ZOMBIE - Basic melee").color(selectedMobType.equals("ZOMBIE") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• SKELETON - Ranged archer").color(selectedMobType.equals("SKELETON") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• SPIDER - Fast crawler").color(selectedMobType.equals("SPIDER") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• CREEPER - Explosive").color(selectedMobType.equals("CREEPER") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• ENDERMAN - Teleporter").color(selectedMobType.equals("ENDERMAN") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• BLAZE - Fire ranged").color(selectedMobType.equals("BLAZE") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• WITHER_SKELETON - Elite melee").color(selectedMobType.equals("WITHER_SKELETON") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to cycle mob types").color(NamedTextColor.AQUA)
            )
            .glow()
            .build();
        
        setItem(10, mobTypeItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                selectedMobType = switch (selectedMobType) {
                    case "ZOMBIE" -> "SKELETON";
                    case "SKELETON" -> "SPIDER";
                    case "SPIDER" -> "CREEPER";
                    case "CREEPER" -> "ENDERMAN";
                    case "ENDERMAN" -> "BLAZE";
                    case "BLAZE" -> "WITHER_SKELETON";
                    default -> "ZOMBIE";
                };
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
        
        // Mob Stats
        ItemStack statsItem = new ItemBuilder(Material.DIAMOND_SWORD)
            .name(Component.text("Mob Statistics").color(NamedTextColor.AQUA))
            .lore(
                Component.text("Configure mob attributes").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Health: 20.0 ❤").color(NamedTextColor.RED),
                Component.text("Attack Damage: 6.0 ⚔").color(NamedTextColor.DARK_RED),
                Component.text("Movement Speed: 0.25 ➤").color(NamedTextColor.BLUE),
                Component.text("Armor: 2.0 🛡").color(NamedTextColor.GRAY),
                Component.text("Follow Range: 16.0 👁").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Left-click: Increase values").color(NamedTextColor.GREEN),
                Component.text("Right-click: Decrease values").color(NamedTextColor.RED),
                Component.text("Shift-click: Reset to defaults").color(NamedTextColor.YELLOW)
            )
            .build();
        
        setItem(11, statsItem, clickType -> {
            switch (clickType) {
                case LEFT -> {
                    player.sendMessage(Component.text("Mob stats increased!")
                        .color(NamedTextColor.GREEN));
                    player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);
                }
                case RIGHT -> {
                    player.sendMessage(Component.text("Mob stats decreased!")
                        .color(NamedTextColor.RED));
                    player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 0.8f);
                }
                case SHIFT_LEFT -> {
                    player.sendMessage(Component.text("Mob stats reset to defaults!")
                        .color(NamedTextColor.YELLOW));
                    player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                }
            }
            refresh();
        });
        
        // Equipment
        ItemStack equipmentItem = new ItemBuilder(Material.IRON_CHESTPLATE)
            .name(Component.text("Mob Equipment").color(NamedTextColor.GRAY))
            .lore(
                Component.text("Configure mob gear and weapons").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Current equipment:").color(NamedTextColor.YELLOW),
                Component.text("• Helmet: Iron Helmet").color(NamedTextColor.WHITE),
                Component.text("• Chestplate: None").color(NamedTextColor.DARK_GRAY),
                Component.text("• Leggings: None").color(NamedTextColor.DARK_GRAY),
                Component.text("• Boots: None").color(NamedTextColor.DARK_GRAY),
                Component.text("• Main Hand: Iron Sword").color(NamedTextColor.WHITE),
                Component.text("• Off Hand: None").color(NamedTextColor.DARK_GRAY),
                Component.empty(),
                Component.text("Click to configure equipment").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(12, equipmentItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Opening equipment editor...")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                // TODO: Open equipment editor submenu
            }
        });
    }
    
    private void setupWaveSettings() {
        // Spawn Count
        ItemStack countItem = new ItemBuilder(Material.REDSTONE)
            .name(Component.text("Spawn Count: " + spawnCount).color(NamedTextColor.RED))
            .lore(
                Component.text("Number of mobs to spawn per wave").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Current: " + spawnCount + " mobs").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Left-click: +1").color(NamedTextColor.GREEN),
                Component.text("Right-click: -1").color(NamedTextColor.RED),
                Component.text("Shift-click: +5").color(NamedTextColor.AQUA)
            )
            .amount(Math.max(1, Math.min(spawnCount, 64)))
            .build();
        
        setItem(19, countItem, clickType -> {
            switch (clickType) {
                case LEFT -> spawnCount = Math.min(spawnCount + 1, 20);
                case RIGHT -> spawnCount = Math.max(spawnCount - 1, 1);
                case SHIFT_LEFT -> spawnCount = Math.min(spawnCount + 5, 20);
            }
            player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            refresh();
        });
        
        // Wave Delay
        ItemStack delayItem = new ItemBuilder(Material.CLOCK)
            .name(Component.text("Wave Delay: " + waveDelay + "s").color(NamedTextColor.BLUE))
            .lore(
                Component.text("Delay between wave spawns").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Current: " + waveDelay + " seconds").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Left-click: +1 second").color(NamedTextColor.GREEN),
                Component.text("Right-click: -1 second").color(NamedTextColor.RED),
                Component.text("Shift-click: +5 seconds").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(20, delayItem, clickType -> {
            switch (clickType) {
                case LEFT -> waveDelay = Math.min(waveDelay + 1, 60);
                case RIGHT -> waveDelay = Math.max(waveDelay - 1, 0);
                case SHIFT_LEFT -> waveDelay = Math.min(waveDelay + 5, 60);
            }
            player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
            refresh();
        });
        
        // Trigger Type
        Material triggerMaterial = switch (selectedTrigger) {
            case "ON_ENTER" -> Material.TRIPWIRE_HOOK;
            case "ON_KILL" -> Material.DIAMOND_SWORD;
            case "TIMER" -> Material.REPEATER;
            case "LEVER" -> Material.LEVER;
            case "PRESSURE_PLATE" -> Material.STONE_PRESSURE_PLATE;
            default -> Material.TRIPWIRE_HOOK;
        };
        
        ItemStack triggerItem = new ItemBuilder(triggerMaterial)
            .name(Component.text("Trigger: " + selectedTrigger).color(NamedTextColor.YELLOW))
            .lore(
                Component.text("When to spawn this wave").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Available triggers:").color(NamedTextColor.YELLOW),
                Component.text("• ON_ENTER - When players enter room").color(selectedTrigger.equals("ON_ENTER") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• ON_KILL - When previous wave dies").color(selectedTrigger.equals("ON_KILL") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• TIMER - After time delay").color(selectedTrigger.equals("TIMER") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• LEVER - Manual activation").color(selectedTrigger.equals("LEVER") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.text("• PRESSURE_PLATE - Step activation").color(selectedTrigger.equals("PRESSURE_PLATE") ? NamedTextColor.GREEN : NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to cycle triggers").color(NamedTextColor.AQUA)
            )
            .glow()
            .build();
        
        setItem(21, triggerItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                selectedTrigger = switch (selectedTrigger) {
                    case "ON_ENTER" -> "ON_KILL";
                    case "ON_KILL" -> "TIMER";
                    case "TIMER" -> "LEVER";
                    case "LEVER" -> "PRESSURE_PLATE";
                    default -> "ON_ENTER";
                };
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.2f);
                refresh();
            }
        });
    }
    
    private void setupBehaviorConfiguration() {
        // AI Behaviors
        ItemStack aiItem = new ItemBuilder(Material.REDSTONE_TORCH)
            .name(Component.text("AI Behaviors").color(NamedTextColor.RED))
            .lore(
                Component.text("Configure mob AI and behaviors").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Active behaviors:").color(NamedTextColor.YELLOW),
                Component.text("✓ Target Players").color(NamedTextColor.GREEN),
                Component.text("✓ Melee Attack").color(NamedTextColor.GREEN),
                Component.text("✓ Random Walk").color(NamedTextColor.GREEN),
                Component.text("✗ Leap Attack").color(NamedTextColor.RED),
                Component.text("✗ Ranged Attack").color(NamedTextColor.RED),
                Component.text("✗ Summon Minions").color(NamedTextColor.RED),
                Component.empty(),
                Component.text("Click to configure behaviors").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(28, aiItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Opening AI behavior editor...")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                // TODO: Open AI behavior submenu
            }
        });
        
        // Special Abilities
        ItemStack abilitiesItem = new ItemBuilder(Material.ENCHANTED_BOOK)
            .name(Component.text("Special Abilities").color(NamedTextColor.LIGHT_PURPLE))
            .lore(
                Component.text("Configure special mob abilities").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Available abilities:").color(NamedTextColor.YELLOW),
                Component.text("• Charge Attack (5s cooldown)").color(NamedTextColor.WHITE),
                Component.text("• Explosive Death").color(NamedTextColor.WHITE),
                Component.text("• Regeneration (Low HP)").color(NamedTextColor.WHITE),
                Component.text("• Teleport Strike").color(NamedTextColor.WHITE),
                Component.text("• Shield Bash").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to configure abilities").color(NamedTextColor.AQUA)
            )
            .glow()
            .build();
        
        setItem(29, abilitiesItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Opening abilities editor...")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                // TODO: Open abilities submenu
            }
        });
        
        // Leash Settings
        ItemStack leashItem = new ItemBuilder(Material.LEAD)
            .name(Component.text("Leash Settings").color(NamedTextColor.GOLD))
            .lore(
                Component.text("Configure mob movement restrictions").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Leash radius: 16 blocks").color(NamedTextColor.YELLOW),
                Component.text("Return behavior: Teleport").color(NamedTextColor.YELLOW),
                Component.text("Leash to: Spawn point").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Prevents mobs from wandering").color(NamedTextColor.GRAY),
                Component.text("too far from their spawn area").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Click to configure leash").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(30, leashItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Leash settings updated!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                refresh();
            }
        });
    }
    
    private void setupTestingTools() {
        // Test Spawn
        ItemStack testSpawnItem = new ItemBuilder(Material.EGG)
            .name(Component.text("Test Spawn").color(NamedTextColor.GREEN))
            .lore(
                Component.text("Spawn a test mob with current settings").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("This will spawn a " + selectedMobType).color(NamedTextColor.YELLOW),
                Component.text("with all configured behaviors").color(NamedTextColor.YELLOW),
                Component.text("and abilities for testing").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Click to spawn test mob").color(NamedTextColor.AQUA)
            )
            .glow()
            .build();
        
        setItem(37, testSpawnItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Spawning test " + selectedMobType.toLowerCase() + "...")
                    .color(NamedTextColor.GREEN));
                player.sendMessage(Component.text("Test mob spawned at your location!")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.ENTITY_ZOMBIE_VILLAGER_CURE, 1.0f, 1.2f);
                close();
            }
        });
        
        // Clear Test Mobs
        ItemStack clearItem = new ItemBuilder(Material.BARRIER)
            .name(Component.text("Clear Test Mobs").color(NamedTextColor.RED))
            .lore(
                Component.text("Remove all test mobs").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("This will despawn all mobs").color(NamedTextColor.YELLOW),
                Component.text("spawned for testing purposes").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Click to clear test mobs").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(38, clearItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("All test mobs cleared!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.ENTITY_GENERIC_EXPLODE, 0.5f, 1.2f);
            }
        });
        
        // Sandbox Mode
        ItemStack sandboxItem = new ItemBuilder(Material.GRASS_BLOCK)
            .name(Component.text("Sandbox Mode").color(NamedTextColor.YELLOW))
            .lore(
                Component.text("Enter sandbox testing environment").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Creates a temporary test world").color(NamedTextColor.YELLOW),
                Component.text("where you can safely test").color(NamedTextColor.YELLOW),
                Component.text("spawner configurations").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Click to enter sandbox").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(39, sandboxItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Creating sandbox world...")
                    .color(NamedTextColor.YELLOW));
                player.sendMessage(Component.text("Teleporting to sandbox...")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);
                close();
            }
        });
    }
    
    private void setupActions() {
        // Save Spawner
        ItemStack saveItem = new ItemBuilder(Material.WRITABLE_BOOK)
            .name(Component.text("Save Spawner Config").color(NamedTextColor.GREEN).decoration(TextDecoration.BOLD, true))
            .lore(
                Component.text("Save current spawner configuration").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("This will save:").color(NamedTextColor.YELLOW),
                Component.text("• Mob type and statistics").color(NamedTextColor.WHITE),
                Component.text("• Wave settings and triggers").color(NamedTextColor.WHITE),
                Component.text("• AI behaviors and abilities").color(NamedTextColor.WHITE),
                Component.text("• Equipment and leash settings").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to save configuration").color(NamedTextColor.AQUA)
            )
            .glow()
            .build();
        
        setItem(46, saveItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Saving spawner configuration...")
                    .color(NamedTextColor.YELLOW));
                player.sendMessage(Component.text("✓ Spawner saved as 'custom_spawner_1.json'")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);
            }
        });
        
        // Load Spawner
        ItemStack loadItem = new ItemBuilder(Material.BOOK)
            .name(Component.text("Load Spawner Config").color(NamedTextColor.BLUE))
            .lore(
                Component.text("Load existing spawner configuration").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("Available configs: 8").color(NamedTextColor.YELLOW),
                Component.text("• skeleton_archer.json").color(NamedTextColor.WHITE),
                Component.text("• zombie_horde.json").color(NamedTextColor.WHITE),
                Component.text("• spider_swarm.json").color(NamedTextColor.WHITE),
                Component.text("• elite_guard.json").color(NamedTextColor.WHITE),
                Component.empty(),
                Component.text("Click to browse configs").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(47, loadItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Opening configuration browser...")
                    .color(NamedTextColor.YELLOW));
                player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                // TODO: Open config browser menu
            }
        });
        
        // Export to Room
        ItemStack exportItem = new ItemBuilder(Material.ENDER_CHEST)
            .name(Component.text("Export to Room").color(NamedTextColor.LIGHT_PURPLE))
            .lore(
                Component.text("Add this spawner to a room template").color(NamedTextColor.GRAY),
                Component.empty(),
                Component.text("This will place the spawner").color(NamedTextColor.YELLOW),
                Component.text("configuration into a room").color(NamedTextColor.YELLOW),
                Component.text("template for use in dungeons").color(NamedTextColor.YELLOW),
                Component.empty(),
                Component.text("Click to export spawner").color(NamedTextColor.AQUA)
            )
            .build();
        
        setItem(48, exportItem, clickType -> {
            if (clickType == ClickType.LEFT) {
                player.sendMessage(Component.text("Exporting spawner to room template...")
                    .color(NamedTextColor.YELLOW));
                player.sendMessage(Component.text("✓ Spawner added to room template!")
                    .color(NamedTextColor.GREEN));
                player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);
            }
        });
    }
}
